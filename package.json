{"name": "remix-ferrypro", "version": "1.0.0", "description": "FerryPros platform connecting aircraft owners with trusted pilots", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix vite:build", "dev": "remix vite:dev", "start": "remix-serve ./build/server/index.js", "typecheck": "tsc", "migrate": "node --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' migrations/run-migrations.ts", "test-db": "node --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' test-db.ts", "test-db-simple": "node --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' test-db-simple.ts", "test-db-pool": "node --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' test-db-pool.ts", "test-cache": "node --import 'data:text/javascript,import { register } from \"node:module\"; import { pathToFileURL } from \"node:url\"; register(\"ts-node/esm\", pathToFileURL(\"./\"));' test-cache.ts"}, "dependencies": {"@remix-run/css-bundle": "^2.0.0", "@remix-run/node": "^2.0.0", "@remix-run/react": "^2.0.0", "@remix-run/serve": "^2.0.0", "bcryptjs": "^2.4.3", "date-fns": "^4.1.0", "isbot": "^3.6.8", "kysely": "^0.26.3", "otplib": "^12.0.1", "pg": "^8.11.3", "qrcode": "^1.5.4", "qrcode.react": "^3.1.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-swipeable": "^7.0.2", "tailwindcss": "^3.3.3", "uuid": "^9.0.1", "web-push": "^3.6.7", "zod": "^3.22.2"}, "devDependencies": {"@remix-run/dev": "^2.0.0", "@types/bcryptjs": "^2.4.3", "@types/pg": "^8.15.2", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "@types/uuid": "^9.0.7", "@typescript-eslint/eslint-plugin": "^6.7.4", "eslint": "^8.38.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.2", "ts-node": "^10.9.1", "typescript": "^5.1.6", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=18.0.0"}}