import { createBusiness, addEmployeeToBusiness, createRole, assignRoleToUser } from './app/utils/business.server.ts';
import { requestJobApproval, processJobApproval } from './app/utils/jobApproval.server.ts';
import { registerServiceProvider, addServiceProviderAddon } from './app/utils/serviceProvider.server.ts';
import { createBidInquiry, processBidInquiry } from './app/utils/bidInquiry.server.ts';
import { sendMessage, startDisputeConversation, resolveDispute } from './app/utils/messaging.server.ts';
import { createExpense, updateExpenseStatus, uploadReceiptForExpense } from './app/utils/expenseTracking.server.ts';
import { sendEmailNotification } from './app/utils/emailNotifications.server.ts';

// Mock user IDs for testing
const ownerId = 1;
const employeeId = 2;
const pilotId = 3;
const serviceProviderId = 4;
const jobId = 1;

async function testFeatures() {
  console.log('Testing new features...');

  try {
    // Test business registration
    console.log('\n1. Testing business registration...');
    const business = await createBusiness(ownerId, 'Test Business', 'A test business');
    console.log('Business created:', business);

    // Test employee management
    console.log('\n2. Testing employee management...');
    const employee = await addEmployeeToBusiness(business.id, '<EMAIL>', 'Test Employee', 'password123', false);
    console.log('Employee added:', employee);

    // Test role management
    console.log('\n3. Testing role management...');
    const role = await createRole(business.id, 'Job Creator', [1, 2, 3]); // Assuming permission IDs
    console.log('Role created:', role);
    const assigned = await assignRoleToUser(employeeId, role.id);
    console.log('Role assigned:', assigned);

    // Test job approval workflow
    console.log('\n4. Testing job approval workflow...');
    const jobApproval = await requestJobApproval(jobId, employeeId, 'Please approve this job');
    console.log('Job approval requested:', jobApproval);
    const processedApproval = await processJobApproval(jobApproval.id, ownerId, true, 'Approved');
    console.log('Job approval processed:', processedApproval);

    // Test service provider registration
    console.log('\n5. Testing service provider registration...');
    const serviceProvider = await registerServiceProvider(serviceProviderId, 'Aircraft Ferry Service');
    console.log('Service provider registered:', serviceProvider);

    // Test service provider addons
    console.log('\n6. Testing service provider addons...');
    const addon = await addServiceProviderAddon(serviceProviderId, 'boosted', new Date(Date.now() + 30 * 24 * 60 * 60 * 1000));
    console.log('Addon added:', addon);

    // Test bid inquiry system
    console.log('\n7. Testing bid inquiry system...');
    const bidInquiry = await createBidInquiry(jobId, pilotId, 'I would like to bid on this job');
    console.log('Bid inquiry created:', bidInquiry);
    const processedInquiry = await processBidInquiry(bidInquiry.id, ownerId, true, 'Approved');
    console.log('Bid inquiry processed:', processedInquiry);

    // Test messaging system
    console.log('\n8. Testing messaging system...');
    const message = await sendMessage(pilotId, ownerId, 'Hello, I have a question about the job', jobId);
    console.log('Message sent:', message);

    // Test dispute handling
    console.log('\n9. Testing dispute handling...');
    const dispute = await startDisputeConversation(pilotId, ownerId, jobId, 'There is an issue with payment');
    console.log('Dispute started:', dispute);
    const resolved = await resolveDispute(1, 'The payment has been processed', ownerId);
    console.log('Dispute resolved:', resolved);

    // Test expense tracking
    console.log('\n10. Testing expense tracking...');
    const expense = await createExpense(jobId, pilotId, 150.75, 'Fuel expense');
    console.log('Expense created:', expense);
    const receipt = await uploadReceiptForExpense(expense.id, 'https://example.com/receipt.jpg', pilotId);
    console.log('Receipt uploaded:', receipt);
    const approvedExpense = await updateExpenseStatus(expense.id, 'approved', ownerId);
    console.log('Expense approved:', approvedExpense);

    // Test email notifications
    console.log('\n11. Testing email notifications...');
    const emailSent = await sendEmailNotification({
      to: '<EMAIL>',
      subject: 'Test Notification',
      body: '<h1>Test</h1><p>This is a test notification</p>'
    });
    console.log('Email notification sent:', emailSent);

    console.log('\nAll tests completed successfully!');
  } catch (error) {
    console.error('Error during testing:', error);
  }
}

testFeatures();
