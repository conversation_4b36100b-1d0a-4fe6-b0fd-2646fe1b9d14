import { useState, useEffect } from "react";
import { isOnline, setupOfflineListeners } from "../utils/offline.client";

interface OfflineNotificationProps {
  className?: string;
}

export default function OfflineNotification({ className = "" }: OfflineNotificationProps) {
  const [offline, setOffline] = useState(false);
  const [visible, setVisible] = useState(false);

  // Initialize offline state and set up listeners
  useEffect(() => {
    // Check initial state
    setOffline(!isOnline());

    // Set up listeners for online/offline events
    const cleanup = setupOfflineListeners(
      // onOffline
      () => {
        setOffline(true);
        setVisible(true);
      },
      // onOnline
      () => {
        setOffline(false);
        // Keep the notification visible for a moment so the user can see we're back online
        setTimeout(() => {
          setVisible(false);
        }, 3000);
      }
    );

    // If we're offline on mount, show the notification
    if (!isOnline()) {
      setVisible(true);
    }

    return cleanup;
  }, []);

  // If we're online and the notification isn't visible, don't render anything
  if (!offline && !visible) {
    return null;
  }

  return (
    <div 
      className={`fixed bottom-4 left-4 right-4 md:left-auto md:right-4 md:w-80 p-4 rounded-lg shadow-card z-50 transition-all duration-300 transform ${
        visible ? 'translate-y-0 opacity-100' : 'translate-y-10 opacity-0'
      } ${offline ? 'bg-red-900 border border-red-700' : 'bg-green-900 border border-green-700'} ${className}`}
    >
      <div className="flex items-start">
        <div className="flex-shrink-0">
          {offline ? (
            <svg className="h-5 w-5 text-red-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
              />
            </svg>
          ) : (
            <svg className="h-5 w-5 text-green-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path 
                strokeLinecap="round" 
                strokeLinejoin="round" 
                strokeWidth={2} 
                d="M5 13l4 4L19 7" 
              />
            </svg>
          )}
        </div>
        <div className="ml-3 w-0 flex-1 pt-0.5">
          <p className="text-sm font-medium text-gray-200">
            {offline ? "You're offline" : "You're back online"}
          </p>
          <p className="mt-1 text-sm text-gray-400">
            {offline 
              ? "Some features may be limited until you reconnect." 
              : "All features are now available."}
          </p>
        </div>
        <div className="ml-4 flex-shrink-0 flex">
          <button
            className="bg-transparent rounded-md inline-flex text-gray-400 hover:text-gray-300 transition-colors duration-200 focus:outline-none"
            onClick={() => setVisible(false)}
          >
            <span className="sr-only">Close</span>
            <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                clipRule="evenodd"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
}
