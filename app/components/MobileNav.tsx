import { useState } from "react";
import { Link, Form } from "@remix-run/react";
import AdminMenu from "./AdminMenu";
import JobsMenu from "./JobsMenu";
import MarineMenu from "./MarineMenu";
import AccountMenu from "./AccountMenu";

interface MobileNavProps {
  user: {
    id: string;
    email: string;
    role: string;
  } | null;
}

export default function MobileNav({ user }: MobileNavProps) {
  const [isOpen, setIsOpen] = useState(false);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  return (
    <div className="md:hidden">
      {/* Hamburger button */}
      <button 
        onClick={toggleMenu}
        className="flex items-center p-2 rounded text-white hover:bg-primary-700 transition-colors duration-200"
        aria-label="Toggle menu"
      >
        <svg 
          className="w-6 h-6" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          {isOpen ? (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          ) : (
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
          )}
        </svg>
      </button>

      {/* Mobile menu */}
      {isOpen && (
        <div className="absolute top-16 left-0 right-0 bg-primary-800 shadow-lg z-50">
          <ul className="flex flex-col p-6 space-y-4">
            {user ? (
              <>
                <li><Link to="/dashboard" className="block font-medium hover:text-primary-200 transition-colors duration-200" onClick={toggleMenu}>Dashboard</Link></li>
                <li>
                  <JobsMenu isMobile={true} onLinkClick={toggleMenu} userRole={user.role} />
                </li>
                <li>
                  <MarineMenu isMobile={true} onLinkClick={toggleMenu} userRole={user.role} />
                </li>
                <li>
                  <AccountMenu isMobile={true} onLinkClick={toggleMenu} userRole={user.role} />
                </li>
                {user.role === "admin" && (
                  <li>
                    <AdminMenu isMobile={true} onLinkClick={toggleMenu} />
                  </li>
                )}
              </>
            ) : (
              <>
                <li><Link to="/" className="block font-medium hover:text-primary-200 transition-colors duration-200" onClick={toggleMenu}>Home</Link></li>
                <li><Link to="/about" className="block font-medium hover:text-primary-200 transition-colors duration-200" onClick={toggleMenu}>About</Link></li>
                <li><Link to="/contact" className="block font-medium hover:text-primary-200 transition-colors duration-200" onClick={toggleMenu}>Contact</Link></li>
                <li><Link to="/login" className="block font-medium hover:text-primary-200 transition-colors duration-200" onClick={toggleMenu}>Login</Link></li>
                <li><Link to="/register" className="block btn-primary w-full text-center mt-2" onClick={toggleMenu}>Register</Link></li>
              </>
            )}
          </ul>
        </div>
      )}
    </div>
  );
}
