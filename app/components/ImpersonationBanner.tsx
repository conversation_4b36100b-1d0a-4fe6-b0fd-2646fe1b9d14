import { Form } from "@remix-run/react";

interface ImpersonationBannerProps {
  actualRole: string;
  impersonatedRole: string;
}

export default function ImpersonationBanner({ actualRole, impersonatedRole }: ImpersonationBannerProps) {
  return (
    <div className="bg-yellow-500 text-black py-2 px-4">
      <div className="container-custom flex justify-between items-center">
        <div>
          <span className="font-medium">Impersonation Mode:</span> You are viewing the system as a <span className="font-bold">{impersonatedRole}</span> (Your actual role: {actualRole})
        </div>
        <Form method="post" action="/admin/end-impersonation">
          <button 
            type="submit"
            className="bg-white text-yellow-700 px-3 py-1 rounded-md text-sm font-medium hover:bg-yellow-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
          >
            Exit Impersonation
          </button>
        </Form>
      </div>
    </div>
  );
}