import { useState, useRef, useEffect } from "react";
import { Link } from "@remix-run/react";

interface MarineMenuProps {
  isMobile?: boolean;
  onLinkClick?: () => void;
  userRole?: string;
}

export default function MarineMenu({ isMobile = false, onLinkClick, userRole }: MarineMenuProps) {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);
  const closeTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const handleMouseEnter = () => {
    if (!isMobile) {
      // Clear any pending close timeout
      if (closeTimeoutRef.current) {
        clearTimeout(closeTimeoutRef.current);
        closeTimeoutRef.current = null;
      }
      setIsOpen(true);
    }
  };

  const handleMouseLeave = () => {
    if (!isMobile) {
      // Add a small delay before closing the menu
      closeTimeoutRef.current = setTimeout(() => {
        setIsOpen(false);
        closeTimeoutRef.current = null;
      }, 300); // 300ms delay gives enough time to move to submenu
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    if (isMobile) return;

    function handleClickOutside(event: MouseEvent) {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobile]);

  const isOwnerOrAdmin = userRole === "admin";
  const isPilotOrAdmin = userRole === "admin";

  return (
    <div 
      ref={menuRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="relative"
    >
      <button 
        onClick={toggleMenu}
        className={`flex items-center ${isMobile ? "w-full text-left" : ""} hover:text-primary-200`}
      >
        Marine
        <svg 
          className="w-4 h-4 ml-1" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24" 
          xmlns="http://www.w3.org/2000/svg"
        >
          <path 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            strokeWidth={2} 
            d={isOpen ? "M5 15l7-7 7 7" : "M19 9l-7 7-7-7"} 
          />
        </svg>
      </button>
      {isOpen && (
        <div className={
          isMobile 
            ? "pl-4 border-l border-primary-500 ml-2 mt-2 space-y-3" 
            : "absolute top-full left-0 bg-primary-700 rounded shadow-lg py-2 mt-1 w-48 z-50"
        }>
          <Link 
            to="/marine" 
            className={isMobile ? "block hover:text-primary-200" : "block px-4 py-2 hover:bg-primary-800"}
            onClick={onLinkClick}
          >
            Marine Home
          </Link>
          <Link 
            to="/marine/jobs" 
            className={isMobile ? "block hover:text-primary-200" : "block px-4 py-2 hover:bg-primary-800"}
            onClick={onLinkClick}
          >
            Browse Marine Jobs
          </Link>

          {isOwnerOrAdmin && (
            <>
              <Link 
                to="/marine/jobs/new" 
                className={isMobile ? "block hover:text-primary-200" : "block px-4 py-2 hover:bg-primary-800"}
                onClick={onLinkClick}
              >
                Post Marine Job
              </Link>
            </>
          )}

          {isPilotOrAdmin && (
            <>
              <Link 
                to="/marine/jobs/applications" 
                className={isMobile ? "block hover:text-primary-200" : "block px-4 py-2 hover:bg-primary-800"}
                onClick={onLinkClick}
              >
                My Marine Applications
              </Link>
            </>
          )}
        </div>
      )}
    </div>
  );
}
