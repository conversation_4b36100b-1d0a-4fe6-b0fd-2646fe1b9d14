import { cssBundleHref } from "@remix-run/css-bundle";
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/node";
import {
  Links,
  LiveReload,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  Link,
  Form,
  useLocation,
} from "@remix-run/react";
import { json } from "@remix-run/node";
import { useState, useEffect } from "react";
import "./tailwind.css";
import { getUser } from "~/utils/session.server";
import MobileNav from "~/components/MobileNav";
import AdminMenu from "~/components/AdminMenu";
import JobsMenu from "~/components/JobsMenu";
import MarineMenu from "~/components/MarineMenu";
import AccountMenu from "~/components/AccountMenu";
import NotificationListener, { NotificationToast } from "~/components/NotificationListener";
import OfflineNotification from "~/components/OfflineNotification";
import ImpersonationBanner from "~/components/ImpersonationBanner";
import { registerServiceWorker } from "./utils/offline.client";

export const links: LinksFunction = () => [
  ...(cssBundleHref ? [{ rel: "stylesheet", href: cssBundleHref }] : []),
];

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getUser(request);
  return json({
    user,
  });
}

export default function App() {
  const { user } = useLoaderData<typeof loader>();
  const location = useLocation();
  const [unreadCount, setUnreadCount] = useState(0);
  const [currentNotification, setCurrentNotification] = useState(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [swRegistration, setSwRegistration] = useState(null);

  // Register service worker for offline capability
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Register service worker
      registerServiceWorker();

      // Listen for service worker update available event
      const handleUpdateAvailable = (event) => {
        setUpdateAvailable(true);
        setSwRegistration(event.detail);
      };

      window.addEventListener('serviceWorkerUpdateAvailable', handleUpdateAvailable);

      return () => {
        window.removeEventListener('serviceWorkerUpdateAvailable', handleUpdateAvailable);
      };
    }
  }, []);

  // Reset current notification when route changes
  useEffect(() => {
    setCurrentNotification(null);
  }, [location.pathname]);

  // Handle new notifications
  const handleNewNotification = (notification) => {
    setCurrentNotification(notification);
  };

  // Handle notification close
  const handleCloseNotification = () => {
    setCurrentNotification(null);
  };

  // Handle service worker update
  const handleUpdate = () => {
    if (swRegistration && swRegistration.waiting) {
      swRegistration.waiting.postMessage({ type: 'SKIP_WAITING' });
    }
    setUpdateAvailable(false);
  };

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <Meta />
        <Links />
      </head>
      <body>
        {user?.isImpersonating && (
          <ImpersonationBanner 
            actualRole={user.actualRole} 
            impersonatedRole={user.role} 
          />
        )}
        <header className="bg-gray-800 text-white py-4 shadow-md">
          <div className="container-custom flex justify-between items-center">
            <h1 className="text-2xl font-display font-bold">
              <Link to="/" className="hover:text-primary-200 transition-colors duration-200 flex items-center">
                <svg className="h-6 w-6 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
                  <path
                      d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z"
                  />
                </svg>
                FerryPros
              </Link>
            </h1>

            {/* Mobile Navigation */}
            <MobileNav user={user}/>

            {/* Desktop Navigation */}
            <nav className="hidden md:block">
              <ul className="flex space-x-6 items-center">
                {user ? (
                    <>
                      <li><Link to="/dashboard"
                                className="hover:text-primary-200 transition-colors duration-200 font-medium">Dashboard</Link>
                      </li>
                      <li className="relative">
                      <JobsMenu userRole={user.role} />
                    </li>
                    <li className="relative">
                      <MarineMenu userRole={user.role} />
                    </li>

                    {/* Messages */}
                    <li>
                      <Link 
                        to="/messages/inbox" 
                        className="relative hover:text-primary-200 transition-colors duration-200 flex items-center"
                        aria-label="Messages"
                      >
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      </Link>
                    </li>

                    {/* Notifications */}
                    <li>
                      <Link 
                        to="/notifications" 
                        className="relative hover:text-primary-200 transition-colors duration-200 flex items-center"
                        aria-label="Notifications"
                      >
                        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                        {unreadCount > 0 && (
                          <span className="absolute -top-1 -right-1 bg-primary-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center shadow-sm">
                            {unreadCount > 9 ? '9+' : unreadCount}
                          </span>
                        )}
                      </Link>
                    </li>

                    <li className="relative">
                      <AccountMenu userRole={user.role} />
                    </li>
                    {user.role === "admin" && (
                      <li className="relative">
                        <AdminMenu />
                      </li>
                    )}
                  </>
                ) : (
                  <>
                    <li><Link to="/" className="hover:text-primary-200 transition-colors duration-200 font-medium">Home</Link></li>
                    <li><Link to="/about" className="hover:text-primary-200 transition-colors duration-200 font-medium">About</Link></li>
                    <li><Link to="/contact" className="hover:text-primary-200 transition-colors duration-200 font-medium">Contact</Link></li>
                    <li><Link to="/login" className="hover:text-primary-200 transition-colors duration-200 font-medium">Login</Link></li>
                    <li><Link to="/register" className="btn-primary ml-2">Register</Link></li>
                  </>
                )}
              </ul>
            </nav>
          </div>
        </header>
        <main className="min-h-screen">
          <Outlet />
        </main>
        <footer className="bg-gray-800 text-white py-12">
          <div className="container-custom">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              <div>
                <h3 className="text-xl mb-4 text-white flex items-center">
                  <svg className="h-6 w-6 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M17.8 19.2 16 11l3.5-3.5C21 6 21.5 4 21 3c-1-.5-3 0-4.5 1.5L13 8 4.8 6.2c-.5-.1-.9.1-1.1.5l-.3.5c-.2.5-.1 1 .3 1.3L9 12l-2 3H4l-1 1 3 2 2 3 1-1v-3l3-2 3.5 5.3c.3.4.8.5 1.3.3l.5-.2c.4-.3.6-.7.5-1.2z"
                    />
                  </svg>
                  FerryPros
                </h3>
                <p className="text-gray-300 mb-4">
                  Connecting aircraft owners, brokers, and Ferry Pilot Agencies with trusted pilots through secure escrow and real-time tracking.
                </p>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Services</h3>
                <ul className="space-y-2 text-gray-300">
                  <li><Link to="/services/aircraft-ferry" className="hover:text-primary-300 transition-colors duration-200">Aircraft Ferry</Link></li>
                  <li><Link to="/services/pilot-verification" className="hover:text-primary-300 transition-colors duration-200">Pilot Verification</Link></li>
                  <li><Link to="/services/secure-escrow" className="hover:text-primary-300 transition-colors duration-200">Secure Escrow</Link></li>
                  <li><Link to="/services/flight-tracking" className="hover:text-primary-300 transition-colors duration-200">Flight Tracking</Link></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Company</h3>
                <ul className="space-y-2 text-gray-300">
                  <li><Link to="/about" className="hover:text-primary-300 transition-colors duration-200">About Us</Link></li>
                  <li><Link to="/contact" className="hover:text-primary-300 transition-colors duration-200">Contact</Link></li>
                </ul>
              </div>
              <div>
                <h3 className="text-lg font-medium mb-4">Legal</h3>
                <ul className="space-y-2 text-gray-300">
                  <li><Link to="/privacy-policy" className="hover:text-primary-300 transition-colors duration-200">Privacy Policy</Link></li>
                  <li><Link to="/terms-of-service" className="hover:text-primary-300 transition-colors duration-200">Terms of Service</Link></li>
                  <li><Link to="/cookie-policy" className="hover:text-primary-300 transition-colors duration-200">Cookie Policy</Link></li>
                </ul>
              </div>
            </div>
            <div className="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
              <p>&copy; {new Date().getFullYear()} FerryPros - All rights reserved.</p>
            </div>
          </div>
        </footer>

        {/* Real-time notifications */}
        {user && (
          <NotificationListener 
            userId={user.id} 
            onNewNotification={handleNewNotification}
            onUnreadCountChange={setUnreadCount}
          />
        )}

        {/* Notification toast */}
        <NotificationToast 
          notification={currentNotification} 
          onClose={handleCloseNotification} 
        />

        {/* Offline notification */}
        <OfflineNotification />

        {/* Update notification */}
        {updateAvailable && (
          <div className="fixed bottom-4 left-4 right-4 md:left-auto md:w-80 p-4 bg-gray-800 border border-gray-700 rounded-lg shadow-card z-50">
            <div className="flex items-start">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-primary-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path 
                    strokeLinecap="round" 
                    strokeLinejoin="round" 
                    strokeWidth={2} 
                    d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                  />
                </svg>
              </div>
              <div className="ml-3 w-0 flex-1 pt-0.5">
                <p className="text-sm font-medium text-gray-200">
                  Update available
                </p>
                <p className="mt-1 text-sm text-gray-400">
                  A new version of the app is available.
                </p>
                <div className="mt-2">
                  <button
                    onClick={handleUpdate}
                    className="text-sm text-primary-300 font-medium hover:text-primary-200 transition-colors duration-200"
                  >
                    Update now
                  </button>
                </div>
              </div>
              <div className="ml-4 flex-shrink-0 flex">
                <button
                  className="bg-transparent rounded-md inline-flex text-gray-400 hover:text-gray-300 transition-colors duration-200 focus:outline-none"
                  onClick={() => setUpdateAvailable(false)}
                >
                  <span className="sr-only">Close</span>
                  <svg className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clipRule="evenodd"
                    />
                  </svg>
                </button>
              </div>
            </div>
          </div>
        )}

        <ScrollRestoration />
        <Scripts />
        <LiveReload />
      </body>
    </html>
  );
}
