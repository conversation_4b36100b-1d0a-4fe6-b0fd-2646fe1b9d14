import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState, useRef, useEffect } from "react";
import { getShortUrlData, markShortUrlAsUsed } from "~/utils/shortUrl.server";
import { uploadIdDocument, base64ToBuffer } from "~/utils/idVerification.server";
import { processDocumentWithOcr, saveOcrExtractedData } from "~/utils/documentOcr.server";
import { IdDocumentType } from "~/types/verification";

export const loader = async ({ request, params }) => {
  const { code } = params;
  
  if (!code) {
    return redirect("/");
  }

  // Get short URL data
  const shortUrlData = await getShortUrlData(code);
  
  // If the short URL doesn't exist or is expired, redirect to home
  if (!shortUrlData) {
    return redirect("/");
  }

  return json({
    shortUrlData
  });
};

export const action = async ({ request, params }) => {
  const { code } = params;
  
  if (!code) {
    return json({ error: "Invalid URL" }, { status: 400 });
  }

  // Get short URL data
  const shortUrlData = await getShortUrlData(code);
  
  // If the short URL doesn't exist or is expired, return error
  if (!shortUrlData) {
    return json({ error: "This URL has expired or is invalid" }, { status: 400 });
  }

  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "upload-document") {
    const fileData = formData.get("fileData"); // Base64 encoded file
    const fileName = formData.get("fileName");
    const fileType = formData.get("fileType");
    const notes = formData.get("notes");

    if (!fileData || !fileName || !fileType) {
      return json({ error: "File data, name, and type are required" }, { status: 400 });
    }

    try {
      // Get IP and user agent for tracking
      const ipAddress = request.headers.get('x-forwarded-for') || 
                        request.headers.get('x-real-ip') || 
                        '127.0.0.1';
      const userAgent = request.headers.get('user-agent') || 'Unknown';

      // Mark the short URL as used
      await markShortUrlAsUsed(code, ipAddress.toString(), userAgent.toString());

      // Convert base64 to buffer
      const buffer = base64ToBuffer(fileData.toString());

      // Process the document with OCR
      const ocrResult = await processDocumentWithOcr(buffer, fileType.toString(), shortUrlData.documentType);

      // Upload the document
      const uploadResult = await uploadIdDocument({
        userId: shortUrlData.userId,
        documentType: shortUrlData.documentType,
        fileData: buffer,
        fileName: fileName.toString(),
        fileType: fileType.toString(),
        notes: notes ? notes.toString() : undefined,
        metadata: {
          captureMethod: "mobile",
          shortUrlId: shortUrlData.id,
          ocrConfidence: ocrResult.confidence
        }
      }, ipAddress.toString(), userAgent.toString());

      // If upload was successful and we have OCR data, save it
      if (uploadResult.success && uploadResult.documentId && ocrResult.confidence > 0) {
        await saveOcrExtractedData(uploadResult.documentId, ocrResult);
      }

      return json({ 
        success: true, 
        message: "Document uploaded successfully",
        documentType: shortUrlData.documentType,
        ocrData: ocrResult.confidence > 0.5 ? {
          documentType: ocrResult.documentType,
          fullName: ocrResult.fullName,
          documentNumber: ocrResult.documentNumber,
          expiryDate: ocrResult.expiryDate,
          confidence: ocrResult.confidence
        } : null
      });
    } catch (error) {
      console.error("Error uploading document:", error);
      return json({ error: "Failed to upload document" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};

export default function CaptureDocument() {
  const { shortUrlData } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [fileBase64, setFileBase64] = useState("");
  const [fileName, setFileName] = useState("");
  const [fileType, setFileType] = useState("");
  const [notes, setNotes] = useState("");
  const [cameraError, setCameraError] = useState("");
  const [showCamera, setShowCamera] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const fileInputRef = useRef(null);

  // Check if device is mobile
  useEffect(() => {
    const checkMobile = () => {
      const userAgent = navigator.userAgent || navigator.vendor || window.opera;
      setIsMobile(/android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(userAgent.toLowerCase()));
    };
    checkMobile();
  }, []);

  // Initialize camera when showCamera changes
  useEffect(() => {
    if (showCamera && videoRef.current) {
      initCamera();
    } else if (!showCamera && videoRef.current) {
      // Stop the camera stream when not showing camera
      const stream = videoRef.current.srcObject;
      if (stream) {
        const tracks = stream.getTracks();
        tracks.forEach(track => track.stop());
        videoRef.current.srcObject = null;
      }
    }
  }, [showCamera]);

  // Initialize camera
  const initCamera = async () => {
    try {
      setCameraError("");
      const constraints = {
        video: {
          facingMode: "environment", // Use the back camera on mobile devices
          width: { ideal: 1920 },
          height: { ideal: 1080 }
        }
      };
      
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      if (videoRef.current) {
        videoRef.current.srcObject = stream;
      }
    } catch (err) {
      console.error("Error accessing camera:", err);
      setCameraError("Could not access camera. Please ensure you've granted camera permissions.");
    }
  };

  // Capture photo from camera
  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      
      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;
      
      // Draw the video frame to the canvas
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
      
      // Convert canvas to base64 image
      const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
      setFileBase64(dataUrl);
      setFileName(`capture_${new Date().toISOString()}.jpg`);
      setFileType('image/jpeg');
      
      // Hide camera after capture
      setShowCamera(false);
    }
  };

  // Handle file selection
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setFileName(file.name);
    setFileType(file.type);

    const reader = new FileReader();
    reader.onload = (e) => {
      setFileBase64(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  // Get document type display name
  const getDocumentTypeDisplay = (type) => {
    switch (type) {
      case IdDocumentType.PASSPORT:
        return "Passport";
      case IdDocumentType.DRIVERS_LICENSE:
        return "Driver's License";
      case IdDocumentType.GOVERNMENT_ID:
        return "Government ID";
      case IdDocumentType.PILOT_LICENSE:
        return "Pilot License";
      default:
        return "Document";
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-md">
      <div className="text-center mb-6">
        <h1 className="text-2xl font-bold">FerryPros Document Capture</h1>
        <p className="text-gray-600">
          Capture your {getDocumentTypeDisplay(shortUrlData.documentType)}
        </p>
      </div>

      {actionData?.error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{actionData.error}</p>
        </div>
      )}

      {actionData?.success ? (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4 text-center">
          <h2 className="font-bold text-xl mb-2">✅ Document Uploaded Successfully!</h2>
          <p className="mb-4">Your {getDocumentTypeDisplay(shortUrlData.documentType)} has been uploaded and will be reviewed.</p>
          
          {actionData.ocrData && (
            <div className="bg-white p-4 rounded-lg shadow-sm mt-4 text-left">
              <h3 className="font-semibold mb-2">Document Information (Extracted)</h3>
              <p className="text-sm text-gray-600 mb-1">
                <span className="font-medium">Document Type:</span> {getDocumentTypeDisplay(actionData.ocrData.documentType)}
              </p>
              {actionData.ocrData.fullName && (
                <p className="text-sm text-gray-600 mb-1">
                  <span className="font-medium">Name:</span> {actionData.ocrData.fullName}
                </p>
              )}
              {actionData.ocrData.documentNumber && (
                <p className="text-sm text-gray-600 mb-1">
                  <span className="font-medium">Document Number:</span> {actionData.ocrData.documentNumber}
                </p>
              )}
              {actionData.ocrData.expiryDate && (
                <p className="text-sm text-gray-600 mb-1">
                  <span className="font-medium">Expiry Date:</span> {new Date(actionData.ocrData.expiryDate).toLocaleDateString()}
                </p>
              )}
              <p className="text-xs text-gray-500 mt-2">
                Confidence: {Math.round(actionData.ocrData.confidence * 100)}%
              </p>
            </div>
          )}
          
          <div className="mt-6">
            <p className="text-sm text-gray-600">You can now close this window.</p>
          </div>
        </div>
      ) : (
        <>
          {showCamera ? (
            <div className="mb-6">
              <div className="relative">
                <video 
                  ref={videoRef} 
                  autoPlay 
                  playsInline 
                  className="w-full rounded-lg border border-gray-300"
                />
                <button
                  type="button"
                  onClick={capturePhoto}
                  className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-white text-blue-600 py-2 px-4 rounded-full shadow-lg hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                >
                  Capture
                </button>
              </div>
              <canvas ref={canvasRef} className="hidden" />
              
              {cameraError && (
                <div className="mt-2 text-red-600 text-sm">
                  {cameraError}
                </div>
              )}
              
              <button
                type="button"
                onClick={() => setShowCamera(false)}
                className="mt-4 text-blue-600 hover:text-blue-800 text-sm"
              >
                Cancel
              </button>
            </div>
          ) : (
            <div className="bg-white shadow-md rounded-lg p-6 mb-6">
              <h2 className="text-xl font-semibold mb-4">Upload Your Document</h2>
              
              <div className="mb-6">
                <p className="text-gray-700 mb-2">
                  Please upload a clear photo of your {getDocumentTypeDisplay(shortUrlData.documentType)}.
                </p>
                <ul className="list-disc list-inside text-sm text-gray-600 mb-4">
                  <li>Make sure all text is clearly visible</li>
                  <li>Ensure the entire document is in the frame</li>
                  <li>Avoid glare or shadows on the document</li>
                </ul>
              </div>
              
              {fileBase64 ? (
                <div className="mb-4">
                  <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
                  <div className="relative">
                    <img 
                      src={fileBase64} 
                      alt="Document preview" 
                      className="w-full rounded-lg border border-gray-300"
                    />
                    <button
                      type="button"
                      onClick={() => setFileBase64("")}
                      className="absolute top-2 right-2 bg-white text-gray-600 rounded-full p-1 shadow-md hover:bg-gray-100"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                      </svg>
                    </button>
                  </div>
                </div>
              ) : (
                <div className="mb-4">
                  <div className="flex flex-col space-y-4">
                    {isMobile && (
                      <button
                        type="button"
                        onClick={() => setShowCamera(true)}
                        className="w-full bg-blue-600 text-white py-3 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                      >
                        Take Photo with Camera
                      </button>
                    )}
                    
                    <div>
                      <input
                        type="file"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        accept="image/*"
                        className="hidden"
                      />
                      <button
                        type="button"
                        onClick={() => fileInputRef.current?.click()}
                        className={`w-full ${isMobile ? 'bg-gray-200 text-gray-800' : 'bg-blue-600 text-white'} py-3 px-4 rounded-md hover:bg-opacity-90 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2`}
                      >
                        {isMobile ? 'Choose from Gallery' : 'Select Document Image'}
                      </button>
                    </div>
                  </div>
                </div>
              )}
              
              {fileBase64 && (
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="upload-document" />
                  <input type="hidden" name="fileData" value={fileBase64} />
                  <input type="hidden" name="fileName" value={fileName} />
                  <input type="hidden" name="fileType" value={fileType} />
                  
                  <div>
                    <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                      Notes (Optional)
                    </label>
                    <textarea
                      id="notes"
                      name="notes"
                      value={notes}
                      onChange={(e) => setNotes(e.target.value)}
                      rows={2}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Any additional information about this document"
                    />
                  </div>
                  
                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-green-600 text-white py-3 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {isSubmitting ? "Uploading..." : "Upload Document"}
                  </button>
                </Form>
              )}
            </div>
          )}
          
          <div className="text-center text-sm text-gray-600">
            <p>This link will expire on {new Date(shortUrlData.expiresAt).toLocaleString()}</p>
          </div>
        </>
      )}
    </div>
  );
}