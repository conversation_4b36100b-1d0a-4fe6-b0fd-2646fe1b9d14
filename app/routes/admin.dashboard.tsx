import { json, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { getUser } from "~/utils/session.server";
import { getAllUsers, isUserAdmin } from "~/utils/users.server";
import { db } from "~/utils/db.server";
import { sql } from "kysely";

export const meta: MetaFunction = () => {
  return [{ title: "Admin Dashboard | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Get the current user
  const user = await getUser(request);

  // If not logged in, redirect to login
  if (!user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // Check if user is an admin
  const isAdmin = await isUserAdmin(user.id);
  if (!isAdmin) {
    throw new Response("Forbidden", { status: 403 });
  }

  // Get all users for the admin dashboard
  const users = await getAllUsers();

  // Get job statistics
  const jobStats = await db
    .selectFrom('jobs')
    .select([
      db.fn.count('id').as('totalJobs'),
      db.fn.count(sql`CASE WHEN status = 'completed' THEN 1 ELSE NULL END`).as('completedJobs'),
      db.fn.count(sql`CASE WHEN status = 'in_progress' THEN 1 ELSE NULL END`).as('activeJobs'),
      db.fn.count(sql`CASE WHEN status = 'pending' THEN 1 ELSE NULL END`).as('pendingJobs')
    ])
    .executeTakeFirst();

  // Get payment statistics
  const paymentStats = await db
    .selectFrom('payments')
    .select([
      db.fn.count('id').as('totalTransactions'),
      db.fn.sum('amount').as('totalAmount')
    ])
    .executeTakeFirst();

  return json({
    users,
    jobStats: {
      total: Number(jobStats?.totalJobs || 0),
      completed: Number(jobStats?.completedJobs || 0),
      active: Number(jobStats?.activeJobs || 0),
      pending: Number(jobStats?.pendingJobs || 0)
    },
    paymentStats: {
      transactions: Number(paymentStats?.totalTransactions || 0),
      amount: Number(paymentStats?.totalAmount || 0)
    }
  });
}

export default function AdminDashboard() {
  const { users, jobStats, paymentStats } = useLoaderData<typeof loader>();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-8">Admin Dashboard</h1>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">User Statistics</h2>
          <p className="text-gray-700">Total Users: <span className="font-bold">{users.length}</span></p>
          <p className="text-gray-700">Pilots: <span className="font-bold">{users.filter(u => u.role === 'pilot').length}</span></p>
          <p className="text-gray-700">Owners: <span className="font-bold">{users.filter(u => u.role === 'owner').length}</span></p>
          <p className="text-gray-700">Admins: <span className="font-bold">{users.filter(u => u.role === 'admin').length}</span></p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Job Statistics</h2>
          <p className="text-gray-700">Total Jobs: <span className="font-bold">{jobStats.total}</span></p>
          <p className="text-gray-700">Completed: <span className="font-bold">{jobStats.completed}</span></p>
          <p className="text-gray-700">Active: <span className="font-bold">{jobStats.active}</span></p>
          <p className="text-gray-700">Pending: <span className="font-bold">{jobStats.pending}</span></p>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Payment Statistics</h2>
          <p className="text-gray-700">Total Transactions: <span className="font-bold">{paymentStats.transactions}</span></p>
          <p className="text-gray-700">Total Amount: <span className="font-bold">${(paymentStats.amount / 100).toFixed(2)}</span></p>
        </div>
      </div>

      {/* Admin Tools */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-8">
        <h2 className="text-xl font-semibold mb-4">Admin Tools</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Link 
            to="/admin/users" 
            className="bg-green-600 text-white py-2 px-4 rounded hover:bg-green-700 text-center"
          >
            User Management
          </Link>
          <Link 
            to="/admin/jobs" 
            className="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700 text-center"
          >
            Job Management
          </Link>
          <Link 
            to="/admin/subscriptions" 
            className="bg-purple-600 text-white py-2 px-4 rounded hover:bg-purple-700 text-center"
          >
            Subscription Management
          </Link>
          <Link 
            to="/admin/payments" 
            className="bg-yellow-600 text-white py-2 px-4 rounded hover:bg-yellow-700 text-center"
          >
            Payment & Invoice Management
          </Link>
          <Link 
            to="/admin/verification" 
            className="bg-orange-600 text-white py-2 px-4 rounded hover:bg-orange-700 text-center"
          >
            Verification Management
          </Link>
          <Link 
            to="/admin/reports" 
            className="bg-teal-600 text-white py-2 px-4 rounded hover:bg-teal-700 text-center"
          >
            Generate Reports
          </Link>
          <Link 
            to="/admin/performance" 
            className="bg-indigo-600 text-white py-2 px-4 rounded hover:bg-indigo-700 text-center"
          >
            Performance Metrics
          </Link>
          <Link 
            to="/admin/security-audit" 
            className="bg-red-600 text-white py-2 px-4 rounded hover:bg-red-700 text-center"
          >
            Security Audit Logs
          </Link>
          <Link 
            to="/admin/db-management" 
            className="bg-gray-600 text-white py-2 px-4 rounded hover:bg-gray-700 text-center"
          >
            Database Management
          </Link>
          <Link 
            to="/admin/migrations" 
            className="bg-indigo-500 text-white py-2 px-4 rounded hover:bg-indigo-600 text-center"
          >
            Database Migrations
          </Link>
        </div>
      </div>

      {/* Recent Users */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Recent Users</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white">
            <thead>
              <tr>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Name
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Email
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Role
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Joined
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {users.slice(0, 10).map((user) => (
                <tr key={user.id}>
                  <td className="py-2 px-4 border-b border-gray-200">{user.name}</td>
                  <td className="py-2 px-4 border-b border-gray-200">{user.email}</td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    <span className={`px-2 py-1 rounded text-xs ${
                      user.role === 'admin' ? 'bg-red-100 text-red-800' : 
                      user.role === 'pilot' ? 'bg-blue-100 text-blue-800' : 
                      'bg-green-100 text-green-800'
                    }`}>
                      {user.role}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    {new Date(user.createdAt).toLocaleDateString()}
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    <Link 
                      to={`/users/${user.id}`}
                      className="text-blue-600 hover:text-blue-900"
                    >
                      View
                    </Link>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {users.length > 10 && (
          <div className="mt-4 text-right">
            <Link 
              to="/admin/users"
              className="text-blue-600 hover:text-blue-900"
            >
              View All Users →
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
