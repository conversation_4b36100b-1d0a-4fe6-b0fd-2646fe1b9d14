import { json, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { requireAdmin } from "~/utils/session.server";
import {
  getPendingVerificationRequests,
  getDocumentsForVerificationRequest,
  updateVerificationRequestStatus,
  verifyDocument,
  VerificationStatus
} from "~/utils/pilotVerification.server";
import { getUserById } from "~/utils/users.server";
import { getComprehensiveVerificationStatus, isPilotVerifiedToFly } from "~/utils/credentialVerification.server";
import { verifyIdDocument } from "~/utils/idVerification.server";
import { CredentialType } from "~/types/verification";

export const loader = async ({ request }) => {
  const admin = await requireAdmin(request);

  // Get all pending verification requests
  const pendingRequests = await getPendingVerificationRequests();

  // Get documents for each request, user info, and comprehensive verification status
  const requestsWithDetails = await Promise.all(
    pendingRequests.map(async (request) => {
      const documents = await getDocumentsForVerificationRequest(request.id);
      const user = await getUserById(request.userId);

      // Get comprehensive verification status for this user
      const comprehensiveStatus = await getComprehensiveVerificationStatus(request.userId);

      // Check if pilot is verified to fly
      const pilotFlyStatus = await isPilotVerifiedToFly(request.userId);

      return { 
        ...request, 
        documents, 
        user,
        comprehensiveStatus,
        pilotFlyStatus
      };
    })
  );

  return json({
    admin,
    pendingRequests: requestsWithDetails
  });
};

export const action = async ({ request }) => {
  const admin = await requireAdmin(request);

  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "update-request-status") {
    const requestId = formData.get("requestId");
    const status = formData.get("status") as VerificationStatus;
    const notes = formData.get("notes");

    if (!requestId || !status) {
      return json({ error: "Request ID and status are required" }, { status: 400 });
    }

    try {
      const updatedRequest = await updateVerificationRequestStatus(
        parseInt(requestId.toString()),
        status,
        parseInt(admin.id),
        notes ? notes.toString() : undefined
      );

      return json({ success: true, request: updatedRequest });
    } catch (error) {
      return json({ error: "Failed to update verification request status" }, { status: 500 });
    }
  }

  if (action === "verify-document") {
    const documentId = formData.get("documentId");
    const verified = formData.get("verified") === "true";
    const notes = formData.get("notes");

    if (!documentId) {
      return json({ error: "Document ID is required" }, { status: 400 });
    }

    try {
      const document = await verifyDocument(
        parseInt(documentId.toString()),
        parseInt(admin.id),
        verified,
        notes ? notes.toString() : undefined
      );

      return json({ success: true, document });
    } catch (error) {
      return json({ error: "Failed to verify document" }, { status: 500 });
    }
  }

  if (action === "verify-id-document") {
    const documentId = formData.get("documentId");
    const userId = formData.get("userId");
    const verified = formData.get("verified") === "true";
    const notes = formData.get("notes");

    if (!documentId || !userId) {
      return json({ error: "Document ID and User ID are required" }, { status: 400 });
    }

    try {
      const result = await verifyIdDocument(
        parseInt(documentId.toString()),
        parseInt(admin.id),
        verified,
        notes ? notes.toString() : undefined
      );

      return json({ success: true, idDocument: result });
    } catch (error) {
      console.error("Error verifying ID document:", error);
      return json({ error: "Failed to verify ID document" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};

export default function AdminVerificationPage() {
  const { admin, pendingRequests } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [selectedRequest, setSelectedRequest] = useState(null);
  const [viewMode, setViewMode] = useState("list"); // "list" or "detail"

  const handleViewRequest = (request) => {
    setSelectedRequest(request);
    setViewMode("detail");
  };

  const handleBackToList = () => {
    setSelectedRequest(null);
    setViewMode("list");
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Pilot Verification Requests</h1>
        {viewMode === "detail" && (
          <button
            onClick={handleBackToList}
            className="bg-gray-200 text-gray-800 py-2 px-4 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2"
          >
            Back to List
          </button>
        )}
      </div>

      {actionData?.error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{actionData.error}</p>
        </div>
      )}

      {actionData?.success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>Action completed successfully!</p>
        </div>
      )}

      {viewMode === "list" && (
        <div>
          <div className="bg-white shadow-md rounded-lg overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pilot
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Submitted
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Documents
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {pendingRequests.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                      No pending verification requests
                    </td>
                  </tr>
                ) : (
                  pendingRequests.map((request) => (
                    <tr key={request.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {request.id}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.user?.name} ({request.user?.email})
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.requestType.charAt(0).toUpperCase() + request.requestType.slice(1)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          request.status === "approved"
                            ? "bg-green-100 text-green-800"
                            : request.status === "in_review"
                            ? "bg-yellow-100 text-yellow-800"
                            : request.status === "rejected"
                            ? "bg-red-100 text-red-800"
                            : "bg-gray-100 text-gray-800"
                        }`}>
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {new Date(request.submittedAt).toLocaleDateString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {request.documents?.length || 0}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleViewRequest(request)}
                          className="text-blue-600 hover:text-blue-900"
                        >
                          Review
                        </button>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {viewMode === "detail" && selectedRequest && (
        <div>
          <div className="bg-white shadow-md rounded-lg p-6 mb-6">
            <div className="flex justify-between">
              <div>
                <h2 className="text-2xl font-semibold mb-2">
                  {selectedRequest.requestType.charAt(0).toUpperCase() + selectedRequest.requestType.slice(1)} Verification Request
                </h2>
                <p className="text-gray-600">Request ID: {selectedRequest.id}</p>
              </div>
              <div>
                <span className={`px-3 py-1 inline-flex text-sm leading-5 font-semibold rounded-full ${
                  selectedRequest.status === "approved"
                    ? "bg-green-100 text-green-800"
                    : selectedRequest.status === "in_review"
                    ? "bg-yellow-100 text-yellow-800"
                    : selectedRequest.status === "rejected"
                    ? "bg-red-100 text-red-800"
                    : "bg-gray-100 text-gray-800"
                }`}>
                  {selectedRequest.status.charAt(0).toUpperCase() + selectedRequest.status.slice(1)}
                </span>
              </div>
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="text-lg font-semibold mb-2">Pilot Information</h3>
                <p><span className="font-medium">Name:</span> {selectedRequest.user?.name}</p>
                <p><span className="font-medium">Email:</span> {selectedRequest.user?.email}</p>
                <p><span className="font-medium">License Number:</span> {selectedRequest.user?.licenseNumber || "Not provided"}</p>
                <p><span className="font-medium">License Type:</span> {selectedRequest.user?.licenseType || "Not provided"}</p>
                <p><span className="font-medium">License Expiry:</span> {selectedRequest.user?.licenseExpiry ? new Date(selectedRequest.user.licenseExpiry).toLocaleDateString() : "Not provided"}</p>

                {/* Pilot Verification Status */}
                <div className="mt-4">
                  <h4 className="font-medium mb-1">Verification Status:</h4>
                  <div className="flex items-center">
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedRequest.pilotFlyStatus?.verified
                        ? "bg-green-100 text-green-800"
                        : "bg-yellow-100 text-yellow-800"
                    }`}>
                      {selectedRequest.pilotFlyStatus?.verified ? "Verified to Fly" : "Not Fully Verified"}
                    </span>
                  </div>
                  {!selectedRequest.pilotFlyStatus?.verified && (
                    <p className="text-sm text-yellow-700 mt-1">
                      {selectedRequest.pilotFlyStatus?.message}
                    </p>
                  )}
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-2">Request Details</h3>
                <p><span className="font-medium">Submitted:</span> {new Date(selectedRequest.submittedAt).toLocaleString()}</p>
                {selectedRequest.reviewedAt && (
                  <p><span className="font-medium">Reviewed:</span> {new Date(selectedRequest.reviewedAt).toLocaleString()}</p>
                )}
                {selectedRequest.notes && (
                  <div className="mt-2">
                    <p className="font-medium">Pilot Notes:</p>
                    <p className="text-gray-700">{selectedRequest.notes}</p>
                  </div>
                )}
                {selectedRequest.adminNotes && (
                  <div className="mt-2">
                    <p className="font-medium">Admin Notes:</p>
                    <p className="text-gray-700">{selectedRequest.adminNotes}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Comprehensive Verification Status */}
            <div className="mt-6 border-t pt-6">
              <h3 className="text-lg font-semibold mb-4">Comprehensive Verification Status</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {/* Identity Verification */}
                <div className="border rounded-lg p-3 shadow-sm">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold">Identity</h4>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.verified
                        ? "bg-green-100 text-green-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "in_review"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "rejected"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.verified
                        ? "Verified"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "in_review"
                        ? "In Review"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "rejected"
                        ? "Rejected"
                        : "Pending"}
                    </span>
                  </div>
                </div>

                {/* Pilot License */}
                <div className="border rounded-lg p-3 shadow-sm">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold">Pilot License</h4>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.verified
                        ? "bg-green-100 text-green-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "in_review"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "rejected"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.verified
                        ? "Verified"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "in_review"
                        ? "In Review"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "rejected"
                        ? "Rejected"
                        : "Pending"}
                    </span>
                  </div>
                </div>

                {/* Medical Certificate */}
                <div className="border rounded-lg p-3 shadow-sm">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold">Medical</h4>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.verified
                        ? "bg-green-100 text-green-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "in_review"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "rejected"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.verified
                        ? "Verified"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "in_review"
                        ? "In Review"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "rejected"
                        ? "Rejected"
                        : "Pending"}
                    </span>
                  </div>
                </div>

                {/* Background Check */}
                <div className="border rounded-lg p-3 shadow-sm">
                  <div className="flex justify-between items-start">
                    <h4 className="font-semibold">Background Check</h4>
                    <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                      selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.verified
                        ? "bg-green-100 text-green-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "in_review"
                        ? "bg-yellow-100 text-yellow-800"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "rejected"
                        ? "bg-red-100 text-red-800"
                        : "bg-gray-100 text-gray-800"
                    }`}>
                      {selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.verified
                        ? "Verified"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "in_review"
                        ? "In Progress"
                        : selectedRequest.comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "rejected"
                        ? "Failed"
                        : "Pending"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Missing Required Credentials */}
              {selectedRequest.comprehensiveStatus?.missingRequiredCredentials.length > 0 && (
                <div className="mt-4 p-3 bg-yellow-50 rounded-lg">
                  <h4 className="font-semibold mb-1">Missing Required Credentials:</h4>
                  <ul className="list-disc pl-5">
                    {selectedRequest.comprehensiveStatus.missingRequiredCredentials.map((credential) => (
                      <li key={credential} className="text-yellow-700">
                        {credential === CredentialType.IDENTITY && "Identity Verification"}
                        {credential === CredentialType.PILOT_LICENSE && "Pilot License"}
                        {credential === CredentialType.MEDICAL_CERTIFICATE && "Medical Certificate"}
                        {credential === CredentialType.BACKGROUND_CHECK && "Background Check"}
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </div>

          <div className="bg-white shadow-md rounded-lg p-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Uploaded Documents</h3>

            {selectedRequest.documents?.length === 0 ? (
              <p className="text-gray-600">No documents uploaded yet.</p>
            ) : (
              <div className="space-y-6">
                {selectedRequest.documents?.map((document) => {
                  // Parse OCR data if it exists
                  let ocrData = null;
                  if (document.ocr_data) {
                    try {
                      ocrData = JSON.parse(document.ocr_data);
                    } catch (e) {
                      console.error("Error parsing OCR data:", e);
                    }
                  }

                  // Add parsed data to document object
                  const documentWithOcr = {
                    ...document,
                    ocrData,
                    ocrConfidence: document.ocr_confidence,
                    mobileCaptured: document.mobile_capture
                  };

                  return (
                  <div key={document.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="text-lg font-semibold">
                          {document.documentType.charAt(0).toUpperCase() + document.documentType.slice(1).replace(/_/g, ' ')}
                          {document.mobileCaptured && (
                            <span className="ml-2 px-2 py-0.5 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                              Mobile Capture
                            </span>
                          )}
                        </h4>
                        <p className="text-gray-600">{document.fileName}</p>
                        <p className="text-sm text-gray-500">Uploaded: {new Date(document.uploadedAt).toLocaleString()}</p>
                      </div>
                      <div>
                        <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          document.verified ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
                        }`}>
                          {document.verified ? "Verified" : "Pending"}
                        </span>
                      </div>
                    </div>

                    {document.notes && (
                      <div className="mt-2">
                        <p className="font-medium">Document Notes:</p>
                        <p className="text-gray-700">{document.notes}</p>
                      </div>
                    )}

                    {document.ocrData && (
                      <div className="mt-3 bg-gray-50 p-3 rounded-md">
                        <div className="flex justify-between items-center mb-2">
                          <p className="font-medium text-sm">OCR Extracted Data:</p>
                          <span className="text-xs text-gray-500">
                            Confidence: {Math.round((document.ocrConfidence || 0) * 100)}%
                          </span>
                        </div>
                        <div className="text-sm text-gray-700 space-y-1">
                          {document.ocrData.fullName && (
                            <p><span className="font-medium">Name:</span> {document.ocrData.fullName}</p>
                          )}
                          {document.ocrData.documentNumber && (
                            <p><span className="font-medium">Document Number:</span> {document.ocrData.documentNumber}</p>
                          )}
                          {document.ocrData.expiryDate && (
                            <p><span className="font-medium">Expiry Date:</span> {new Date(document.ocrData.expiryDate).toLocaleDateString()}</p>
                          )}
                          {document.ocrData.dateOfBirth && (
                            <p><span className="font-medium">Date of Birth:</span> {new Date(document.ocrData.dateOfBirth).toLocaleDateString()}</p>
                          )}
                          {document.ocrData.issuingCountry && (
                            <p><span className="font-medium">Issuing Country:</span> {document.ocrData.issuingCountry}</p>
                          )}
                          {document.ocrData.issuingAuthority && (
                            <p><span className="font-medium">Issuing Authority:</span> {document.ocrData.issuingAuthority}</p>
                          )}
                          {document.ocrData.pilotLicenseType && (
                            <p><span className="font-medium">License Type:</span> {document.ocrData.pilotLicenseType}</p>
                          )}
                        </div>
                      </div>
                    )}

                    <div className="mt-4">
                      <a 
                        href={document.filePath} 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800 mr-4"
                      >
                        View Document
                      </a>

                      <Form method="post" className="mt-2">
                        {/* Use verify-id-document action for ID documents */}
                        {document.documentType === 'passport' || 
                         document.documentType === 'drivers_license' || 
                         document.documentType === 'government_id' ? (
                          <>
                            <input type="hidden" name="action" value="verify-id-document" />
                            <input type="hidden" name="documentId" value={document.id} />
                            <input type="hidden" name="userId" value={selectedRequest.userId} />
                            <input type="hidden" name="verified" value={!document.verified ? "true" : "false"} />

                            <div className="flex items-center space-x-2">
                              <input
                                type="text"
                                name="notes"
                                placeholder="ID verification notes (optional)"
                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                              <button
                                type="submit"
                                disabled={isSubmitting}
                                className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                                  document.verified
                                    ? "bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500"
                                    : "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500"
                                }`}
                              >
                                {isSubmitting ? "Processing..." : document.verified ? "Unverify ID" : "Verify ID"}
                              </button>
                            </div>
                            <p className="text-xs text-gray-500 mt-1">
                              This will update the pilot's identity verification status.
                            </p>
                          </>
                        ) : (
                          <>
                            <input type="hidden" name="action" value="verify-document" />
                            <input type="hidden" name="documentId" value={document.id} />
                            <input type="hidden" name="verified" value={!document.verified ? "true" : "false"} />

                            <div className="flex items-center space-x-2">
                              <input
                                type="text"
                                name="notes"
                                placeholder="Verification notes (optional)"
                                className="flex-grow px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                              />
                              <button
                                type="submit"
                                disabled={isSubmitting}
                                className={`px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 ${
                                  document.verified
                                    ? "bg-yellow-600 text-white hover:bg-yellow-700 focus:ring-yellow-500"
                                    : "bg-green-600 text-white hover:bg-green-700 focus:ring-green-500"
                                }`}
                              >
                                {isSubmitting ? "Processing..." : document.verified ? "Unverify" : "Verify"}
                              </button>
                            </div>
                          </>
                        )}
                      </Form>
                    </div>
                  </div>
                )})}
              </div>
            )}
          </div>

          <div className="bg-white shadow-md rounded-lg p-6">
            <h3 className="text-xl font-semibold mb-4">Update Request Status</h3>

            <Form method="post" className="space-y-4">
              <input type="hidden" name="action" value="update-request-status" />
              <input type="hidden" name="requestId" value={selectedRequest.id} />

              <div>
                <label htmlFor="status" className="block text-gray-700 font-semibold mb-2">
                  Status
                </label>
                <select
                  id="status"
                  name="status"
                  defaultValue={selectedRequest.status}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  required
                >
                  <option value="pending">Pending</option>
                  <option value="in_review">In Review</option>
                  <option value="approved">Approved</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>

              <div>
                <label htmlFor="notes" className="block text-gray-700 font-semibold mb-2">
                  Admin Notes
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  rows={3}
                  defaultValue={selectedRequest.adminNotes}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Add notes about this verification request"
                />
              </div>

              <div className="flex justify-end space-x-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="bg-blue-600 text-white py-2 px-6 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  {isSubmitting ? "Updating..." : "Update Status"}
                </button>
              </div>
            </Form>
          </div>
        </div>
      )}
    </div>
  );
}
