import { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "Pilot Verification | FerryPros" },
    { name: "description", content: "Comprehensive pilot verification services ensuring qualified and trustworthy pilots for your aircraft." },
  ];
};

export default function PilotVerificationService() {
  return (
    <div className="container-custom py-12">
      <h1 className="text-3xl font-bold mb-8">Pilot Verification Service</h1>
      
      <div className="prose max-w-none">
        <p className="mb-6">
          At FerryPros, we understand that trusting someone with your aircraft requires complete confidence in their qualifications and experience. 
          Our comprehensive pilot verification service ensures that every pilot in our network meets the highest standards of professionalism and expertise.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">Our Verification Process</h2>
        <p className="mb-4">
          We've developed a rigorous verification process that goes beyond basic credential checks:
        </p>
        <ol className="list-decimal pl-6 mb-6">
          <li className="mb-2"><strong>License Verification</strong> - We confirm the validity of all pilot licenses and ratings</li>
          <li className="mb-2"><strong>Experience Validation</strong> - We verify flight hours and aircraft type experience</li>
          <li className="mb-2"><strong>Background Checks</strong> - Comprehensive background screening for safety and security</li>
          <li className="mb-2"><strong>Reference Checks</strong> - We contact previous employers and clients</li>
          <li className="mb-2"><strong>Insurance Verification</strong> - Confirmation of appropriate insurance coverage</li>
          <li className="mb-2"><strong>Ongoing Monitoring</strong> - Regular updates to ensure continued compliance</li>
        </ol>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">Verification Standards</h2>
        <p className="mb-4">
          Our verification standards include:
        </p>
        <ul className="list-disc pl-6 mb-6">
          <li className="mb-2"><strong>Valid Pilot Certificates</strong> - Commercial or ATP certificates with appropriate ratings</li>
          <li className="mb-2"><strong>Medical Certification</strong> - Current medical certificate appropriate for the operations</li>
          <li className="mb-2"><strong>Flight Experience</strong> - Minimum flight hours and recent experience requirements</li>
          <li className="mb-2"><strong>Type-Specific Experience</strong> - Documented experience in the specific aircraft types</li>
          <li className="mb-2"><strong>Clean Record</strong> - No significant violations or accidents</li>
        </ul>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">Verification Badge</h2>
        <p className="mb-4">
          Pilots who successfully complete our verification process receive the FerryPros Verified Pilot badge on their profile. 
          This badge signals to aircraft owners that the pilot has been thoroughly vetted and meets our high standards.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">For Pilots</h2>
        <p className="mb-4">
          If you're a pilot looking to join our network, our verification process helps you stand out from the crowd. 
          A verified status demonstrates your commitment to professionalism and gives aircraft owners confidence in your abilities.
        </p>
        
        <h2 className="text-2xl font-semibold mt-8 mb-4">For Aircraft Owners</h2>
        <p className="mb-4">
          When you hire a FerryPros verified pilot, you can be confident that they have the qualifications, experience, and 
          professionalism to safely transport your valuable aircraft.
        </p>
        
        <div className="mt-6">
          <a href="/register" className="btn-primary mr-4">Register Now</a>
          <a href="/login" className="btn-secondary">Log In</a>
        </div>
      </div>
    </div>
  );
}