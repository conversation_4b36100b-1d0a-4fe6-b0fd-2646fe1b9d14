import { json, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { requireUser } from "~/utils/session.server";
import {
  createVerificationRequest,
  addVerificationDocument,
  getVerificationRequestsForUser,
  getDocumentsForVerificationRequest,
  getUserVerificationStatus,
  isUserFullyVerified
} from "~/utils/pilotVerification.server";
import { getComprehensiveVerificationStatus, isPilotVerifiedToFly } from "~/utils/credentialVerification.server";
import { initiateBackgroundCheck, getBackgroundCheckStatus } from "~/utils/backgroundCheck.server";
import { uploadIdDocument, base64ToBuffer } from "~/utils/idVerification.server";
import { CredentialType, BackgroundCheckType, IdDocumentType } from "~/types/verification";

export const loader = async ({ request }) => {
  const user = await requireUser(request);

  // Only pilots can access this page
  if (user.role !== "pilot") {
    return redirect("/dashboard");
  }

  const userId = parseInt(user.id);

  // Get verification status and requests
  const verificationStatus = await getUserVerificationStatus(userId);
  const verificationRequests = await getVerificationRequestsForUser(userId);

  // Get documents for each request
  const requestsWithDocuments = await Promise.all(
    verificationRequests.map(async (request) => {
      const documents = await getDocumentsForVerificationRequest(request.id);
      return { ...request, documents };
    })
  );

  const isFullyVerified = await isUserFullyVerified(userId);

  // Get comprehensive verification status
  const comprehensiveStatus = await getComprehensiveVerificationStatus(userId);

  // Get background check status
  const backgroundCheckStatus = await getBackgroundCheckStatus(userId);

  // Check if pilot is verified to fly
  const pilotFlyStatus = await isPilotVerifiedToFly(userId);

  return json({
    user: user || {},
    verificationStatus: verificationStatus || {
      identity: { verified: false, status: 'pending', verificationDate: undefined },
      experience: { verified: false, status: 'pending', verificationDate: undefined },
      medical: { verified: false, status: 'pending', verificationDate: undefined },
      notes: ''
    },
    verificationRequests: requestsWithDocuments || [],
    isFullyVerified: isFullyVerified || false,
    comprehensiveStatus: comprehensiveStatus || {},
    backgroundCheckStatus: backgroundCheckStatus || {},
    pilotFlyStatus: pilotFlyStatus || false
  });
};

export const action = async ({ request }) => {
  const user = await requireUser(request);

  // Ensure user is always an object even if API returns 204
  const safeUser = user || {};

  // Only pilots can submit verification requests
  if (safeUser.role !== "pilot") {
    return redirect("/dashboard");
  }

  const userId = parseInt(safeUser.id || '0');
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "create-request") {
    const requestType = formData.get("requestType");
    const notes = formData.get("notes");

    if (!requestType) {
      return json({ error: "Request type is required" }, { status: 400 });
    }

    try {
      const request = await createVerificationRequest(
        userId,
        requestType.toString(),
        notes ? notes.toString() : undefined
      );

      return json({ success: true, request });
    } catch (error) {
      return json({ error: "Failed to create verification request" }, { status: 500 });
    }
  }

  if (action === "upload-document") {
    const requestId = formData.get("requestId");
    const documentType = formData.get("documentType");
    const file = formData.get("file");
    const notes = formData.get("notes");

    if (!requestId || !documentType || !file) {
      return json({ error: "Request ID, document type, and file are required" }, { status: 400 });
    }

    // In a real application, you would upload the file to a storage service
    // and get a file path. For this example, we'll use a placeholder.
    const filePath = `/uploads/${Date.now()}-${file.name}`;

    try {
      const document = await addVerificationDocument(
        parseInt(requestId.toString()),
        documentType.toString(),
        filePath,
        file.name,
        file.type,
        notes ? notes.toString() : undefined
      );

      return json({ success: true, document });
    } catch (error) {
      return json({ error: "Failed to upload document" }, { status: 500 });
    }
  }

  if (action === "upload-id-document") {
    const documentType = formData.get("documentType");
    const fileData = formData.get("fileData"); // Base64 encoded file
    const fileName = formData.get("fileName");
    const fileType = formData.get("fileType");
    const notes = formData.get("notes");

    if (!documentType || !fileData || !fileName || !fileType) {
      return json({ error: "Document type, file data, file name, and file type are required" }, { status: 400 });
    }

    try {
      // Convert base64 to buffer
      const buffer = base64ToBuffer(fileData.toString());

      // Map document type to IdDocumentType
      let idDocType;
      switch (documentType.toString()) {
        case "passport":
          idDocType = IdDocumentType.PASSPORT;
          break;
        case "drivers_license":
          idDocType = IdDocumentType.DRIVERS_LICENSE;
          break;
        case "government_id":
          idDocType = IdDocumentType.GOVERNMENT_ID;
          break;
        case "pilot_license":
          idDocType = IdDocumentType.PILOT_LICENSE;
          break;
        default:
          idDocType = IdDocumentType.GOVERNMENT_ID;
      }

      const result = await uploadIdDocument({
        userId,
        documentType: idDocType,
        fileData: buffer,
        fileName: fileName.toString(),
        fileType: fileType.toString(),
        notes: notes ? notes.toString() : undefined
      });

      return json({ success: true, idDocument: result });
    } catch (error) {
      console.error("Error uploading ID document:", error);
      return json({ error: "Failed to upload ID document" }, { status: 500 });
    }
  }

  if (action === "initiate-background-check") {
    const checkType = formData.get("checkType");

    if (!checkType) {
      return json({ error: "Background check type is required" }, { status: 400 });
    }

    try {
      // Map check type to BackgroundCheckType
      let bgCheckType;
      switch (checkType.toString()) {
        case "criminal":
          bgCheckType = BackgroundCheckType.CRIMINAL;
          break;
        case "driving_record":
          bgCheckType = BackgroundCheckType.DRIVING_RECORD;
          break;
        case "employment":
          bgCheckType = BackgroundCheckType.EMPLOYMENT;
          break;
        case "education":
          bgCheckType = BackgroundCheckType.EDUCATION;
          break;
        case "comprehensive":
        default:
          bgCheckType = BackgroundCheckType.COMPREHENSIVE;
      }

      const result = await initiateBackgroundCheck(userId, bgCheckType);

      return json({ success: true, backgroundCheck: result });
    } catch (error) {
      console.error("Error initiating background check:", error);
      return json({ error: "Failed to initiate background check" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};

export default function VerificationPage() {
  const { 
    user, 
    verificationStatus, 
    verificationRequests, 
    isFullyVerified,
    comprehensiveStatus,
    backgroundCheckStatus,
    pilotFlyStatus
  } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [activeTab, setActiveTab] = useState("status");
  const [selectedRequestType, setSelectedRequestType] = useState("identity");
  const [selectedBackgroundCheckType, setSelectedBackgroundCheckType] = useState(BackgroundCheckType.COMPREHENSIVE);
  const [selectedIdDocumentType, setSelectedIdDocumentType] = useState(IdDocumentType.DRIVERS_LICENSE);
  const [fileBase64, setFileBase64] = useState("");
  const [fileName, setFileName] = useState("");
  const [fileType, setFileType] = useState("");

  // Function to handle file selection for ID document upload
  const handleFileChange = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    setFileName(file.name);
    setFileType(file.type);

    const reader = new FileReader();
    reader.onload = (e) => {
      setFileBase64(e.target.result);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Pilot Verification</h1>
        {user.role === "pilot" && (
          <Link
            to="/settings/mobile-capture"
            className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          >
            Mobile Document Capture
          </Link>
        )}
      </div>

      {pilotFlyStatus?.verified ? (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <p className="font-bold">Fully Verified and Eligible to Fly!</p>
          <p>All required credentials have been verified. Aircraft owners will see your verified status.</p>
        </div>
      ) : (
        <div className="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-6">
          <p className="font-bold">Verification Incomplete</p>
          <p>{pilotFlyStatus?.message || "You need to complete all required verifications before you can fly."}</p>
        </div>
      )}

      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex">
            <button
              onClick={() => setActiveTab("status")}
              className={`py-4 px-6 ${
                activeTab === "status"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Verification Status
            </button>
            <button
              onClick={() => setActiveTab("submit")}
              className={`py-4 px-6 ${
                activeTab === "submit"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Submit Verification
            </button>
            <button
              onClick={() => setActiveTab("history")}
              className={`py-4 px-6 ${
                activeTab === "history"
                  ? "border-b-2 border-blue-500 text-blue-600"
                  : "text-gray-500 hover:text-gray-700"
              }`}
            >
              Request History
            </button>
          </nav>
        </div>
      </div>

      {activeTab === "status" && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Your Verification Status</h2>

          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-3">Required Credentials</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Identity Verification */}
              <div className="border rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold mb-2">Identity Verification</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.verified
                      ? "bg-green-100 text-green-800"
                      : comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "in_review"
                      ? "bg-yellow-100 text-yellow-800"
                      : comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "rejected"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.verified
                      ? "Verified"
                      : comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "in_review"
                      ? "In Review"
                      : comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.status === "rejected"
                      ? "Rejected"
                      : "Pending"}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  Government-issued photo ID verification
                </p>
                {comprehensiveStatus?.credentials[CredentialType.IDENTITY]?.lastChecked && (
                  <div className="text-xs text-gray-600">
                    Last checked: {new Date(comprehensiveStatus.credentials[CredentialType.IDENTITY].lastChecked).toLocaleDateString()}
                  </div>
                )}
              </div>

              {/* Pilot License */}
              <div className="border rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold mb-2">Pilot License</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.verified
                      ? "bg-green-100 text-green-800"
                      : comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "in_review"
                      ? "bg-yellow-100 text-yellow-800"
                      : comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "rejected"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.verified
                      ? "Verified"
                      : comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "in_review"
                      ? "In Review"
                      : comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.status === "rejected"
                      ? "Rejected"
                      : "Pending"}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  Valid FAA pilot license
                </p>
                {comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.details?.licenseNumber && (
                  <div className="text-xs text-gray-600">
                    License: {comprehensiveStatus.credentials[CredentialType.PILOT_LICENSE].details.licenseNumber}
                  </div>
                )}
                {comprehensiveStatus?.credentials[CredentialType.PILOT_LICENSE]?.details?.expiryDate && (
                  <div className="text-xs text-gray-600">
                    Expires: {new Date(comprehensiveStatus.credentials[CredentialType.PILOT_LICENSE].details.expiryDate).toLocaleDateString()}
                  </div>
                )}
              </div>

              {/* Medical Certificate */}
              <div className="border rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold mb-2">Medical Certificate</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.verified
                      ? "bg-green-100 text-green-800"
                      : comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "in_review"
                      ? "bg-yellow-100 text-yellow-800"
                      : comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "rejected"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.verified
                      ? "Verified"
                      : comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "in_review"
                      ? "In Review"
                      : comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.status === "rejected"
                      ? "Rejected"
                      : "Pending"}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  Valid FAA medical certificate
                </p>
                {comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.details?.certificateType && (
                  <div className="text-xs text-gray-600">
                    Type: {comprehensiveStatus.credentials[CredentialType.MEDICAL_CERTIFICATE].details.certificateType}
                  </div>
                )}
                {comprehensiveStatus?.credentials[CredentialType.MEDICAL_CERTIFICATE]?.details?.expiryDate && (
                  <div className="text-xs text-gray-600">
                    Expires: {new Date(comprehensiveStatus.credentials[CredentialType.MEDICAL_CERTIFICATE].details.expiryDate).toLocaleDateString()}
                  </div>
                )}
              </div>

              {/* Background Check */}
              <div className="border rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold mb-2">Background Check</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.verified
                      ? "bg-green-100 text-green-800"
                      : comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "in_review"
                      ? "bg-yellow-100 text-yellow-800"
                      : comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "rejected"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.verified
                      ? "Verified"
                      : comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "in_review"
                      ? "In Progress"
                      : comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.status === "rejected"
                      ? "Failed"
                      : "Pending"}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  Criminal background check
                </p>
                {comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.lastChecked && (
                  <div className="text-xs text-gray-600">
                    Completed: {new Date(comprehensiveStatus.credentials[CredentialType.BACKGROUND_CHECK].lastChecked).toLocaleDateString()}
                  </div>
                )}
                {comprehensiveStatus?.credentials[CredentialType.BACKGROUND_CHECK]?.details?.reportId && (
                  <div className="text-xs text-gray-600">
                    Report ID: {comprehensiveStatus.credentials[CredentialType.BACKGROUND_CHECK].details.reportId}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Recommended Credentials */}
          <div className="mb-6">
            <h3 className="text-xl font-semibold mb-3">Recommended Credentials</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Flight Experience */}
              <div className="border rounded-lg p-4 shadow-sm">
                <div className="flex justify-between items-start">
                  <h4 className="text-lg font-semibold mb-2">Flight Experience</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                    comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.verified
                      ? "bg-green-100 text-green-800"
                      : comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.status === "in_review"
                      ? "bg-yellow-100 text-yellow-800"
                      : comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.status === "rejected"
                      ? "bg-red-100 text-red-800"
                      : "bg-gray-100 text-gray-800"
                  }`}>
                    {comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.verified
                      ? "Verified"
                      : comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.status === "in_review"
                      ? "In Review"
                      : comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.status === "rejected"
                      ? "Rejected"
                      : "Pending"}
                  </span>
                </div>
                <p className="text-sm text-gray-700 mb-2">
                  Verified flight hours and experience
                </p>
                {comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.details?.experienceHours && (
                  <div className="text-xs text-gray-600">
                    Hours: {comprehensiveStatus.credentials[CredentialType.FLIGHT_EXPERIENCE].details.experienceHours}
                  </div>
                )}
                {comprehensiveStatus?.credentials[CredentialType.FLIGHT_EXPERIENCE]?.details?.certifications && (
                  <div className="text-xs text-gray-600">
                    Certifications: {comprehensiveStatus.credentials[CredentialType.FLIGHT_EXPERIENCE].details.certifications}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Missing Credentials */}
          {comprehensiveStatus?.missingRequiredCredentials.length > 0 && (
            <div className="mt-6 p-4 bg-yellow-50 rounded-lg">
              <h3 className="font-semibold mb-2">Missing Required Credentials:</h3>
              <ul className="list-disc pl-5">
                {comprehensiveStatus.missingRequiredCredentials.map((credential) => (
                  <li key={credential} className="text-yellow-700">
                    {credential === CredentialType.IDENTITY && "Identity Verification"}
                    {credential === CredentialType.PILOT_LICENSE && "Pilot License"}
                    {credential === CredentialType.MEDICAL_CERTIFICATE && "Medical Certificate"}
                    {credential === CredentialType.BACKGROUND_CHECK && "Background Check"}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Background Check Section */}
          <div className="mt-8 border rounded-lg p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-4">Background Check</h3>

            {backgroundCheckStatus?.status === "verified" ? (
              <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
                <p className="font-bold">Background Check Completed</p>
                <p>Your background check has been completed and verified.</p>
                {backgroundCheckStatus.date && (
                  <p className="text-sm mt-1">Completed on: {new Date(backgroundCheckStatus.date).toLocaleDateString()}</p>
                )}
                {backgroundCheckStatus.reportUrl && (
                  <p className="mt-2">
                    <a 
                      href={backgroundCheckStatus.reportUrl} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      View Background Check Report
                    </a>
                  </p>
                )}
              </div>
            ) : backgroundCheckStatus?.status === "in_progress" ? (
              <div className="bg-blue-100 border border-blue-400 text-blue-700 px-4 py-3 rounded mb-4">
                <p className="font-bold">Background Check In Progress</p>
                <p>Your background check is currently being processed. This may take 1-3 business days.</p>
              </div>
            ) : backgroundCheckStatus?.status === "rejected" ? (
              <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <p className="font-bold">Background Check Failed</p>
                <p>Your background check could not be verified. Please contact support for more information.</p>
              </div>
            ) : (
              <div>
                <p className="mb-4">A background check is required to verify your eligibility to fly. This helps ensure safety and security for aircraft owners.</p>

                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="initiate-background-check" />

                  <div>
                    <label htmlFor="checkType" className="block text-gray-700 font-semibold mb-2">
                      Background Check Type
                    </label>
                    <select
                      id="checkType"
                      name="checkType"
                      value={selectedBackgroundCheckType}
                      onChange={(e) => setSelectedBackgroundCheckType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value={BackgroundCheckType.COMPREHENSIVE}>Comprehensive (Recommended)</option>
                      <option value={BackgroundCheckType.CRIMINAL}>Criminal History</option>
                      <option value={BackgroundCheckType.DRIVING_RECORD}>Driving Record</option>
                    </select>
                    <p className="text-sm text-gray-500 mt-1">
                      A comprehensive check is recommended and includes criminal history, driving record, and identity verification.
                    </p>
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                  >
                    {isSubmitting ? "Processing..." : "Initiate Background Check"}
                  </button>
                </Form>
              </div>
            )}
          </div>

          {/* Direct ID Upload Section */}
          <div className="mt-8 border rounded-lg p-6 shadow-sm">
            <h3 className="text-xl font-semibold mb-4">Upload Identification Documents</h3>

            <p className="mb-4">Upload your government-issued ID and other documents to verify your identity.</p>

            <Form method="post" className="space-y-4" encType="multipart/form-data">
              <input type="hidden" name="action" value="upload-id-document" />

              <div>
                <label htmlFor="idDocumentType" className="block text-gray-700 font-semibold mb-2">
                  Document Type
                </label>
                <select
                  id="idDocumentType"
                  name="documentType"
                  value={selectedIdDocumentType}
                  onChange={(e) => setSelectedIdDocumentType(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value={IdDocumentType.DRIVERS_LICENSE}>Driver's License</option>
                  <option value={IdDocumentType.PASSPORT}>Passport</option>
                  <option value={IdDocumentType.GOVERNMENT_ID}>Government ID</option>
                  <option value={IdDocumentType.PILOT_LICENSE}>Pilot License</option>
                </select>
              </div>

              <div>
                <label htmlFor="idFile" className="block text-gray-700 font-semibold mb-2">
                  Document File
                </label>
                <input
                  type="file"
                  id="idFile"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  accept="image/jpeg,image/png,application/pdf"
                />
                <p className="text-sm text-gray-500 mt-1">
                  Accepted file types: PDF, JPG, PNG (max 10MB)
                </p>
              </div>

              {/* Hidden fields for file data */}
              <input type="hidden" name="fileData" value={fileBase64} />
              <input type="hidden" name="fileName" value={fileName} />
              <input type="hidden" name="fileType" value={fileType} />

              <div>
                <label htmlFor="idNotes" className="block text-gray-700 font-semibold mb-2">
                  Notes (Optional)
                </label>
                <textarea
                  id="idNotes"
                  name="notes"
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Any notes about this document"
                />
              </div>

              <button
                type="submit"
                disabled={isSubmitting || !fileBase64}
                className="bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
              >
                {isSubmitting ? "Uploading..." : "Upload ID Document"}
              </button>
            </Form>
          </div>
        </div>
      )}

      {activeTab === "submit" && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Submit Verification Request</h2>

          {actionData?.error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
              <p>{actionData.error}</p>
            </div>
          )}

          {actionData?.success && (
            <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
              <p>Verification request submitted successfully!</p>
            </div>
          )}

          <div className="mb-6">
            <label className="block text-gray-700 font-semibold mb-2">Select Verification Type</label>
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => setSelectedRequestType("identity")}
                className={`px-4 py-2 rounded ${
                  selectedRequestType === "identity"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
              >
                Identity
              </button>
              <button
                type="button"
                onClick={() => setSelectedRequestType("experience")}
                className={`px-4 py-2 rounded ${
                  selectedRequestType === "experience"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
              >
                Experience
              </button>
              <button
                type="button"
                onClick={() => setSelectedRequestType("medical")}
                className={`px-4 py-2 rounded ${
                  selectedRequestType === "medical"
                    ? "bg-blue-600 text-white"
                    : "bg-gray-200 text-gray-800 hover:bg-gray-300"
                }`}
              >
                Medical
              </button>
            </div>
          </div>

          <Form method="post" className="space-y-6">
            <input type="hidden" name="action" value="create-request" />
            <input type="hidden" name="requestType" value={selectedRequestType} />

            <div>
              <label htmlFor="notes" className="block text-gray-700 font-semibold mb-2">
                Additional Notes (Optional)
              </label>
              <textarea
                id="notes"
                name="notes"
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Add any additional information that might help with verification"
              />
            </div>

            <div>
              <h3 className="text-lg font-semibold mb-2">Required Documents</h3>
              {selectedRequestType === "identity" && (
                <ul className="list-disc pl-5 mb-4 text-gray-700">
                  <li>Government-issued photo ID (passport, driver's license)</li>
                  <li>Proof of address (utility bill, bank statement)</li>
                </ul>
              )}
              {selectedRequestType === "experience" && (
                <ul className="list-disc pl-5 mb-4 text-gray-700">
                  <li>Pilot logbook (last 10 pages)</li>
                  <li>Certificates and ratings</li>
                  <li>Recent flight experience documentation</li>
                </ul>
              )}
              {selectedRequestType === "medical" && (
                <ul className="list-disc pl-5 mb-4 text-gray-700">
                  <li>Medical certificate</li>
                  <li>Any special issuances or waivers</li>
                </ul>
              )}
            </div>

            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
            >
              {isSubmitting ? "Submitting..." : "Submit Verification Request"}
            </button>
          </Form>

          {actionData?.request && (
            <div className="mt-8">
              <h3 className="text-xl font-semibold mb-4">Upload Documents</h3>
              <p className="mb-4">Your verification request has been created. Please upload the required documents.</p>

              <Form method="post" className="space-y-6" encType="multipart/form-data">
                <input type="hidden" name="action" value="upload-document" />
                <input type="hidden" name="requestId" value={actionData.request.id} />

                <div>
                  <label htmlFor="documentType" className="block text-gray-700 font-semibold mb-2">
                    Document Type
                  </label>
                  <select
                    id="documentType"
                    name="documentType"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  >
                    {selectedRequestType === "identity" && (
                      <>
                        <option value="passport">Passport</option>
                        <option value="drivers_license">Driver's License</option>
                        <option value="proof_of_address">Proof of Address</option>
                      </>
                    )}
                    {selectedRequestType === "experience" && (
                      <>
                        <option value="logbook">Logbook</option>
                        <option value="certificate">Certificate</option>
                        <option value="rating">Rating</option>
                      </>
                    )}
                    {selectedRequestType === "medical" && (
                      <>
                        <option value="medical_certificate">Medical Certificate</option>
                        <option value="special_issuance">Special Issuance</option>
                        <option value="waiver">Waiver</option>
                      </>
                    )}
                  </select>
                </div>

                <div>
                  <label htmlFor="file" className="block text-gray-700 font-semibold mb-2">
                    Document File
                  </label>
                  <input
                    type="file"
                    id="file"
                    name="file"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    required
                  />
                  <p className="text-sm text-gray-500 mt-1">
                    Accepted file types: PDF, JPG, PNG (max 10MB)
                  </p>
                </div>

                <div>
                  <label htmlFor="notes" className="block text-gray-700 font-semibold mb-2">
                    Notes (Optional)
                  </label>
                  <textarea
                    id="notes"
                    name="notes"
                    rows={2}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    placeholder="Any notes about this document"
                  />
                </div>

                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
                >
                  {isSubmitting ? "Uploading..." : "Upload Document"}
                </button>
              </Form>
            </div>
          )}
        </div>
      )}

      {activeTab === "history" && (
        <div>
          <h2 className="text-2xl font-semibold mb-4">Verification Request History</h2>

          {verificationRequests.length === 0 ? (
            <p className="text-gray-600">You haven't submitted any verification requests yet.</p>
          ) : (
            <div className="space-y-6">
              {verificationRequests.map((request) => (
                <div key={request.id} className="border rounded-lg p-6 shadow-sm">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="text-xl font-semibold mb-2">
                        {request.requestType.charAt(0).toUpperCase() + request.requestType.slice(1)} Verification
                      </h3>
                      <div className="mb-2">
                        <span className="font-medium">Status: </span>
                        <span className={`${
                          request.status === "approved"
                            ? "text-green-600"
                            : request.status === "in_review"
                            ? "text-yellow-600"
                            : request.status === "rejected"
                            ? "text-red-600"
                            : "text-gray-600"
                        }`}>
                          {request.status.charAt(0).toUpperCase() + request.status.slice(1)}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Submitted: {new Date(request.submittedAt).toLocaleDateString()}
                      </div>
                      {request.reviewedAt && (
                        <div className="text-sm text-gray-600">
                          Reviewed: {new Date(request.reviewedAt).toLocaleDateString()}
                        </div>
                      )}
                    </div>
                    <div className="bg-gray-100 px-3 py-1 rounded text-gray-700 text-sm">
                      ID: {request.id}
                    </div>
                  </div>

                  {request.notes && (
                    <div className="mt-4">
                      <h4 className="font-medium">Your Notes:</h4>
                      <p className="text-gray-700">{request.notes}</p>
                    </div>
                  )}

                  {request.adminNotes && (
                    <div className="mt-4">
                      <h4 className="font-medium">Admin Notes:</h4>
                      <p className="text-gray-700">{request.adminNotes}</p>
                    </div>
                  )}

                  {request.documents && request.documents.length > 0 && (
                    <div className="mt-6">
                      <h4 className="font-medium mb-2">Uploaded Documents:</h4>
                      <ul className="space-y-2">
                        {request.documents.map((doc) => (
                          <li key={doc.id} className="flex items-center justify-between">
                            <div>
                              <span className="font-medium">{doc.documentType}: </span>
                              <span className="text-gray-700">{doc.fileName}</span>
                            </div>
                            <span className={`text-sm ${doc.verified ? "text-green-600" : "text-gray-600"}`}>
                              {doc.verified ? "Verified" : "Pending"}
                            </span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
