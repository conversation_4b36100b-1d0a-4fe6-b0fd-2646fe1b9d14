import { json, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { useLoaderData, Link, useParams, useFetcher } from "@remix-run/react";
import { getUser } from "~/utils/session.server";
import { getTrackingData } from "~/utils/tracking.server";
import { TrackingStatus, FlightStatus } from "~/types/verification";
import { useEffect, useState, useRef } from "react";
import { useSwipeable } from "react-swipeable";

export const meta: MetaFunction = () => {
  return [{ title: "Flight Tracking | FerryPros" }];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const currentUser = await getUser(request);

  if (!currentUser) {
    throw new Response("Unauthorized", { status: 401 });
  }

  const jobId = params.jobId;

  if (!jobId) {
    throw new Response("Job ID is required", { status: 400 });
  }

  // Get tracking data for the job
  const trackingData = await getTrackingData(Number(jobId));

  return json({ 
    currentUser, 
    trackingData,
    jobId: Number(jobId)
  });
}

export default function JobTracking() {
  const { currentUser, trackingData, jobId } = useLoaderData<typeof loader>();
  const [mapLoaded, setMapLoaded] = useState(false);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [markers, setMarkers] = useState<google.maps.Marker[]>([]);
  const [flightPath, setFlightPath] = useState<google.maps.Polyline | null>(null);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [mapZoom, setMapZoom] = useState(8);
  const mapRef = useRef<HTMLDivElement>(null);
  const fetcher = useFetcher();

  // Function to refresh tracking data
  const refreshTrackingData = () => {
    if (isRefreshing) return;

    setIsRefreshing(true);
    fetcher.load(`/jobs/${jobId}/tracking`);
  };

  // Handle fetcher state changes
  useEffect(() => {
    if (fetcher.state === 'idle' && fetcher.data) {
      setIsRefreshing(false);
    }
  }, [fetcher.state, fetcher.data]);

  // Function to initialize Google Maps
  const initializeMap = () => {
    if (!trackingData.success || !trackingData.positions || trackingData.positions.length === 0) {
      return;
    }

    // Get the most recent position
    const positions = trackingData.adsbData?.positions || trackingData.positions;
    const mostRecentPosition = positions[positions.length - 1];

    // Create map centered on the most recent position
    const mapElement = mapRef.current || document.getElementById('map');
    if (!mapElement) return;

    const newMap = new google.maps.Map(mapElement, {
      center: { 
        lat: mostRecentPosition.latitude, 
        lng: mostRecentPosition.longitude 
      },
      zoom: mapZoom,
      mapTypeId: google.maps.MapTypeId.TERRAIN,
      gestureHandling: 'greedy', // Makes the map easier to use on mobile
      zoomControl: true,
      mapTypeControl: false,
      streetViewControl: false,
      fullscreenControl: true
    });

    setMap(newMap);

    // Create markers for each position
    const newMarkers: google.maps.Marker[] = [];

    positions.forEach((position, index) => {
      const marker = new google.maps.Marker({
        position: { lat: position.latitude, lng: position.longitude },
        map: newMap,
        title: `Position ${index + 1}`,
        // Only show icon for the most recent position
        icon: index === positions.length - 1 
          ? {
              path: google.maps.SymbolPath.FORWARD_CLOSED_ARROW,
              scale: 5,
              rotation: position.heading
            } 
          : null
      });

      newMarkers.push(marker);
    });

    setMarkers(newMarkers);

    // Create flight path
    const flightPathCoordinates = positions.map(position => ({
      lat: position.latitude,
      lng: position.longitude
    }));

    const newFlightPath = new google.maps.Polyline({
      path: flightPathCoordinates,
      geodesic: true,
      strokeColor: '#FF0000',
      strokeOpacity: 1.0,
      strokeWeight: 2
    });

    newFlightPath.setMap(newMap);
    setFlightPath(newFlightPath);
  };

  // Load Google Maps API
  useEffect(() => {
    if (typeof window !== 'undefined' && !window.google?.maps && !mapLoaded) {
      setMapLoaded(true);

      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.GOOGLE_MAPS_API_KEY || 'YOUR_API_KEY'}&libraries=geometry`;
      script.async = true;
      script.defer = true;
      script.onload = initializeMap;

      document.head.appendChild(script);
    } else if (typeof window !== 'undefined' && window.google?.maps && !map) {
      initializeMap();
    }

    // Cleanup function
    return () => {
      if (markers) {
        markers.forEach(marker => marker.setMap(null));
      }

      if (flightPath) {
        flightPath.setMap(null);
      }
    };
  }, [trackingData, mapLoaded]);

  // Save map zoom level when it changes
  useEffect(() => {
    if (map) {
      const zoomListener = map.addListener('zoom_changed', () => {
        setMapZoom(map.getZoom() || 8);
      });

      return () => {
        google.maps.event.removeListener(zoomListener);
      };
    }
  }, [map]);

  // Set up auto-refresh every 30 seconds if flight is in progress
  useEffect(() => {
    if (trackingData.success && 
        (trackingData.tracking.status === TrackingStatus.IN_PROGRESS || 
         (trackingData.adsbData && trackingData.adsbData.status === FlightStatus.EN_ROUTE))) {
      const refreshInterval = setInterval(refreshTrackingData, 30000);
      return () => clearInterval(refreshInterval);
    }
  }, [trackingData]);

  // Set up swipe handlers for mobile navigation
  const swipeHandlers = useSwipeable({
    onSwipedLeft: () => {
      // Swipe left to see position history (scroll to position history section)
      const historySection = document.getElementById('position-history');
      if (historySection) {
        historySection.scrollIntoView({ behavior: 'smooth' });
      }
    },
    onSwipedRight: () => {
      // Swipe right to see flight info (scroll to flight info section)
      const infoSection = document.getElementById('flight-info');
      if (infoSection) {
        infoSection.scrollIntoView({ behavior: 'smooth' });
      }
    },
    preventDefaultTouchmoveEvent: true,
    trackMouse: false
  });

  // Function to format date
  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A';
    return new Date(dateString).toLocaleString();
  };

  // Function to get status badge color
  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case TrackingStatus.COMPLETED:
        return "bg-green-100 text-green-800";
      case TrackingStatus.IN_PROGRESS:
        return "bg-blue-100 text-blue-800";
      case TrackingStatus.DELAYED:
        return "bg-yellow-100 text-yellow-800";
      case TrackingStatus.CANCELLED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Function to get flight status badge color
  const getFlightStatusBadgeColor = (status: string) => {
    switch (status) {
      case FlightStatus.LANDED:
        return "bg-green-100 text-green-800";
      case FlightStatus.EN_ROUTE:
        return "bg-blue-100 text-blue-800";
      case FlightStatus.DEPARTED:
        return "bg-blue-100 text-blue-800";
      case FlightStatus.APPROACHING:
        return "bg-yellow-100 text-yellow-800";
      case FlightStatus.TAXIING:
        return "bg-yellow-100 text-yellow-800";
      case FlightStatus.SCHEDULED:
        return "bg-gray-100 text-gray-800";
      case FlightStatus.DIVERTED:
        return "bg-red-100 text-red-800";
      case FlightStatus.CANCELLED:
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Link to={`/jobs/${jobId}`} className="text-blue-600 hover:underline">
            ← Back to Job
          </Link>
          <h1 className="text-xl md:text-2xl font-bold">Flight Tracking</h1>
        </div>

        {trackingData.success && (
          <button 
            onClick={refreshTrackingData}
            disabled={isRefreshing}
            className="flex items-center gap-1 px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm hover:bg-blue-200 transition-colors"
            aria-label="Refresh tracking data"
          >
            <svg className={`w-4 h-4 ${isRefreshing ? 'animate-spin' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
        )}
      </div>

      {!trackingData.success ? (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <p className="text-yellow-800">
            {trackingData.message || "No tracking data available for this job yet."}
          </p>
          <p className="mt-2">
            <Link to={`/jobs/${jobId}`} className="text-blue-600 hover:underline">
              Return to job details
            </Link>
          </p>
        </div>
      ) : (
        <div {...swipeHandlers} className="space-y-6">
          {/* Mobile navigation hint */}
          <div className="md:hidden bg-blue-50 p-3 rounded-lg text-sm text-blue-800 text-center">
            <p>Swipe left/right to navigate between sections</p>
          </div>

          {/* Flight Info Card */}
          <div id="flight-info" className="bg-white shadow rounded-lg p-4 md:p-6">
            <div className="flex justify-between items-start">
              <div>
                <h2 className="text-lg md:text-xl font-semibold">Flight Information</h2>
                <p className="text-gray-600 mt-1">
                  {trackingData.tracking.registration} • {trackingData.tracking.aircraft_type}
                </p>
              </div>
              <div className="text-right">
                <span className={`px-2 md:px-3 py-1 rounded-full text-xs md:text-sm font-medium ${getStatusBadgeColor(trackingData.tracking.status)}`}>
                  {trackingData.tracking.status}
                </span>
                {trackingData.adsbData && (
                  <p className="mt-1">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getFlightStatusBadgeColor(trackingData.adsbData.status)}`}>
                      {trackingData.adsbData.status}
                    </span>
                  </p>
                )}
              </div>
            </div>

            <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium text-gray-700">Origin</h3>
                <p className="mt-1 text-gray-600">{trackingData.tracking.origin}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Destination</h3>
                <p className="mt-1 text-gray-600">{trackingData.tracking.destination}</p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Departure Time</h3>
                <p className="mt-1 text-gray-600">
                  {formatDate(trackingData.tracking.departure_time || (trackingData.adsbData?.departureTime as any))}
                </p>
              </div>
              <div>
                <h3 className="font-medium text-gray-700">Estimated Arrival</h3>
                <p className="mt-1 text-gray-600">
                  {formatDate(trackingData.tracking.estimated_arrival_time || (trackingData.adsbData?.estimatedArrivalTime as any))}
                </p>
              </div>
            </div>
          </div>

          {/* Map Card */}
          <div className="bg-white shadow rounded-lg p-4 md:p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg md:text-xl font-semibold">Flight Path</h2>

              {/* Map controls for mobile */}
              <div className="flex md:hidden gap-2">
                <button 
                  onClick={() => map?.setZoom((map.getZoom() || 8) + 1)}
                  className="p-1 bg-white border border-gray-300 rounded-full shadow-sm"
                  aria-label="Zoom in"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </button>
                <button 
                  onClick={() => map?.setZoom((map.getZoom() || 8) - 1)}
                  className="p-1 bg-white border border-gray-300 rounded-full shadow-sm"
                  aria-label="Zoom out"
                >
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </button>
              </div>
            </div>

            {/* Responsive map container */}
            <div 
              id="map" 
              ref={mapRef}
              className="w-full h-64 md:h-96 rounded-lg border"
              style={{ touchAction: 'pan-x pan-y' }}
            ></div>

            {/* Position Details */}
            {trackingData.adsbData && trackingData.adsbData.positions.length > 0 && (
              <div className="mt-4">
                <h3 className="font-medium text-gray-700 mb-2">Current Position</h3>
                <div className="bg-gray-50 p-3 md:p-4 rounded-lg">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-3 md:gap-4">
                    <div>
                      <p className="text-xs md:text-sm text-gray-500">Latitude</p>
                      <p className="font-medium text-sm md:text-base">{trackingData.adsbData.positions[trackingData.adsbData.positions.length - 1].latitude.toFixed(4)}</p>
                    </div>
                    <div>
                      <p className="text-xs md:text-sm text-gray-500">Longitude</p>
                      <p className="font-medium text-sm md:text-base">{trackingData.adsbData.positions[trackingData.adsbData.positions.length - 1].longitude.toFixed(4)}</p>
                    </div>
                    <div>
                      <p className="text-xs md:text-sm text-gray-500">Altitude</p>
                      <p className="font-medium text-sm md:text-base">{trackingData.adsbData.positions[trackingData.adsbData.positions.length - 1].altitude.toFixed(0)} ft</p>
                    </div>
                    <div>
                      <p className="text-xs md:text-sm text-gray-500">Ground Speed</p>
                      <p className="font-medium text-sm md:text-base">{trackingData.adsbData.positions[trackingData.adsbData.positions.length - 1].groundSpeed.toFixed(0)} kts</p>
                    </div>
                  </div>
                  <p className="text-xs md:text-sm text-gray-500 mt-2">
                    Last updated: {formatDate(trackingData.adsbData.positions[trackingData.adsbData.positions.length - 1].timestamp as any)}
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Position History */}
          <div id="position-history" className="bg-white shadow rounded-lg p-4 md:p-6">
            <h2 className="text-lg md:text-xl font-semibold mb-4">Position History</h2>

            {(trackingData.positions && trackingData.positions.length > 0) || (trackingData.adsbData && trackingData.adsbData.positions.length > 0) ? (
              <div className="overflow-x-auto">
                {/* Mobile view: Cards instead of table */}
                <div className="md:hidden space-y-4">
                  {(trackingData.adsbData?.positions || trackingData.positions).map((position, index) => (
                    <div key={index} className={`p-3 rounded-lg border ${index === 0 ? "bg-blue-50 border-blue-200" : "bg-white border-gray-200"}`}>
                      <div className="grid grid-cols-2 gap-2">
                        <div>
                          <p className="text-xs text-gray-500">Time</p>
                          <p className="text-sm">{formatDate(position.timestamp as any)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Speed</p>
                          <p className="text-sm">{position.groundSpeed ? `${position.groundSpeed.toFixed(0)} kts` : 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Altitude</p>
                          <p className="text-sm">{position.altitude ? `${position.altitude.toFixed(0)} ft` : 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Heading</p>
                          <p className="text-sm">{position.heading ? `${position.heading.toFixed(0)}°` : 'N/A'}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Latitude</p>
                          <p className="text-sm">{position.latitude.toFixed(4)}</p>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500">Longitude</p>
                          <p className="text-sm">{position.longitude.toFixed(4)}</p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Desktop view: Table */}
                <table className="hidden md:table min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Latitude</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Longitude</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Altitude</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Speed</th>
                      <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Heading</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {(trackingData.adsbData?.positions || trackingData.positions).map((position, index) => (
                      <tr key={index} className={index === 0 ? "bg-blue-50" : ""}>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {formatDate(position.timestamp as any)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {position.latitude.toFixed(4)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {position.longitude.toFixed(4)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {position.altitude ? `${position.altitude.toFixed(0)} ft` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {position.groundSpeed ? `${position.groundSpeed.toFixed(0)} kts` : 'N/A'}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {position.heading ? `${position.heading.toFixed(0)}°` : 'N/A'}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            ) : (
              <p className="text-gray-500">No position history available.</p>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
