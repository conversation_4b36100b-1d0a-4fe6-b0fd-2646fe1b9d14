import { json, LoaderFunctionArgs } from "@remix-run/node";
import { Link, Outlet, useLoaderData, useLocation } from "@remix-run/react";
import { requireUser } from "~/utils/session.server";
import { getInboxMessages, getUnreadMessageCount } from "~/utils/messaging.server";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const unreadCount = await getUnreadMessageCount(user.id);
  
  return json({
    user,
    unreadCount
  });
}

export default function Messages() {
  const { user, unreadCount } = useLoaderData<typeof loader>();
  const location = useLocation();
  
  // Determine which tab is active
  const currentPath = location.pathname;
  const isInbox = currentPath === "/messages" || currentPath === "/messages/inbox";
  const isSent = currentPath === "/messages/sent";
  const isTrash = currentPath === "/messages/trash";
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Messages</h1>
      
      {/* Tabs for different message folders */}
      <div className="border-b border-gray-200 mb-6">
        <nav className="flex -mb-px">
          <Link
            to="/messages/inbox"
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              isInbox
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Inbox {unreadCount > 0 && <span className="ml-2 bg-blue-500 text-white rounded-full px-2 py-1 text-xs">{unreadCount}</span>}
          </Link>
          
          <Link
            to="/messages/sent"
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              isSent
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Sent
          </Link>
          
          <Link
            to="/messages/trash"
            className={`py-4 px-6 border-b-2 font-medium text-sm ${
              isTrash
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
            }`}
          >
            Trash
          </Link>
        </nav>
      </div>
      
      {/* Outlet for nested routes */}
      <Outlet />
    </div>
  );
}