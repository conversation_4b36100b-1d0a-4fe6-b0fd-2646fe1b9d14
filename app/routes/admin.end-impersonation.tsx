import { redirect } from "@remix-run/node";
import { endImpersonation, requireUser } from "~/utils/session.server";
import type { ActionFunctionArgs } from "@remix-run/node";

export async function action({ request }: ActionFunctionArgs) {
  // Ensure user is logged in
  await requireUser(request);
  
  // End impersonation and redirect to dashboard
  return endImpersonation(request);
}

// This route doesn't render anything, it just handles the action
export async function loader() {
  return redirect("/dashboard");
}