import { json, redirect, ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useActionData, useLoaderData, Link, useFetcher } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { createMarineJob, getVesselTypes, groupVesselTypesByCategory } from "~/utils/marine.server";
import { useState, useEffect, useRef } from "react";
import { canFeatureJob } from "~/utils/subscriptions.server";

export const meta: MetaFunction = () => {
  return [{ title: "Post New Marine Job | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Ensure the user is logged in
  const userId = await requireUserId(request);

  // Get the user to check their role
  const user = await getUser(request);

  // If the user is not an owner, redirect to the dashboard
  if (user?.role !== "owner" && user?.role !== "admin") {
    return redirect("/dashboard");
  }

  // Check if the user can feature jobs based on their subscription
  const canFeature = await canFeatureJob(parseInt(userId));

  // Get vessel types for the dropdown
  const vesselTypes = await getVesselTypes();

  // Group vessel types by category on the server side
  const vesselTypesByCategory = groupVesselTypesByCategory(vesselTypes);

  // Convert to the format needed for the dropdown
  const formattedVesselTypesByCategory: Record<string, Array<{value: string, label: string}>> = {};
  Object.entries(vesselTypesByCategory).forEach(([category, types]) => {
    formattedVesselTypesByCategory[category] = types.map(type => ({
      value: type.name,
      label: type.name
    }));
  });

  return json({
    user,
    canFeature,
    vesselTypes,
    formattedVesselTypesByCategory
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  // Ensure the user is an owner
  if (user?.role !== "owner") {
    return json({ error: "Only vessel owners can post marine jobs" }, { status: 403 });
  }

  const formData = await request.formData();
  const title = formData.get("title");
  const description = formData.get("description");
  const vesselType = formData.get("vesselType");
  const vesselLength = formData.get("vesselLength");
  const vesselDraft = formData.get("vesselDraft");
  const origin = formData.get("origin");
  const destination = formData.get("destination");
  const estimatedDate = formData.get("estimatedDate");
  const budget = formData.get("budget");
  const requirements = formData.get("requirements");
  const isFeatured = formData.get("isFeatured") === "true";

  // Validate the form data
  const errors = {
    title: title ? null : "Title is required",
    description: description ? null : "Description is required",
    vesselType: vesselType ? null : "Vessel type is required",
    origin: origin ? null : "Origin is required",
    destination: destination ? null : "Destination is required",
    estimatedDate: estimatedDate ? null : "Estimated date is required",
  };

  // Return errors if any fields are invalid
  const hasErrors = Object.values(errors).some(error => error);
  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Check if the user can feature jobs if they're trying to feature this job
  if (isFeatured) {
    const canFeature = await canFeatureJob(parseInt(userId));
    if (!canFeature) {
      return json({ 
        error: "Your subscription plan does not include featured listings. Please upgrade your plan to feature jobs.",
        values: Object.fromEntries(formData)
      }, { status: 403 });
    }
  }

  // Create the marine job in the database
  try {
    const vesselDetails = {
      length: vesselLength ? parseFloat(vesselLength.toString()) : null,
      draft: vesselDraft ? parseFloat(vesselDraft.toString()) : null
    };

    const newJob = await createMarineJob({
      owner_id: parseInt(userId),
      title: title?.toString() || "",
      description: description?.toString() || "",
      vessel_type: vesselType?.toString() || "",
      vessel_length: vesselLength ? parseFloat(vesselLength.toString()) : undefined,
      vessel_draft: vesselDraft ? parseFloat(vesselDraft.toString()) : undefined,
      vessel_details: vesselDetails,
      origin: origin?.toString() || "",
      destination: destination?.toString() || "",
      estimated_date: estimatedDate?.toString() || "",
      budget: budget ? parseFloat(budget.toString()) : undefined,
      requirements: requirements?.toString(),
      is_featured: isFeatured,
      featured_duration_days: isFeatured ? 30 : undefined // Default to 30 days for featured listings
    });

    return redirect(`/marine/jobs/${newJob.id}?created=true`);
  } catch (error) {
    console.error("Error creating marine job:", error);
    return json({ 
      error: "Failed to create marine job. Please try again.", 
      values: Object.fromEntries(formData) 
    }, { status: 500 });
  }
}

export default function NewMarineJob() {
  const { user, canFeature, vesselTypes, formattedVesselTypesByCategory } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [formValues, setFormValues] = useState(actionData?.values || {});
  const [isFeatured, setIsFeatured] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/marine" className="text-blue-600 hover:underline">
          ← Back to Marine
        </Link>
        <h1 className="text-2xl font-bold">Post a New Marine Transport Job</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        {actionData?.error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded mb-4">
            <p>{actionData.error}</p>
          </div>
        )}

        <Form method="post" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block font-medium text-gray-700 mb-1">
                  Job Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.title}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.title ? true : undefined}
                  aria-errormessage={actionData?.errors?.title ? "title-error" : undefined}
                />
                {actionData?.errors?.title && (
                  <p className="text-red-500 text-sm mt-1" id="title-error">
                    {actionData.errors.title}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="vesselType" className="block font-medium text-gray-700 mb-1">
                  Vessel Type
                </label>
                <select
                  id="vesselType"
                  name="vesselType"
                  className="w-full border rounded-md px-3 py-2"
                  value={formValues.vesselType || ""}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.vesselType ? true : undefined}
                  aria-errormessage={actionData?.errors?.vesselType ? "vesselType-error" : undefined}
                >
                  <option value="">Select a vessel type</option>
                  {Object.entries(formattedVesselTypesByCategory).map(([category, types]) => (
                    <optgroup key={category} label={category}>
                      {types.map((type: any) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </optgroup>
                  ))}
                </select>
                {actionData?.errors?.vesselType && (
                  <p className="text-red-500 text-sm mt-1" id="vesselType-error">
                    {actionData.errors.vesselType}
                  </p>
                )}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label htmlFor="vesselLength" className="block font-medium text-gray-700 mb-1">
                    Vessel Length (feet)
                  </label>
                  <input
                    type="number"
                    id="vesselLength"
                    name="vesselLength"
                    className="w-full border rounded-md px-3 py-2"
                    defaultValue={formValues.vesselLength}
                    onChange={handleInputChange}
                    min="0"
                    step="0.1"
                  />
                </div>

                <div>
                  <label htmlFor="vesselDraft" className="block font-medium text-gray-700 mb-1">
                    Vessel Draft (feet)
                  </label>
                  <input
                    type="number"
                    id="vesselDraft"
                    name="vesselDraft"
                    className="w-full border rounded-md px-3 py-2"
                    defaultValue={formValues.vesselDraft}
                    onChange={handleInputChange}
                    min="0"
                    step="0.1"
                  />
                </div>
              </div>

              <div>
                <label htmlFor="origin" className="block font-medium text-gray-700 mb-1">
                  Origin (Port/Marina)
                </label>
                <input
                  type="text"
                  id="origin"
                  name="origin"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.origin}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.origin ? true : undefined}
                  aria-errormessage={actionData?.errors?.origin ? "origin-error" : undefined}
                />
                {actionData?.errors?.origin && (
                  <p className="text-red-500 text-sm mt-1" id="origin-error">
                    {actionData.errors.origin}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="destination" className="block font-medium text-gray-700 mb-1">
                  Destination (Port/Marina)
                </label>
                <input
                  type="text"
                  id="destination"
                  name="destination"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.destination}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.destination ? true : undefined}
                  aria-errormessage={actionData?.errors?.destination ? "destination-error" : undefined}
                />
                {actionData?.errors?.destination && (
                  <p className="text-red-500 text-sm mt-1" id="destination-error">
                    {actionData.errors.destination}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="estimatedDate" className="block font-medium text-gray-700 mb-1">
                  Estimated Date
                </label>
                <input
                  type="date"
                  id="estimatedDate"
                  name="estimatedDate"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.estimatedDate}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.estimatedDate ? true : undefined}
                  aria-errormessage={actionData?.errors?.estimatedDate ? "estimatedDate-error" : undefined}
                />
                {actionData?.errors?.estimatedDate && (
                  <p className="text-red-500 text-sm mt-1" id="estimatedDate-error">
                    {actionData.errors.estimatedDate}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="budget" className="block font-medium text-gray-700 mb-1">
                  Budget (USD)
                </label>
                <input
                  type="number"
                  id="budget"
                  name="budget"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.budget}
                  onChange={handleInputChange}
                  min="0"
                  step="100"
                />
              </div>

              {canFeature && (
                <div className="mt-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isFeatured"
                      name="isFeatured"
                      value="true"
                      checked={isFeatured}
                      onChange={(e) => setIsFeatured(e.target.checked)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                      Feature this job (Premium)
                    </label>
                  </div>
                  <p className="text-sm text-gray-500 mt-1">
                    Featured jobs appear at the top of search results and get more visibility.
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="description" className="block font-medium text-gray-700 mb-1">
                  Job Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.description}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.description ? true : undefined}
                  aria-errormessage={actionData?.errors?.description ? "description-error" : undefined}
                ></textarea>
                {actionData?.errors?.description && (
                  <p className="text-red-500 text-sm mt-1" id="description-error">
                    {actionData.errors.description}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="requirements" className="block font-medium text-gray-700 mb-1">
                  Captain Requirements
                </label>
                <textarea
                  id="requirements"
                  name="requirements"
                  rows={4}
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.requirements}
                  onChange={handleInputChange}
                  placeholder="E.g., Required certifications, experience with specific vessel types, navigation experience"
                ></textarea>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Link
              to="/marine"
              className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Post Marine Job
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
}
