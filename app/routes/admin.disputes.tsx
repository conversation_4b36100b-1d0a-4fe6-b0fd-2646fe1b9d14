import { json, redirect, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { useLoaderData, Link } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { getDisputesByStatus } from "~/utils/payment.server";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [{ title: "Admin Disputes | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  // Ensure the user is an admin
  if (user?.role !== "admin") {
    return redirect("/dashboard");
  }

  // Get URL parameters for filtering
  const url = new URL(request.url);
  const statusFilter = url.searchParams.get("status") || "open";

  // Get disputes based on status filter
  const disputes = await getDisputesByStatus(statusFilter);

  return json({ 
    user,
    disputes,
    statusFilter
  });
}

export default function AdminDisputes() {
  const { user, disputes, statusFilter } = useLoaderData<typeof loader>();
  const [filter, setFilter] = useState(statusFilter);

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Handle filter change
  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setFilter(e.target.value);
    window.location.href = `/admin/disputes?status=${e.target.value}`;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Admin Dispute Management</h1>
        <div className="flex items-center gap-2">
          <label htmlFor="status-filter" className="text-sm font-medium text-gray-700">
            Filter by Status:
          </label>
          <select
            id="status-filter"
            className="border rounded-md px-3 py-1"
            value={filter}
            onChange={handleFilterChange}
          >
            <option value="open">Open</option>
            <option value="under_review">Under Review</option>
            <option value="resolved">Resolved</option>
            <option value="cancelled">Cancelled</option>
            <option value="all">All</option>
          </select>
        </div>
      </div>

      <div className="bg-white shadow rounded-lg overflow-hidden">
        {disputes.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            No disputes found with status: {statusFilter === 'all' ? 'any' : statusFilter.replace('_', ' ')}
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Job ID
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Reason
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {disputes.map((dispute) => (
                  <tr key={dispute.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {dispute.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {dispute.job_id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                        dispute.status === 'open' 
                          ? 'bg-red-100 text-red-800' 
                          : dispute.status === 'under_review'
                          ? 'bg-yellow-100 text-yellow-800'
                          : dispute.status === 'resolved'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {dispute.status.toUpperCase().replace('_', ' ')}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {dispute.reason.replace('_', ' ').toUpperCase()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {formatDate(dispute.created_at)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <Link 
                        to={`/marine/jobs/${dispute.job_id}/disputes/${dispute.id}`}
                        className="text-blue-600 hover:text-blue-900"
                      >
                        View Details
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div className="bg-gray-50 p-4 rounded-lg border">
        <h2 className="text-lg font-semibold mb-2">About Dispute Management</h2>
        <p className="text-gray-700">
          As an administrator, you can review and manage all disputes in the system. 
          Use the filter to view disputes by status. Click "View Details" to see the full 
          dispute information and take actions such as updating the status or resolving the dispute.
        </p>
      </div>
    </div>
  );
}