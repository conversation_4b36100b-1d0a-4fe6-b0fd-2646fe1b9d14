import type { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "FerryPros - Connecting Aircraft Owners and Brokers with Trusted Service Providers and Pilots" },
    { name: "description", content: "FerryPros is a marketplace connecting aircraft owners, brokers, and Ferry Pilot Agencies with trusted pilots through secure escrow and real-time tracking." },
  ];
};

export default function Index() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-primary-900 text-white overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-900 to-primary-800"></div>
          <div className="absolute inset-0" style={{ 
            backgroundImage: "url('https://images.unsplash.com/photo-1464037866556-6812c9d1c72e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80')",
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            mixBlendMode: 'overlay'
          }}></div>
        </div>
        <div className="container-custom relative z-10 py-20 md:py-32">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6 leading-tight">
              Welcome to FerryPros
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-10 leading-relaxed">
              The premier marketplace connecting aircraft owners and brokers with trusted service providers and pilots through secure escrow and real-time tracking.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="/register" className="btn-primary text-lg px-8 py-3">
                Get Started
              </a>
              <a href="/about" className="btn-secondary text-lg px-8 py-3">
                Learn More
              </a>
            </div>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-900 to-transparent"></div>
      </section>

      <section className="py-20 bg-gray-900">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-4">How It Works</h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Our platform simplifies the aircraft ferry process with a secure, transparent system.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-10">
            <div className="card hover:shadow-lg transition-shadow duration-300">
              <div className="rounded-full bg-primary-900 w-16 h-16 flex items-center justify-center mb-6 text-primary-300">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                </svg>
              </div>
              <h3 className="text-xl font-display font-semibold mb-3">For Aircraft Owners</h3>
              <p className="text-gray-300">Post your ferry job, fund the secure escrow, and select from qualified pilots. Track your aircraft in real-time throughout the journey.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300">
              <div className="rounded-full bg-primary-900 w-16 h-16 flex items-center justify-center mb-6 text-primary-300">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-display font-semibold mb-3">For Pilots</h3>
              <p className="text-gray-300">Browse available jobs, apply with your credentials, and get paid securely upon completion. Build your reputation with every successful ferry.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300">
              <div className="rounded-full bg-primary-900 w-16 h-16 flex items-center justify-center mb-6 text-primary-300">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-display font-semibold mb-3">For Aircraft Brokers</h3>
              <p className="text-gray-300">Manage multiple aircraft and ferry jobs for your clients. Streamline your operations with our comprehensive management tools and secure payment system.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300">
              <div className="rounded-full bg-primary-900 w-16 h-16 flex items-center justify-center mb-6 text-primary-300">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                </svg>
              </div>
              <h3 className="text-xl font-display font-semibold mb-3">For Ferry Pilot Agencies</h3>
              <p className="text-gray-300">Manage your team of pilots and coordinate multiple ferry jobs. Access specialized tools to optimize your operations and grow your ferry pilot agency.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300 md:col-span-2 mt-4">
              <div className="rounded-full bg-primary-900 w-16 h-16 flex items-center justify-center mb-6 text-primary-300">
                <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <h3 className="text-xl font-display font-semibold mb-3">Secure Transactions</h3>
              <p className="text-gray-300">Our escrow system ensures funds are only released when jobs are completed successfully, protecting all parties involved in the ferry process.</p>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gray-800">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-4">Why Choose FerryPros?</h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              We've built a platform that prioritizes safety, security, and transparency.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-x-12 gap-y-10">
            <div className="flex gap-6">
              <div className="flex-shrink-0 w-14 h-14 bg-primary-900 rounded-full flex items-center justify-center text-primary-300">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-display font-semibold mb-3">Verified Pilots</h3>
                <p className="text-gray-300">All pilots are thoroughly vetted with background checks and credential verification, ensuring you work with only the most qualified professionals.</p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-14 h-14 bg-primary-900 rounded-full flex items-center justify-center text-primary-300">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-display font-semibold mb-3">Secure Payments</h3>
                <p className="text-gray-300">Our escrow system ensures fair transactions for both parties, with funds only released when jobs are completed to satisfaction.</p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-14 h-14 bg-primary-900 rounded-full flex items-center justify-center text-primary-300">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 20l-5.447-2.724A1 1 0 013 16.382V5.618a1 1 0 011.447-.894L9 7m0 13l6-3m-6 3V7m6 10l4.553 2.276A1 1 0 0021 18.382V7.618a1 1 0 00-.553-.894L15 4m0 13V4m0 0L9 7" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-display font-semibold mb-3">Real-time Tracking</h3>
                <p className="text-gray-300">Monitor your aircraft's journey with integrated ADS-B tracking, providing peace of mind throughout the entire ferry process.</p>
              </div>
            </div>

            <div className="flex gap-6">
              <div className="flex-shrink-0 w-14 h-14 bg-primary-900 rounded-full flex items-center justify-center text-primary-300">
                <svg className="w-7 h-7" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
              <div>
                <h3 className="text-xl font-display font-semibold mb-3">Transparent Ratings</h3>
                <p className="text-gray-300">Make informed decisions based on verified reviews and ratings from other aircraft owners and pilots in our community.</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <section className="py-20 bg-gradient-to-r from-primary-800 to-primary-900 text-white text-center">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">Ready to Get Started?</h2>
            <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto mb-10">
              Join the FerryPros community today as an aircraft owner, broker, pilot, or Ferry Pilot Agency and experience a new standard in aircraft ferry services.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="/register" className="btn-primary bg-gray-800 text-primary-300 hover:bg-gray-700 hover:text-primary-200 text-lg px-8 py-3 border border-primary-700">
                Create Your Account
              </a>
              <a href="/contact" className="btn-secondary border-gray-700 text-gray-200 hover:bg-primary-900 hover:text-primary-300 text-lg px-8 py-3">
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
