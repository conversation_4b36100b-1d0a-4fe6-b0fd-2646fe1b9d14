import { ActionFunction<PERSON><PERSON><PERSON>, j<PERSON>, <PERSON><PERSON>Function<PERSON>rgs, MetaFunction } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useEffect, useState } from "react";
import { getUser } from "~/utils/session.server";
import { isUserAdmin } from "~/utils/users.server";
import { pool } from "~/utils/db.server";
import fs from "fs";
import path from "path";
import { fileURLToPath } from "url";

export const meta: MetaFunction = () => {
  return [{ title: "Database Migrations | FerryPros Admin" }];
};

// Function to get all migration files
function getMigrationFiles() {
  const migrationsDir = path.join(process.cwd(), "migrations");
  return fs.readdirSync(migrationsDir)
    .filter(file => (file.endsWith('.js') || file.endsWith('.ts')) && 
                    file !== 'migration-runner.js' && 
                    file !== 'migration-runner.ts' &&
                    file !== 'run-migrations.js' &&
                    file !== 'run-migrations.ts')
    .sort();
}

// Function to get all migrations from the database
async function getDatabaseMigrations() {
  const result = await pool.query('SELECT id, name, applied_at, skipped FROM migrations ORDER BY id');
  return result.rows;
}

// Function to run a specific migration
async function runMigration(migrationName: string) {
  try {
    // Import the migration file
    const migrationPath = path.join(process.cwd(), "migrations", migrationName);
    const migrationModule = await import(migrationPath);

    // Handle both direct function exports and object exports
    const migration = {
      up: typeof migrationModule.up === 'function' ? migrationModule.up : migrationModule.default?.up,
    };

    // Start a transaction
    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Apply the migration
      await migration.up(client);

      // Record the migration
      await client.query(
        'INSERT INTO migrations (name, skipped) VALUES ($1, $2)',
        [migrationName, false]
      );

      await client.query('COMMIT');
      return { success: true, message: `Migration applied: ${migrationName}` };
    } catch (error) {
      await client.query('ROLLBACK');
      return { success: false, message: `Error applying migration ${migrationName}: ${error.message}` };
    } finally {
      client.release();
    }
  } catch (error) {
    return { success: false, message: `Error importing migration ${migrationName}: ${error.message}` };
  }
}

// Function to skip a migration
async function skipMigration(migrationName: string) {
  try {
    // Record the migration as skipped
    await pool.query(
      'INSERT INTO migrations (name, skipped) VALUES ($1, $2)',
      [migrationName, true]
    );
    
    return { success: true, message: `Migration skipped: ${migrationName}` };
  } catch (error) {
    return { success: false, message: `Error skipping migration ${migrationName}: ${error.message}` };
  }
}

export async function loader({ request }: LoaderFunctionArgs) {
  // Get the current user
  const user = await getUser(request);

  // If not logged in, redirect to login
  if (!user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // Check if user is an admin
  const isAdmin = await isUserAdmin(user.id);
  if (!isAdmin) {
    throw new Response("Forbidden", { status: 403 });
  }

  // Get all migration files
  const migrationFiles = getMigrationFiles();

  // Get all migrations from the database
  const databaseMigrations = await getDatabaseMigrations();

  // Normalize filenames by removing extensions for comparison
  const normalizeFilename = (filename: string): string => filename.replace(/\.(js|ts)$/, '');
  
  // Create a map of applied migrations for easy lookup
  const appliedMigrationsMap = new Map();
  databaseMigrations.forEach(migration => {
    appliedMigrationsMap.set(normalizeFilename(migration.name), {
      id: migration.id,
      appliedAt: migration.applied_at,
      skipped: migration.skipped
    });
  });

  // Create a list of all migrations with their status
  const migrations = migrationFiles.map(file => {
    const normalizedName = normalizeFilename(file);
    const appliedMigration = appliedMigrationsMap.get(normalizedName);
    
    return {
      name: file,
      normalizedName,
      status: appliedMigration 
        ? (appliedMigration.skipped ? 'skipped' : 'applied') 
        : 'pending',
      appliedAt: appliedMigration?.appliedAt || null,
      id: appliedMigration?.id || null
    };
  });

  return json({
    migrations
  });
}

export async function action({ request }: ActionFunctionArgs) {
  // Get the current user
  const user = await getUser(request);

  // If not logged in, return unauthorized
  if (!user) {
    throw new Response("Unauthorized", { status: 401 });
  }

  // Check if user is an admin
  const isAdmin = await isUserAdmin(user.id);
  if (!isAdmin) {
    throw new Response("Forbidden", { status: 403 });
  }

  const formData = await request.formData();
  const action = formData.get("action") as string;
  const migrationName = formData.get("migrationName") as string;

  if (!migrationName) {
    return json({ success: false, message: "Migration name is required" });
  }

  if (action === "run") {
    const result = await runMigration(migrationName);
    return json(result);
  }

  if (action === "skip") {
    const result = await skipMigration(migrationName);
    return json(result);
  }

  return json({ success: false, message: "Invalid action" });
}

export default function MigrationsManagement() {
  const { migrations } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [refreshKey, setRefreshKey] = useState(0);

  // Auto-refresh the page when an action completes
  useEffect(() => {
    if (actionData?.success) {
      // Wait a moment to show the success message
      const timer = setTimeout(() => {
        setRefreshKey(prev => prev + 1);
      }, 1000);

      return () => clearTimeout(timer);
    }
  }, [actionData]);

  return (
    <div className="max-w-4xl mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-6 text-center">Database Migrations Management</h1>

      {actionData && (
        <div className={`mb-4 p-3 rounded-md ${
          actionData.success ? "bg-green-50 border border-green-200" : "bg-red-50 border border-red-200"
        }`}>
          <p className={actionData.success ? "text-green-800" : "text-red-800"}>
            {actionData.message}
          </p>
        </div>
      )}

      <div className="mb-6">
        <h2 className="text-xl font-semibold mb-4">Migrations</h2>
        
        <div className="overflow-x-auto">
          <table className="min-w-full bg-white border border-gray-200">
            <thead>
              <tr>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Migration
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Status
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Applied At
                </th>
                <th className="py-2 px-4 border-b border-gray-200 bg-gray-50 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              {migrations.map((migration) => (
                <tr key={migration.name}>
                  <td className="py-2 px-4 border-b border-gray-200">{migration.name}</td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    <span className={`px-2 py-1 rounded text-xs ${
                      migration.status === 'applied' ? 'bg-green-100 text-green-800' : 
                      migration.status === 'skipped' ? 'bg-yellow-100 text-yellow-800' : 
                      'bg-blue-100 text-blue-800'
                    }`}>
                      {migration.status}
                    </span>
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    {migration.appliedAt 
                      ? new Date(migration.appliedAt).toLocaleString() 
                      : 'N/A'}
                  </td>
                  <td className="py-2 px-4 border-b border-gray-200">
                    {migration.status === 'pending' && (
                      <div className="flex space-x-2">
                        <Form method="post">
                          <input type="hidden" name="migrationName" value={migration.name} />
                          <input type="hidden" name="action" value="run" />
                          <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 text-sm"
                          >
                            Run
                          </button>
                        </Form>
                        <Form method="post">
                          <input type="hidden" name="migrationName" value={migration.name} />
                          <input type="hidden" name="action" value="skip" />
                          <button
                            type="submit"
                            disabled={isSubmitting}
                            className="px-3 py-1 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm"
                          >
                            Skip
                          </button>
                        </Form>
                      </div>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      <div className="mt-6 text-center">
        <a href="/admin/dashboard" className="text-sm text-blue-600 hover:text-blue-500">
          Return to Admin Dashboard
        </a>
      </div>
    </div>
  );
}