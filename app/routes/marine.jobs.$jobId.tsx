import { json, LoaderFunctionArgs, ActionFunctionArgs, MetaFunction } from "@remix-run/node";
import { useLoaderData, Link, useSearchParams, Form, useFetcher } from "@remix-run/react";
import { getUser } from "~/utils/session.server";
import { getMarineJobById } from "~/utils/marine.server";
import { getActiveUpsellItems, getUpsellItemsForJob, addUpsellItemToJob, removeUpsellItemFromJob, calculateUpsellItemsTotal } from "~/utils/upsellItems.server";
import { getDisputesForJob, getEscrowAccountForJob } from "~/utils/payment.server";

export const meta: MetaFunction = () => {
  return [{ title: "Marine Job Details | FerryPros" }];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await getUser(request);
  const jobId = params.jobId;

  if (!jobId) {
    throw new Response("Job ID is required", { status: 400 });
  }

  // Fetch the job from the database
  const job = await getMarineJobById(parseInt(jobId));

  if (!job) {
    throw new Response("Marine job not found", { status: 404 });
  }

  // Get the owner information
  // In a real implementation, this would fetch the owner from the database
  // For now, we'll use mock data
  const ownerName = job.owner_id % 2 === 0 ? "Marine Solutions Inc." : "Pacific Northwest Marine";

  // Add the owner name to the job object
  const jobWithOwner = {
    ...job,
    owner: ownerName
  };

  // Get all active upsell items
  const activeUpsellItems = await getActiveUpsellItems();

  // Get upsell items for this job
  const jobUpsellItems = await getUpsellItemsForJob(parseInt(jobId));

  // Calculate total price of upsell items
  const upsellItemsTotal = await calculateUpsellItemsTotal(parseInt(jobId));

  // Get escrow account for this job
  const escrowAccount = await getEscrowAccountForJob(parseInt(jobId));

  // Get disputes for this job
  const disputes = await getDisputesForJob(parseInt(jobId));

  // Check if there's an active dispute
  const activeDispute = disputes.find(d => d.status === 'open' || d.status === 'under_review');

  return json({ 
    job: jobWithOwner, 
    user, 
    activeUpsellItems,
    jobUpsellItems,
    upsellItemsTotal,
    escrowAccount,
    disputes,
    activeDispute
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const user = await getUser(request);
  const jobId = params.jobId;

  if (!jobId) {
    return json({ error: "Job ID is required" }, { status: 400 });
  }

  // Only allow owners to modify upsell items
  if (user?.role !== "owner") {
    return json({ error: "Only owners can modify upsell items" }, { status: 403 });
  }

  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "add-upsell-item") {
    const upsellItemId = formData.get("upsellItemId");
    const quantity = formData.get("quantity") || "1";

    if (!upsellItemId) {
      return json({ error: "Upsell item ID is required" }, { status: 400 });
    }

    try {
      await addUpsellItemToJob(
        parseInt(jobId),
        parseInt(upsellItemId.toString()),
        parseInt(quantity.toString())
      );

      return json({ success: true });
    } catch (error) {
      return json({ error: "Failed to add upsell item" }, { status: 500 });
    }
  } else if (action === "remove-upsell-item") {
    const upsellItemId = formData.get("upsellItemId");

    if (!upsellItemId) {
      return json({ error: "Upsell item ID is required" }, { status: 400 });
    }

    try {
      await removeUpsellItemFromJob(
        parseInt(jobId),
        parseInt(upsellItemId.toString())
      );

      return json({ success: true });
    } catch (error) {
      return json({ error: "Failed to remove upsell item" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
}

export default function MarineJobDetails() {
  const { 
    job, 
    user, 
    activeUpsellItems, 
    jobUpsellItems, 
    upsellItemsTotal,
    escrowAccount,
    disputes,
    activeDispute
  } = useLoaderData<typeof loader>();
  const isPilot = user?.role === "pilot";
  const isOwner = user?.role === "owner";
  const isAdmin = user?.role === "admin";
  const upsellFetcher = useFetcher();

  // Check if the user is the job owner or the assigned pilot
  const isJobOwner = user?.id === job.owner_id;
  const isJobPilot = user?.id === job.pilot_id;

  // Determine if the user can create a dispute
  const canCreateDispute = (isJobOwner || isJobPilot) && 
                          job.status === "in_progress" && 
                          escrowAccount?.status === "active" &&
                          !activeDispute;

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/marine/jobs" className="text-blue-600 hover:underline">
          ← Back to Marine Jobs
        </Link>
        <h1 className="text-2xl font-bold">{job.title}</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <div className="grid md:grid-cols-2 gap-6">
          <div>
            <h2 className="text-xl font-semibold mb-4">Job Details</h2>
            <div className="space-y-3">
              <div>
                <span className="font-medium text-gray-600">Vessel Type:</span> {job.vessel_type}
              </div>
              {job.vessel_length && (
                <div>
                  <span className="font-medium text-gray-600">Vessel Length:</span> {job.vessel_length} ft
                </div>
              )}
              {job.vessel_draft && (
                <div>
                  <span className="font-medium text-gray-600">Vessel Draft:</span> {job.vessel_draft} ft
                </div>
              )}
              <div>
                <span className="font-medium text-gray-600">Origin:</span> {job.origin}
              </div>
              <div>
                <span className="font-medium text-gray-600">Destination:</span> {job.destination}
              </div>
              <div>
                <span className="font-medium text-gray-600">Estimated Date:</span> {new Date(job.estimated_date).toLocaleDateString()}
              </div>
              <div>
                <span className="font-medium text-gray-600">Budget:</span> ${job.budget}
              </div>
              <div>
                <span className="font-medium text-gray-600">Status:</span> 
                <span className="ml-1 px-2 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  {job.status}
                </span>
              </div>
              <div>
                <span className="font-medium text-gray-600">Posted:</span> {new Date(job.created_at).toLocaleDateString()}
              </div>
            </div>
          </div>

          <div>
            <h2 className="text-xl font-semibold mb-4">Description</h2>
            <p className="text-gray-700 mb-4">{job.description}</p>

            <h3 className="font-medium text-gray-600 mt-4 mb-2">Requirements:</h3>
            <p className="text-gray-700">{job.requirements}</p>
          </div>
        </div>

        {/* Upsell Items Section */}
        <div className="mt-8 pt-6 border-t">
          <h2 className="text-xl font-semibold mb-4">Additional Services</h2>

          {/* Current Upsell Items */}
          {jobUpsellItems.length > 0 ? (
            <div className="mb-6">
              <h3 className="text-lg font-medium mb-3">Selected Services</h3>
              <div className="bg-gray-50 rounded-lg p-4 border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2">Service</th>
                      <th className="text-left py-2">Category</th>
                      <th className="text-right py-2">Price</th>
                      <th className="text-right py-2">Quantity</th>
                      <th className="text-right py-2">Total</th>
                      {isOwner && <th className="text-right py-2">Actions</th>}
                    </tr>
                  </thead>
                  <tbody>
                    {jobUpsellItems.map((item) => (
                      <tr key={item.id} className="border-b">
                        <td className="py-2">
                          <div className="font-medium">{item.name}</div>
                          {item.description && <div className="text-sm text-gray-500">{item.description}</div>}
                        </td>
                        <td className="py-2">{item.category}</td>
                        <td className="py-2 text-right">${item.price_at_purchase.toFixed(2)}</td>
                        <td className="py-2 text-right">{item.quantity}</td>
                        <td className="py-2 text-right">${(item.price_at_purchase * item.quantity).toFixed(2)}</td>
                        {isOwner && (
                          <td className="py-2 text-right">
                            <upsellFetcher.Form method="post">
                              <input type="hidden" name="action" value="remove-upsell-item" />
                              <input type="hidden" name="upsellItemId" value={item.upsell_item_id} />
                              <button 
                                type="submit"
                                className="text-red-600 hover:text-red-800"
                                aria-label="Remove item"
                              >
                                Remove
                              </button>
                            </upsellFetcher.Form>
                          </td>
                        )}
                      </tr>
                    ))}
                    <tr className="font-bold">
                      <td colSpan={4} className="py-2 text-right">Total:</td>
                      <td className="py-2 text-right">${upsellItemsTotal.toFixed(2)}</td>
                      {isOwner && <td></td>}
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          ) : (
            <p className="text-gray-500 mb-6">No additional services selected for this job.</p>
          )}

          {/* Add Upsell Items (Only for owners) */}
          {isOwner && (
            <div>
              <h3 className="text-lg font-medium mb-3">Add Services</h3>
              {activeUpsellItems.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {activeUpsellItems.map((item) => (
                    <div key={item.id} className="border rounded-lg p-4 bg-white">
                      <div className="font-medium">{item.name}</div>
                      <div className="text-sm text-gray-500 mb-2">{item.description}</div>
                      <div className="flex justify-between items-center">
                        <span className="text-green-600 font-medium">${item.price.toFixed(2)}</span>
                        <upsellFetcher.Form method="post" className="flex items-center gap-2">
                          <input type="hidden" name="action" value="add-upsell-item" />
                          <input type="hidden" name="upsellItemId" value={item.id} />
                          <select 
                            name="quantity" 
                            className="border rounded px-2 py-1 text-sm"
                            defaultValue="1"
                          >
                            {[1, 2, 3, 4, 5].map((num) => (
                              <option key={num} value={num}>{num}</option>
                            ))}
                          </select>
                          <button 
                            type="submit"
                            className="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 text-sm"
                          >
                            Add
                          </button>
                        </upsellFetcher.Form>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500">No additional services available.</p>
              )}
            </div>
          )}
        </div>

        {/* Dispute Section */}
        {(activeDispute || disputes.length > 0) && (
          <div className="mt-8 pt-6 border-t">
            <h2 className="text-xl font-semibold mb-4">Dispute Information</h2>

            {activeDispute ? (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-medium text-red-800">Active Dispute</h3>
                    <p className="text-red-700">
                      Status: <span className="font-medium">{activeDispute.status.toUpperCase().replace('_', ' ')}</span>
                    </p>
                    <p className="text-red-700">
                      Reason: <span className="font-medium">{activeDispute.reason.replace('_', ' ').toUpperCase()}</span>
                    </p>
                    <p className="text-red-700">
                      Created: <span className="font-medium">{new Date(activeDispute.created_at).toLocaleDateString()}</span>
                    </p>
                  </div>
                  <Link
                    to={`/marine/jobs/${job.id}/disputes/${activeDispute.id}`}
                    className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                  >
                    View Dispute Details
                  </Link>
                </div>
                <p className="text-sm text-red-600">
                  This job has an active dispute. The escrow funds are currently on hold until the dispute is resolved.
                </p>
              </div>
            ) : disputes.length > 0 && (
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 mb-4">
                <h3 className="font-medium mb-2">Previous Disputes</h3>
                <ul className="space-y-2">
                  {disputes.map(dispute => (
                    <li key={dispute.id} className="flex justify-between items-center">
                      <div>
                        <span className="font-medium">
                          {dispute.reason.replace('_', ' ').toUpperCase()}
                        </span>
                        <span className="mx-2">•</span>
                        <span className={`${
                          dispute.status === 'resolved' 
                            ? 'text-green-600' 
                            : dispute.status === 'cancelled'
                            ? 'text-gray-600'
                            : 'text-red-600'
                        }`}>
                          {dispute.status.toUpperCase().replace('_', ' ')}
                        </span>
                        <span className="mx-2">•</span>
                        <span>{new Date(dispute.created_at).toLocaleDateString()}</span>
                      </div>
                      <Link
                        to={`/marine/jobs/${job.id}/disputes/${dispute.id}`}
                        className="text-blue-600 hover:underline"
                      >
                        View Details
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        <div className="mt-8 pt-6 border-t">
          <div className="flex justify-between items-center">
            <div>
              <span className="font-medium text-gray-600">Posted by:</span> {job.owner}
            </div>

            {user ? (
              isPilot ? (
                <div className="flex gap-3">
                  <Link 
                    to={`/marine/jobs/${job.id}/apply`}
                    className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Apply for this Job
                  </Link>
                  <Link
                    to={`/marine/jobs/${job.id}/expenses`}
                    className="px-6 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
                  >
                    Submit Expenses
                  </Link>
                  {isJobPilot && canCreateDispute && (
                    <Link
                      to={`/marine/jobs/${job.id}/dispute`}
                      className="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Create Dispute
                    </Link>
                  )}
                </div>
              ) : (
                <div className="flex gap-3">
                  <Link
                    to={`/marine/jobs/${job.id}/applications`}
                    className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    View Applications
                  </Link>
                  <Link
                    to={`/marine/jobs/${job.id}/escrow`}
                    className="px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                  >
                    Manage Escrow
                  </Link>
                  <Link
                    to={`/marine/jobs/${job.id}/expenses`}
                    className="px-6 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700"
                  >
                    Review Expenses
                  </Link>
                  {isJobOwner && canCreateDispute && (
                    <Link
                      to={`/marine/jobs/${job.id}/dispute`}
                      className="px-6 py-2 bg-red-600 text-white rounded hover:bg-red-700"
                    >
                      Create Dispute
                    </Link>
                  )}
                  <Link
                    to={`/marine/jobs/${job.id}/edit`}
                    className="px-6 py-2 border border-gray-300 rounded hover:bg-gray-50"
                  >
                    Edit Job
                  </Link>
                </div>
              )
            ) : (
              <Link 
                to={`/login?redirectTo=/marine/jobs/${job.id}`}
                className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Login to Apply
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
