import { json, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useLoaderData, useSearchParams, useFetcher } from "@remix-run/react";
import { getMarineJobs, getVesselTypes, groupVesselTypesByCategory } from "~/utils/marine.server";
import { getUser, requireUserId } from "~/utils/session.server";
import { useState, useEffect, useRef } from "react";
import { withPerformanceTracking } from "~/utils/performance.server";

export const meta: MetaFunction = () => {
  return [{ title: "Available Marine Jobs | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const url = new URL(request.url);

  // Get filter parameters from URL
  const vesselType = url.searchParams.get("vesselType") || undefined;
  const origin = url.searchParams.get("origin") || undefined;
  const destination = url.searchParams.get("destination") || undefined;
  const fromDate = url.searchParams.get("fromDate") ? new Date(url.searchParams.get("fromDate")!) : undefined;
  const toDate = url.searchParams.get("toDate") ? new Date(url.searchParams.get("toDate")!) : undefined;
  const minBudget = url.searchParams.get("minBudget") ? Number(url.searchParams.get("minBudget")) : undefined;
  const maxBudget = url.searchParams.get("maxBudget") ? Number(url.searchParams.get("maxBudget")) : undefined;
  const keyword = url.searchParams.get("keyword") || undefined;
  const sortBy = url.searchParams.get("sortBy") || "date";

  try {
    // Check if user is logged in
    const userId = await requireUserId(request);
    const user = await getUser(request);

    // Get all vessel types for the filter dropdown
    const vesselTypes = await getVesselTypes();

    // Group vessel types by category on the server side
    const vesselTypesByCategory = groupVesselTypesByCategory(vesselTypes);

    // Convert to the format needed for the dropdown
    const formattedVesselTypesByCategory: Record<string, Array<{value: string, label: string}>> = {};
    Object.entries(vesselTypesByCategory).forEach(([category, types]) => {
      formattedVesselTypesByCategory[category] = types.map(type => ({
        value: type.name,
        label: type.name
      }));
    });

    // Get marine jobs with filters
    let jobs = await getMarineJobs({
      vessel_type: vesselType,
      featured_first: true // Prioritize featured jobs
    });

    // Apply additional filters that aren't directly supported by the getMarineJobs function
    if (origin) {
      jobs = jobs.filter(job => job.origin.toLowerCase().includes(origin.toLowerCase()));
    }
    
    if (destination) {
      jobs = jobs.filter(job => job.destination.toLowerCase().includes(destination.toLowerCase()));
    }
    
    if (fromDate) {
      jobs = jobs.filter(job => new Date(job.estimated_date) >= fromDate);
    }
    
    if (toDate) {
      jobs = jobs.filter(job => new Date(job.estimated_date) <= toDate);
    }
    
    if (minBudget !== undefined) {
      jobs = jobs.filter(job => (job.budget || 0) >= minBudget);
    }
    
    if (maxBudget !== undefined) {
      jobs = jobs.filter(job => (job.budget || 0) <= maxBudget);
    }
    
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      jobs = jobs.filter(job => 
        job.title.toLowerCase().includes(lowerKeyword) || 
        job.description.toLowerCase().includes(lowerKeyword)
      );
    }

    // Sort jobs based on sortBy parameter
    if (sortBy === "budgetHighToLow") {
      jobs.sort((a, b) => (b.budget || 0) - (a.budget || 0));
    } else if (sortBy === "budgetLowToHigh") {
      jobs.sort((a, b) => (a.budget || 0) - (b.budget || 0));
    } else if (sortBy === "date") {
      jobs.sort((a, b) => new Date(a.estimated_date).getTime() - new Date(b.estimated_date).getTime());
    }

    // Get owner information for each job
    const jobsWithOwners = await Promise.all(jobs.map(async (job) => {
      try {
        // In a real implementation, this would fetch the owner from the database
        // For now, we'll use mock data
        const ownerName = job.owner_id % 2 === 0 ? "Ocean Ventures LLC" : "Pacific Marine Transport";

        return {
          ...job,
          owner: ownerName
        };
      } catch (error) {
        return {
          ...job,
          owner: "Unknown Owner"
        };
      }
    }));

    return json({
      jobs: jobsWithOwners,
      user,
      vesselTypes,
      formattedVesselTypesByCategory,
      filters: {
        vesselType,
        origin,
        destination,
        fromDate: fromDate?.toISOString().split('T')[0],
        toDate: toDate?.toISOString().split('T')[0],
        minBudget,
        maxBudget,
        keyword,
        sortBy
      }
    });
  } catch (error) {
    // If user is not logged in, return a limited set of jobs
    const vesselTypes = await getVesselTypes();

    // Group vessel types by category on the server side
    const vesselTypesByCategory = groupVesselTypesByCategory(vesselTypes);

    // Convert to the format needed for the dropdown
    const formattedVesselTypesByCategory: Record<string, Array<{value: string, label: string}>> = {};
    Object.entries(vesselTypesByCategory).forEach(([category, types]) => {
      formattedVesselTypesByCategory[category] = types.map(type => ({
        value: type.name,
        label: type.name
      }));
    });

    let jobs = await getMarineJobs({
      vessel_type: vesselType,
      featured_first: true // Prioritize featured jobs
    });

    // Apply additional filters
    if (origin) {
      jobs = jobs.filter(job => job.origin.toLowerCase().includes(origin.toLowerCase()));
    }
    
    if (destination) {
      jobs = jobs.filter(job => job.destination.toLowerCase().includes(destination.toLowerCase()));
    }
    
    if (fromDate) {
      jobs = jobs.filter(job => new Date(job.estimated_date) >= fromDate);
    }
    
    if (toDate) {
      jobs = jobs.filter(job => new Date(job.estimated_date) <= toDate);
    }
    
    if (minBudget !== undefined) {
      jobs = jobs.filter(job => (job.budget || 0) >= minBudget);
    }
    
    if (maxBudget !== undefined) {
      jobs = jobs.filter(job => (job.budget || 0) <= maxBudget);
    }
    
    if (keyword) {
      const lowerKeyword = keyword.toLowerCase();
      jobs = jobs.filter(job => 
        job.title.toLowerCase().includes(lowerKeyword) || 
        job.description.toLowerCase().includes(lowerKeyword)
      );
    }

    // Sort jobs based on sortBy parameter
    if (sortBy === "budgetHighToLow") {
      jobs.sort((a, b) => (b.budget || 0) - (a.budget || 0));
    } else if (sortBy === "budgetLowToHigh") {
      jobs.sort((a, b) => (a.budget || 0) - (b.budget || 0));
    } else if (sortBy === "date") {
      jobs.sort((a, b) => new Date(a.estimated_date).getTime() - new Date(b.estimated_date).getTime());
    }

    // Get owner information for each job
    const jobsWithOwners = jobs.map(job => ({
      ...job,
      owner: job.owner_id % 2 === 0 ? "Ocean Ventures LLC" : "Pacific Marine Transport"
    }));

    return json({
      jobs: jobsWithOwners,
      user: null,
      vesselTypes,
      formattedVesselTypesByCategory,
      filters: {
        vesselType,
        origin,
        destination,
        fromDate: fromDate?.toISOString().split('T')[0],
        toDate: toDate?.toISOString().split('T')[0],
        minBudget,
        maxBudget,
        keyword,
        sortBy
      }
    });
  }
}

export default function MarineJobs() {
  const { jobs, user, vesselTypes, formattedVesselTypesByCategory, filters } = useLoaderData<typeof loader>();
  const [searchParams, setSearchParams] = useSearchParams();
  const [showFilters, setShowFilters] = useState(false);

  // Function to update search params and navigate
  const updateFilters = (newFilters: Record<string, string | undefined>) => {
    const newParams = new URLSearchParams(searchParams);

    // Update or remove each filter
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        newParams.set(key, value);
      } else {
        newParams.delete(key);
      }
    });

    setSearchParams(newParams);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Available Marine Transport Jobs</h1>
        <div className="flex gap-2">
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="px-3 py-1 border rounded bg-gray-50 hover:bg-gray-100"
          >
            {showFilters ? "Hide Filters" : "Show Filters"}
          </button>

          <select 
            className="border rounded px-3 py-1"
            value={filters.sortBy || "date"}
            onChange={(e) => updateFilters({ sortBy: e.target.value })}
          >
            <option value="date">Sort by Date</option>
            <option value="budgetHighToLow">Sort by Budget (High to Low)</option>
            <option value="budgetLowToHigh">Sort by Budget (Low to High)</option>
          </select>
        </div>
      </div>

      {showFilters && (
        <div className="bg-gray-50 p-4 rounded-lg border">
          <h2 className="text-lg font-semibold mb-3">Filter Jobs</h2>
          <Form method="get" className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="md:col-span-3 mb-2">
              <label htmlFor="keyword" className="block text-sm font-medium text-gray-700 mb-1">
                Search
              </label>
              <input
                type="text"
                id="keyword"
                name="keyword"
                className="w-full border rounded-md px-3 py-2"
                value={filters.keyword || ""}
                onChange={(e) => updateFilters({ keyword: e.target.value || undefined })}
                placeholder="Search by keywords in title or description"
              />
            </div>
            <div>
              <label htmlFor="vesselType" className="block text-sm font-medium text-gray-700 mb-1">
                Vessel Type
              </label>
              <select
                id="vesselType"
                name="vesselType"
                className="w-full border rounded-md px-3 py-2"
                value={filters.vesselType || ""}
                onChange={(e) => updateFilters({ vesselType: e.target.value || undefined })}
              >
                <option value="">All Vessel Types</option>
                {Object.entries(formattedVesselTypesByCategory).map(([category, types]) => (
                  <optgroup key={category} label={category}>
                    {types.map((type) => (
                      <option key={type.value} value={type.value}>
                        {type.label}
                      </option>
                    ))}
                  </optgroup>
                ))}
              </select>
            </div>

            <div>
              <label htmlFor="origin" className="block text-sm font-medium text-gray-700 mb-1">
                Origin (Port/Marina)
              </label>
              <input
                type="text"
                id="origin"
                name="origin"
                className="w-full border rounded-md px-3 py-2"
                value={filters.origin || ""}
                onChange={(e) => updateFilters({ origin: e.target.value || undefined })}
                placeholder="Enter origin port or marina"
              />
            </div>

            <div>
              <label htmlFor="destination" className="block text-sm font-medium text-gray-700 mb-1">
                Destination (Port/Marina)
              </label>
              <input
                type="text"
                id="destination"
                name="destination"
                className="w-full border rounded-md px-3 py-2"
                value={filters.destination || ""}
                onChange={(e) => updateFilters({ destination: e.target.value || undefined })}
                placeholder="Enter destination port or marina"
              />
            </div>

            <div>
              <label htmlFor="fromDate" className="block text-sm font-medium text-gray-700 mb-1">
                From Date
              </label>
              <input
                type="date"
                id="fromDate"
                name="fromDate"
                className="w-full border rounded-md px-3 py-2"
                value={filters.fromDate || ""}
                onChange={(e) => updateFilters({ fromDate: e.target.value || undefined })}
              />
            </div>

            <div>
              <label htmlFor="toDate" className="block text-sm font-medium text-gray-700 mb-1">
                To Date
              </label>
              <input
                type="date"
                id="toDate"
                name="toDate"
                className="w-full border rounded-md px-3 py-2"
                value={filters.toDate || ""}
                onChange={(e) => updateFilters({ toDate: e.target.value || undefined })}
              />
            </div>

            <div>
              <label htmlFor="budget" className="block text-sm font-medium text-gray-700 mb-1">
                Budget Range
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  id="minBudget"
                  name="minBudget"
                  className="w-full border rounded-md px-3 py-2"
                  value={filters.minBudget || ""}
                  onChange={(e) => updateFilters({ minBudget: e.target.value || undefined })}
                  placeholder="Min"
                />
                <span className="self-center">-</span>
                <input
                  type="number"
                  id="maxBudget"
                  name="maxBudget"
                  className="w-full border rounded-md px-3 py-2"
                  value={filters.maxBudget || ""}
                  onChange={(e) => updateFilters({ maxBudget: e.target.value || undefined })}
                  placeholder="Max"
                />
              </div>
            </div>

            <div className="md:col-span-3 flex justify-end gap-2">
              <button
                type="button"
                onClick={() => {
                  // Clear all filters
                  const newParams = new URLSearchParams();
                  if (filters.sortBy) {
                    newParams.set("sortBy", filters.sortBy);
                  }
                  setSearchParams(newParams);
                }}
                className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
              >
                Clear Filters
              </button>
            </div>
          </Form>
        </div>
      )}

      {jobs.length === 0 ? (
        <div className="text-center py-8">
          <p className="text-gray-500">No marine jobs found matching your criteria.</p>
        </div>
      ) : (
        <div className="grid gap-6">
          {jobs.map((job) => (
            <div key={job.id} className={`border rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow ${job.is_featured ? 'border-yellow-400 border-2' : ''}`}>
              <div className="flex justify-between">
                <div className="flex items-center gap-2">
                  <h2 className="text-xl font-semibold text-blue-700">{job.title}</h2>
                  {job.is_featured && (
                    <span className="inline-block px-2 py-1 bg-yellow-100 text-yellow-800 text-xs font-semibold rounded">
                      Featured
                    </span>
                  )}
                </div>
                <span className="text-green-600 font-medium">${job.budget}</span>
              </div>

              <div className="mt-2 grid grid-cols-2 gap-x-4 gap-y-2">
                <div>
                  <span className="text-gray-600">Vessel:</span> {job.vessel_type}
                </div>
                <div>
                  <span className="text-gray-600">Date:</span> {new Date(job.estimated_date).toLocaleDateString()}
                </div>
                <div>
                  <span className="text-gray-600">From:</span> {job.origin}
                </div>
                <div>
                  <span className="text-gray-600">To:</span> {job.destination}
                </div>
                {job.vessel_length && (
                  <div>
                    <span className="text-gray-600">Length:</span> {job.vessel_length} ft
                  </div>
                )}
                {job.vessel_draft && (
                  <div>
                    <span className="text-gray-600">Draft:</span> {job.vessel_draft} ft
                  </div>
                )}
              </div>
              <div className="mt-3 flex justify-between items-center">
                <span className="text-sm text-gray-500">Posted by: {job.owner}</span>
                <a 
                  href={`/marine/jobs/${job.id}`} 
                  className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                >
                  View Details
                </a>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}