import { json, redirect, ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useActionData, useLoaderData, Link } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { getMarineJobById } from "~/utils/marine.server";
import { createDispute, getEscrowAccountForJob } from "~/utils/payment.server";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [{ title: "Create Dispute | FerryPros" }];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  // Ensure the user is an owner or pilot
  if (user?.role !== "owner" && user?.role !== "pilot" && user?.role !== "admin") {
    return redirect("/dashboard");
  }

  const jobId = params.jobId;

  if (!jobId) {
    return redirect("/marine/jobs");
  }

  // Verify this is a marine job
  const marineJob = await getMarineJobById(Number(jobId));
  if (!marineJob) {
    throw new Response("Marine job not found", { status: 404 });
  }

  // Get the escrow account for this job
  const escrowAccount = await getEscrowAccountForJob(Number(jobId));
  if (!escrowAccount) {
    return redirect(`/marine/jobs/${jobId}`);
  }

  // Only allow disputes for active jobs with active escrow
  if (marineJob.status !== "in_progress" || escrowAccount.status !== "active") {
    return json({ 
      error: "Disputes can only be created for in-progress jobs with active escrow accounts",
      canCreateDispute: false,
      user,
      job: marineJob,
      escrowAccount
    });
  }

  // Check if the user is the owner or the assigned pilot
  const isOwner = user?.id === marineJob.owner_id;
  const isPilot = user?.id === marineJob.pilot_id;
  
  if (!isOwner && !isPilot && user?.role !== "admin") {
    return json({ 
      error: "Only the job owner or assigned pilot can create a dispute",
      canCreateDispute: false,
      user,
      job: marineJob,
      escrowAccount
    });
  }

  return json({ 
    canCreateDispute: true,
    user, 
    job: marineJob,
    escrowAccount
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  // Ensure the user is an owner or pilot
  if (user?.role !== "owner" && user?.role !== "pilot" && user?.role !== "admin") {
    return json({ error: "Unauthorized" }, { status: 403 });
  }

  const jobId = params.jobId;

  if (!jobId) {
    return redirect("/marine/jobs");
  }

  // Verify this is a marine job
  const marineJob = await getMarineJobById(Number(jobId));
  if (!marineJob) {
    return json({ error: "Marine job not found" }, { status: 404 });
  }

  // Get the escrow account for this job
  const escrowAccount = await getEscrowAccountForJob(Number(jobId));
  if (!escrowAccount) {
    return json({ error: "No escrow account found for this job" }, { status: 400 });
  }

  // Only allow disputes for active jobs with active escrow
  if (marineJob.status !== "in_progress" || escrowAccount.status !== "active") {
    return json({ 
      error: "Disputes can only be created for in-progress jobs with active escrow accounts" 
    }, { status: 400 });
  }

  // Check if the user is the owner or the assigned pilot
  const isOwner = user?.id === marineJob.owner_id;
  const isPilot = user?.id === marineJob.pilot_id;
  
  if (!isOwner && !isPilot && user?.role !== "admin") {
    return json({ 
      error: "Only the job owner or assigned pilot can create a dispute" 
    }, { status: 403 });
  }

  const formData = await request.formData();
  const reason = formData.get("reason");
  const description = formData.get("description");

  if (!reason || !description) {
    return json({ 
      error: "Reason and description are required",
      values: Object.fromEntries(formData)
    }, { status: 400 });
  }

  try {
    // Create the dispute
    const dispute = await createDispute(
      Number(jobId),
      escrowAccount.id,
      Number(userId),
      reason.toString(),
      description.toString()
    );

    return redirect(`/marine/jobs/${jobId}`);
  } catch (error) {
    return json({ 
      error: "Failed to create dispute",
      values: Object.fromEntries(formData)
    }, { status: 500 });
  }
}

export default function CreateDispute() {
  const { user, job, escrowAccount, canCreateDispute, error } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [reason, setReason] = useState(actionData?.values?.reason || "");
  const [description, setDescription] = useState(actionData?.values?.description || "");

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to={`/marine/jobs/${job.id}`} className="text-blue-600 hover:underline">
          ← Back to Marine Job
        </Link>
        <h1 className="text-2xl font-bold">Create Dispute</h1>
      </div>

      {(error || actionData?.error) && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{error || actionData?.error}</p>
        </div>
      )}

      {canCreateDispute ? (
        <div className="bg-white shadow rounded-lg p-6">
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-2">Job Information</h2>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <span className="text-gray-600">Job Title:</span>{" "}
                <span className="font-medium">{job.title}</span>
              </div>
              <div>
                <span className="text-gray-600">Status:</span>{" "}
                <span className="font-medium">{job.status}</span>
              </div>
              <div>
                <span className="text-gray-600">Escrow Balance:</span>{" "}
                <span className="font-medium">${escrowAccount.balance}</span>
              </div>
            </div>
          </div>

          <Form method="post" className="space-y-4">
            <div>
              <label htmlFor="reason" className="block font-medium text-gray-700 mb-1">
                Reason for Dispute
              </label>
              <select
                id="reason"
                name="reason"
                className="w-full border rounded-md px-3 py-2"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                required
              >
                <option value="">Select a reason</option>
                <option value="payment_issue">Payment Issue</option>
                <option value="job_completion">Job Completion Problem</option>
                <option value="communication">Communication Problem</option>
                <option value="contract_terms">Contract Terms Dispute</option>
                <option value="other">Other</option>
              </select>
            </div>

            <div>
              <label htmlFor="description" className="block font-medium text-gray-700 mb-1">
                Detailed Description
              </label>
              <textarea
                id="description"
                name="description"
                rows={5}
                className="w-full border rounded-md px-3 py-2"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Please provide a detailed description of the issue..."
                required
              ></textarea>
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
              >
                Create Dispute
              </button>
            </div>
          </Form>
        </div>
      ) : (
        <div className="bg-white shadow rounded-lg p-6">
          <p className="text-gray-700">
            You cannot create a dispute for this job at this time. Disputes can only be created for in-progress jobs with active escrow accounts.
          </p>
        </div>
      )}

      <div className="bg-gray-50 p-4 rounded-lg border">
        <h2 className="text-lg font-semibold mb-2">About Disputes</h2>
        <p className="text-gray-700">
          Creating a dispute will temporarily hold the funds in escrow until the dispute is resolved. 
          Both parties will be notified, and an administrator will review the case. 
          Please provide as much detail as possible to help resolve the dispute quickly.
        </p>
      </div>
    </div>
  );
}