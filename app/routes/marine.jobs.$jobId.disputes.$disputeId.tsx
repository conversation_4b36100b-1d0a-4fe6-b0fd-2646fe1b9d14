import { json, redirect, ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useActionData, useLoaderData, Link } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { getMarineJobById } from "~/utils/marine.server";
import { 
  getDisputeById, 
  getEscrowAccountForJob, 
  updateDisputeStatus, 
  resolveDisputeWithPartialPayment,
  addFundsToDisputedEscrow,
  getTransactionsForEscrowAccount
} from "~/utils/payment.server";
import { processPaymentForEscrow } from "~/utils/stripe.server";
import { useState } from "react";

export const meta: MetaFunction = () => {
  return [{ title: "Dispute Details | FerryPros" }];
};

export async function loader({ request, params }: LoaderFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  const jobId = params.jobId;
  const disputeId = params.disputeId;

  if (!jobId || !disputeId) {
    return redirect("/marine/jobs");
  }

  // Verify this is a marine job
  const marineJob = await getMarineJobById(Number(jobId));
  if (!marineJob) {
    throw new Response("Marine job not found", { status: 404 });
  }

  // Get the dispute
  const dispute = await getDisputeById(Number(disputeId));
  if (!dispute) {
    throw new Response("Dispute not found", { status: 404 });
  }

  // Verify the dispute belongs to this job
  if (dispute.job_id !== Number(jobId)) {
    return redirect(`/marine/jobs/${jobId}`);
  }

  // Get the escrow account
  const escrowAccount = await getEscrowAccountForJob(Number(jobId));
  if (!escrowAccount) {
    throw new Response("Escrow account not found", { status: 404 });
  }

  // Get transactions related to this dispute
  const allTransactions = await getTransactionsForEscrowAccount(escrowAccount.id);
  const disputeTransactions = allTransactions.filter(
    transaction => transaction.dispute_id === Number(disputeId)
  );

  // Check if the user is the owner, the assigned pilot, or an admin
  const isOwner = user?.id === marineJob.owner_id;
  const isPilot = user?.id === marineJob.pilot_id;
  const isAdmin = user?.role === "admin";
  const isDisputeInitiator = user?.id === dispute.initiated_by_id;
  
  // If not authorized to view this dispute, redirect
  if (!isOwner && !isPilot && !isAdmin) {
    return redirect(`/marine/jobs/${jobId}`);
  }

  return json({ 
    user, 
    job: marineJob,
    dispute,
    escrowAccount,
    disputeTransactions,
    isOwner,
    isPilot,
    isAdmin,
    isDisputeInitiator
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  const jobId = params.jobId;
  const disputeId = params.disputeId;

  if (!jobId || !disputeId) {
    return redirect("/marine/jobs");
  }

  // Verify this is a marine job
  const marineJob = await getMarineJobById(Number(jobId));
  if (!marineJob) {
    return json({ error: "Marine job not found" }, { status: 404 });
  }

  // Get the dispute
  const dispute = await getDisputeById(Number(disputeId));
  if (!dispute) {
    return json({ error: "Dispute not found" }, { status: 404 });
  }

  // Verify the dispute belongs to this job
  if (dispute.job_id !== Number(jobId)) {
    return redirect(`/marine/jobs/${jobId}`);
  }

  // Get the escrow account
  const escrowAccount = await getEscrowAccountForJob(Number(jobId));
  if (!escrowAccount) {
    return json({ error: "Escrow account not found" }, { status: 404 });
  }

  // Check if the user is the owner, the assigned pilot, or an admin
  const isOwner = user?.id === marineJob.owner_id;
  const isPilot = user?.id === marineJob.pilot_id;
  const isAdmin = user?.role === "admin";
  
  const formData = await request.formData();
  const action = formData.get("action");

  // Update dispute status (admin only)
  if (action === "update_status") {
    if (!isAdmin) {
      return json({ error: "Only administrators can update dispute status" }, { status: 403 });
    }

    const status = formData.get("status");
    const adminNotes = formData.get("admin_notes");

    if (!status) {
      return json({ 
        error: "Status is required",
        values: Object.fromEntries(formData)
      }, { status: 400 });
    }

    try {
      const updatedDispute = await updateDisputeStatus(
        Number(disputeId),
        status.toString() as any,
        adminNotes ? adminNotes.toString() : null
      );

      return json({ success: true, dispute: updatedDispute });
    } catch (error) {
      return json({ 
        error: "Failed to update dispute status",
        values: Object.fromEntries(formData)
      }, { status: 500 });
    }
  }
  
  // Resolve dispute with partial payment (admin only)
  else if (action === "resolve_partial") {
    if (!isAdmin) {
      return json({ error: "Only administrators can resolve disputes" }, { status: 403 });
    }

    const amount = formData.get("amount");
    const resolutionDetails = formData.get("resolution_details");
    const adminNotes = formData.get("admin_notes");

    if (!amount || !resolutionDetails) {
      return json({ 
        error: "Amount and resolution details are required",
        values: Object.fromEntries(formData)
      }, { status: 400 });
    }

    try {
      const result = await resolveDisputeWithPartialPayment(
        Number(disputeId),
        escrowAccount.id,
        Number(marineJob.pilot_id),
        Number(amount),
        resolutionDetails.toString(),
        adminNotes ? adminNotes.toString() : null
      );

      return json({ success: true, result });
    } catch (error) {
      return json({ 
        error: "Failed to resolve dispute",
        values: Object.fromEntries(formData)
      }, { status: 500 });
    }
  }
  
  // Add funds to escrow (owner only)
  else if (action === "add_funds") {
    if (!isOwner) {
      return json({ error: "Only the job owner can add funds to escrow" }, { status: 403 });
    }

    const amount = formData.get("amount");
    const description = formData.get("description") || "Additional funds for dispute resolution";

    if (!amount) {
      return json({ 
        error: "Amount is required",
        values: Object.fromEntries(formData)
      }, { status: 400 });
    }

    try {
      // Process payment through Stripe
      const paymentResult = await processPaymentForEscrow(
        escrowAccount.id,
        Number(userId),
        Number(amount),
        description.toString()
      );

      if (!paymentResult.success) {
        return json({ 
          error: "Payment processing failed",
          values: Object.fromEntries(formData)
        }, { status: 400 });
      }

      // Add funds to the escrow account
      const result = await addFundsToDisputedEscrow(
        Number(disputeId),
        escrowAccount.id,
        Number(amount),
        description.toString()
      );

      return json({ success: true, result });
    } catch (error) {
      return json({ 
        error: "Failed to add funds to escrow",
        values: Object.fromEntries(formData)
      }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
}

export default function DisputeDetails() {
  const { 
    user, 
    job, 
    dispute, 
    escrowAccount, 
    disputeTransactions,
    isOwner,
    isPilot,
    isAdmin,
    isDisputeInitiator
  } = useLoaderData<typeof loader>();
  
  const actionData = useActionData<typeof action>();
  const [status, setStatus] = useState(actionData?.values?.status || dispute.status);
  const [adminNotes, setAdminNotes] = useState(actionData?.values?.admin_notes || dispute.admin_notes || "");
  const [amount, setAmount] = useState(actionData?.values?.amount || "");
  const [description, setDescription] = useState(actionData?.values?.description || "");
  const [resolutionDetails, setResolutionDetails] = useState(actionData?.values?.resolution_details || "");

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString() + ' ' + 
           new Date(dateString).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to={`/marine/jobs/${job.id}`} className="text-blue-600 hover:underline">
          ← Back to Marine Job
        </Link>
        <h1 className="text-2xl font-bold">Dispute Details</h1>
      </div>

      {actionData?.error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800">{actionData.error}</p>
        </div>
      )}

      {actionData?.success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <p className="text-green-800">
            {actionData.dispute 
              ? "Dispute status updated successfully!" 
              : "Dispute action completed successfully!"}
          </p>
        </div>
      )}

      <div className="bg-white shadow rounded-lg p-6">
        <div className="border-b pb-4 mb-4">
          <h2 className="text-xl font-semibold mb-4">Dispute Information</h2>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-gray-600">Status:</span>{" "}
              <span className={`font-medium ${
                dispute.status === 'open' 
                  ? 'text-red-600' 
                  : dispute.status === 'under_review'
                  ? 'text-yellow-600'
                  : dispute.status === 'resolved'
                  ? 'text-green-600'
                  : 'text-gray-600'
              }`}>
                {dispute.status.toUpperCase().replace('_', ' ')}
              </span>
            </div>
            <div>
              <span className="text-gray-600">Created:</span>{" "}
              <span>{formatDate(dispute.created_at)}</span>
            </div>
            <div>
              <span className="text-gray-600">Reason:</span>{" "}
              <span className="font-medium">{dispute.reason.replace('_', ' ').toUpperCase()}</span>
            </div>
            {dispute.resolved_at && (
              <div>
                <span className="text-gray-600">Resolved:</span>{" "}
                <span>{formatDate(dispute.resolved_at)}</span>
              </div>
            )}
          </div>
        </div>

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Description</h3>
          <p className="text-gray-700 whitespace-pre-line">{dispute.description}</p>
        </div>

        {dispute.resolution_details && (
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Resolution Details</h3>
            <p className="text-gray-700 whitespace-pre-line">{dispute.resolution_details}</p>
          </div>
        )}

        {dispute.admin_notes && isAdmin && (
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Admin Notes</h3>
            <p className="text-gray-700 whitespace-pre-line">{dispute.admin_notes}</p>
          </div>
        )}

        <div className="mb-6">
          <h3 className="text-lg font-medium mb-2">Escrow Information</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="text-gray-600">Current Balance:</span>{" "}
              <span className="font-medium">${escrowAccount.balance}</span>
            </div>
            <div>
              <span className="text-gray-600">Status:</span>{" "}
              <span className="font-medium">{escrowAccount.status.toUpperCase()}</span>
            </div>
          </div>
        </div>

        {disputeTransactions.length > 0 && (
          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Dispute Transactions</h3>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {disputeTransactions.map((transaction) => (
                    <tr key={transaction.id}>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {formatDate(transaction.created_at)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {transaction.type.toUpperCase()}
                        {transaction.dispute_resolution_type && ` (${transaction.dispute_resolution_type.replace('_', ' ')})`}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${transaction.amount}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {transaction.description}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {/* Admin Actions */}
        {isAdmin && dispute.status !== 'resolved' && dispute.status !== 'cancelled' && (
          <div className="border-t pt-4 mt-6">
            <h3 className="text-lg font-medium mb-4">Admin Actions</h3>
            
            <div className="space-y-6">
              {/* Update Status Form */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">Update Dispute Status</h4>
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="update_status" />
                  
                  <div>
                    <label htmlFor="status" className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      id="status"
                      name="status"
                      className="w-full border rounded-md px-3 py-2"
                      value={status}
                      onChange={(e) => setStatus(e.target.value)}
                      required
                    >
                      <option value="open">Open</option>
                      <option value="under_review">Under Review</option>
                      <option value="resolved">Resolved</option>
                      <option value="cancelled">Cancelled</option>
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="admin_notes" className="block text-sm font-medium text-gray-700 mb-1">
                      Admin Notes
                    </label>
                    <textarea
                      id="admin_notes"
                      name="admin_notes"
                      rows={3}
                      className="w-full border rounded-md px-3 py-2"
                      value={adminNotes}
                      onChange={(e) => setAdminNotes(e.target.value)}
                      placeholder="Internal notes about this dispute..."
                    ></textarea>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                    >
                      Update Status
                    </button>
                  </div>
                </Form>
              </div>
              
              {/* Resolve with Partial Payment Form */}
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-3">Resolve with Partial Payment</h4>
                <Form method="post" className="space-y-4">
                  <input type="hidden" name="action" value="resolve_partial" />
                  
                  <div>
                    <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                      Payment Amount (USD)
                    </label>
                    <input
                      type="number"
                      id="amount"
                      name="amount"
                      min="0"
                      step="0.01"
                      max={escrowAccount.balance}
                      className="w-full border rounded-md px-3 py-2"
                      value={amount}
                      onChange={(e) => setAmount(e.target.value)}
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Maximum available: ${escrowAccount.balance}
                    </p>
                  </div>
                  
                  <div>
                    <label htmlFor="resolution_details" className="block text-sm font-medium text-gray-700 mb-1">
                      Resolution Details
                    </label>
                    <textarea
                      id="resolution_details"
                      name="resolution_details"
                      rows={3}
                      className="w-full border rounded-md px-3 py-2"
                      value={resolutionDetails}
                      onChange={(e) => setResolutionDetails(e.target.value)}
                      placeholder="Explain the resolution details..."
                      required
                    ></textarea>
                  </div>
                  
                  <div>
                    <label htmlFor="admin_notes_resolve" className="block text-sm font-medium text-gray-700 mb-1">
                      Admin Notes
                    </label>
                    <textarea
                      id="admin_notes_resolve"
                      name="admin_notes"
                      rows={3}
                      className="w-full border rounded-md px-3 py-2"
                      placeholder="Internal notes about this resolution..."
                    ></textarea>
                  </div>
                  
                  <div className="flex justify-end">
                    <button
                      type="submit"
                      className="px-4 py-2 bg-green-600 text-white rounded hover:bg-green-700"
                    >
                      Resolve Dispute
                    </button>
                  </div>
                </Form>
              </div>
            </div>
          </div>
        )}
        
        {/* Owner Actions */}
        {isOwner && dispute.status !== 'resolved' && dispute.status !== 'cancelled' && (
          <div className="border-t pt-4 mt-6">
            <h3 className="text-lg font-medium mb-4">Owner Actions</h3>
            
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium mb-3">Add Funds to Escrow</h4>
              <Form method="post" className="space-y-4">
                <input type="hidden" name="action" value="add_funds" />
                
                <div>
                  <label htmlFor="amount_add" className="block text-sm font-medium text-gray-700 mb-1">
                    Amount (USD)
                  </label>
                  <input
                    type="number"
                    id="amount_add"
                    name="amount"
                    min="1"
                    step="0.01"
                    className="w-full border rounded-md px-3 py-2"
                    value={amount}
                    onChange={(e) => setAmount(e.target.value)}
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="description_add" className="block text-sm font-medium text-gray-700 mb-1">
                    Description (Optional)
                  </label>
                  <textarea
                    id="description_add"
                    name="description"
                    rows={2}
                    className="w-full border rounded-md px-3 py-2"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="e.g., Additional funds for dispute resolution"
                  ></textarea>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
                  >
                    Add Funds
                  </button>
                </div>
              </Form>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}