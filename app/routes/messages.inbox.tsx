import { json, LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData } from "@remix-run/react";
import { requireUser } from "~/utils/session.server";
import { getInboxMessages } from "~/utils/messaging.server";
import { formatDistanceToNow } from "date-fns";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const messages = await getInboxMessages(user.id);
  
  return json({ messages });
}

export default function Inbox() {
  const { messages } = useLoaderData<typeof loader>();
  
  if (messages.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium text-gray-500">Your inbox is empty</h2>
        <p className="mt-2 text-gray-400">When you receive messages, they will appear here.</p>
      </div>
    );
  }
  
  return (
    <div>
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {messages.map((message) => (
            <li key={message.id}>
              <Link 
                to={`/messages/${message.id}`} 
                className={`block hover:bg-gray-50 ${message.isNew ? 'bg-blue-50' : ''}`}
              >
                <div className="px-4 py-4 sm:px-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      {message.isNew && (
                        <span className="flex-shrink-0 inline-block h-2 w-2 rounded-full bg-blue-600 mr-2" 
                              aria-hidden="true"></span>
                      )}
                      <p className={`text-sm font-medium ${message.isNew ? 'text-blue-600' : 'text-gray-900'}`}>
                        {message.senderName}
                      </p>
                    </div>
                    <div className="ml-2 flex-shrink-0 flex">
                      <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                        {message.messageType}
                      </p>
                    </div>
                  </div>
                  <div className="mt-2 sm:flex sm:justify-between">
                    <div className="sm:flex">
                      <p className="text-sm text-gray-500 truncate" style={{ maxWidth: '500px' }}>
                        {message.message}
                      </p>
                    </div>
                    <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                      <p>
                        {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                      </p>
                    </div>
                  </div>
                  {message.jobTitle && (
                    <div className="mt-2">
                      <span className="text-xs text-gray-500">
                        Re: {message.jobTitle}
                      </span>
                    </div>
                  )}
                </div>
              </Link>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}