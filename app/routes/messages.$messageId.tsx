import { json, LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form, useNavigate, useParams } from "@remix-run/react";
import { requireUser } from "~/utils/session.server";
import { getMessageById, replyToMessage, moveMessageToTrash } from "~/utils/messaging.server";
import { useState } from "react";
import { formatDistanceToNow, format } from "date-fns";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const messageId = Number(params.messageId);
  
  if (!messageId) {
    throw new Response("Message not found", { status: 404 });
  }
  
  const message = await getMessageById(messageId, user.id);
  
  if (!message) {
    throw new Response("Message not found", { status: 404 });
  }
  
  return json({ message, user });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const user = await requireUser(request);
  const messageId = Number(params.messageId);
  
  if (!messageId) {
    return json({ error: "Message not found" }, { status: 404 });
  }
  
  const formData = await request.formData();
  const action = formData.get("action");
  
  if (action === "reply") {
    const replyText = formData.get("replyText") as string;
    
    if (!replyText || replyText.trim() === "") {
      return json({ error: "Reply text is required" }, { status: 400 });
    }
    
    const newMessage = await replyToMessage(messageId, user.id, replyText);
    
    if (!newMessage) {
      return json({ error: "Failed to send reply" }, { status: 500 });
    }
    
    return json({ success: true, newMessage });
  }
  
  if (action === "delete") {
    const success = await moveMessageToTrash(messageId, user.id);
    
    if (!success) {
      return json({ error: "Failed to delete message" }, { status: 500 });
    }
    
    return json({ success: true, deleted: true });
  }
  
  return json({ error: "Invalid action" }, { status: 400 });
}

export default function MessageDetail() {
  const { message, user } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [replyText, setReplyText] = useState("");
  const navigate = useNavigate();
  const params = useParams();
  
  // If the message was deleted, redirect to the inbox
  if (actionData?.deleted) {
    navigate("/messages/inbox");
    return null;
  }
  
  // Determine if the user is the sender or recipient
  const isSender = message.senderId === user.id;
  const otherPersonName = isSender ? message.recipientName : message.senderName;
  
  // Handle reply submission
  const handleReplySubmit = (e: React.FormEvent) => {
    if (replyText.trim() === "") {
      e.preventDefault();
    }
  };
  
  // Render different content based on message type
  const renderMessageContent = () => {
    switch (message.messageType) {
      case 'dispute':
        return (
          <div className="bg-red-50 p-4 rounded-md mb-4">
            <h3 className="text-red-800 font-medium">Dispute Information</h3>
            {message.disputeData ? (
              <div className="mt-2">
                <p className="text-sm text-red-700"><strong>Status:</strong> {message.disputeData.status}</p>
                <p className="text-sm text-red-700"><strong>Reason:</strong> {message.disputeData.reason}</p>
                {message.disputeData.resolution && (
                  <p className="text-sm text-red-700"><strong>Resolution:</strong> {message.disputeData.resolution}</p>
                )}
              </div>
            ) : (
              <p className="text-sm text-red-700">No dispute details available</p>
            )}
          </div>
        );
      
      case 'inquiry':
        return (
          <div className="bg-blue-50 p-4 rounded-md mb-4">
            <h3 className="text-blue-800 font-medium">Inquiry Information</h3>
            {message.inquiryData ? (
              <div className="mt-2">
                <p className="text-sm text-blue-700"><strong>Status:</strong> {message.inquiryData.status}</p>
                {message.inquiryData.notes && (
                  <p className="text-sm text-blue-700"><strong>Notes:</strong> {message.inquiryData.notes}</p>
                )}
              </div>
            ) : (
              <p className="text-sm text-blue-700">No inquiry details available</p>
            )}
          </div>
        );
      
      case 'job_update':
        return (
          <div className="bg-green-50 p-4 rounded-md mb-4">
            <h3 className="text-green-800 font-medium">Job Update</h3>
            {message.jobTitle && (
              <p className="text-sm text-green-700"><strong>Job:</strong> {message.jobTitle}</p>
            )}
          </div>
        );
      
      default:
        return null;
    }
  };
  
  return (
    <div className="bg-white shadow overflow-hidden sm:rounded-lg">
      <div className="px-4 py-5 sm:px-6 flex justify-between items-center">
        <div>
          <h3 className="text-lg leading-6 font-medium text-gray-900">
            {isSender ? `Message to ${otherPersonName}` : `Message from ${otherPersonName}`}
          </h3>
          <p className="mt-1 max-w-2xl text-sm text-gray-500">
            {format(new Date(message.createdAt), 'PPpp')}
          </p>
        </div>
        <div>
          <Form method="post">
            <input type="hidden" name="action" value="delete" />
            <button
              type="submit"
              className="inline-flex items-center px-3 py-1.5 border border-transparent text-xs font-medium rounded-md text-white bg-red-600 hover:bg-red-700"
            >
              Delete
            </button>
          </Form>
        </div>
      </div>
      
      <div className="border-t border-gray-200">
        <div className="px-4 py-5 sm:p-6">
          {/* Type-specific content */}
          {renderMessageContent()}
          
          {/* Message content */}
          <div className="prose max-w-none">
            <p>{message.message}</p>
          </div>
          
          {/* Reply form */}
          {!message.isDeleted && (
            <div className="mt-6">
              <h4 className="text-lg font-medium text-gray-900 mb-3">Reply</h4>
              <Form method="post" onSubmit={handleReplySubmit}>
                <input type="hidden" name="action" value="reply" />
                <div>
                  <textarea
                    name="replyText"
                    rows={4}
                    className="shadow-sm block w-full focus:ring-blue-500 focus:border-blue-500 sm:text-sm border border-gray-300 rounded-md"
                    placeholder="Write your reply..."
                    value={replyText}
                    onChange={(e) => setReplyText(e.target.value)}
                  ></textarea>
                </div>
                {actionData?.error && (
                  <div className="mt-2 text-sm text-red-600">
                    {actionData.error}
                  </div>
                )}
                <div className="mt-3 flex justify-end">
                  <button
                    type="submit"
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                  >
                    Send Reply
                  </button>
                </div>
              </Form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}