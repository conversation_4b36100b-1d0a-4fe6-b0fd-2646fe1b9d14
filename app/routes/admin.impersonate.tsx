import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData } from "@remix-run/react";
import { requireAdmin, startImpersonation } from "~/utils/session.server";
import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/node";

export async function loader({ request }: LoaderFunctionArgs) {
  // Ensure only admins can access this page
  const user = await requireAdmin(request);
  
  return json({ user });
}

export async function action({ request }: ActionFunctionArgs) {
  // Ensure only admins can perform this action
  await requireAdmin(request);
  
  const formData = await request.formData();
  const role = formData.get("role");
  
  if (!role || typeof role !== "string") {
    return json({ error: "Role is required" }, { status: 400 });
  }
  
  if (!["pilot", "owner"].includes(role)) {
    return json({ error: "Invalid role selected" }, { status: 400 });
  }
  
  // Start impersonation
  return startImpersonation(request, role);
}

export default function ImpersonateRole() {
  const { user } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  
  return (
    <div className="container-custom py-8">
      <h1 className="text-2xl font-bold mb-6">Impersonate Role</h1>
      
      <div className="bg-white rounded-lg shadow-md p-6 max-w-md">
        <p className="mb-4 text-gray-700">
          This feature allows you to view the system as a user with a different role.
          While impersonating, you'll see a banner at the top of the page with an option to return to your admin role.
        </p>
        
        {actionData?.error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {actionData.error}
          </div>
        )}
        
        <Form method="post">
          <div className="mb-4">
            <label htmlFor="role" className="block text-sm font-medium text-gray-700 mb-1">
              Select Role to Impersonate
            </label>
            <select
              id="role"
              name="role"
              className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-primary-500 focus:border-primary-500"
              required
            >
              <option value="">Select a role...</option>
              <option value="pilot">Pilot</option>
              <option value="owner">Owner</option>
            </select>
          </div>
          
          <div className="flex justify-end">
            <button
              type="submit"
              className="px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              Start Impersonation
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
}