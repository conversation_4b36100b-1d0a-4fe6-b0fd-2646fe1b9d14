import { ActionFunction<PERSON>rgs, json, LoaderFunctionArgs, MetaFunction, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useSearchParams } from "@remix-run/react";
import { useEffect, useRef, useState } from "react";
import { getUserId, register, createUserSession } from "~/utils/session.server";

export const meta: MetaFunction = () => {
  return [{ title: "Register | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await getUserId(request);
  if (userId) return redirect("/");
  return json({});
}

export async function action({ request }: ActionFunctionArgs) {
  const formData = await request.formData();
  const email = formData.get("email");
  const password = formData.get("password");
  const name = formData.get("name");
  const role = formData.get("role");
  const redirectTo = formData.get("redirectTo") || "/";

  // Get business information fields if applicable
  const businessName = formData.get("businessName");
  const businessDescription = formData.get("businessDescription");
  const companyName = formData.get("companyName");
  const serviceProviderType = formData.get("serviceProviderType");

  if (!email || typeof email !== "string") {
    return json({ errors: { email: "Email is required", password: null, name: null, role: null, businessName: null, businessDescription: null, companyName: null, serviceProviderType: null } }, { status: 400 });
  }

  if (!password || typeof password !== "string" || password.length < 8) {
    return json({ 
      errors: { 
        email: null, 
        password: "Password must be at least 8 characters", 
        name: null, 
        role: null,
        businessName: null,
        businessDescription: null,
        companyName: null,
        serviceProviderType: null
      } 
    }, { status: 400 });
  }

  if (!name || typeof name !== "string") {
    return json({ errors: { email: null, password: null, name: "Name is required", role: null, businessName: null, businessDescription: null, companyName: null, serviceProviderType: null } }, { status: 400 });
  }

  if (!role || !["owner", "pilot", "business", "service_provider"].includes(role)) {
    return json({ errors: { email: null, password: null, name: null, role: "Role is required", businessName: null, businessDescription: null, companyName: null, serviceProviderType: null } }, { status: 400 });
  }

  // Validate business information fields for specific roles
  if (role === "business") {
    if (!businessName || typeof businessName !== "string") {
      return json({ errors: { email: null, password: null, name: null, role: null, businessName: "Business name is required", businessDescription: null, companyName: null, serviceProviderType: null } }, { status: 400 });
    }
  }

  if (role === "service_provider") {
    if (!companyName || typeof companyName !== "string") {
      return json({ errors: { email: null, password: null, name: null, role: null, businessName: null, businessDescription: null, companyName: "Company name is required", serviceProviderType: null } }, { status: 400 });
    }
  }

  // Set default serviceProviderType to "ferry_pilot_agency" for service_provider role
  const serviceProviderTypeValue = role === "service_provider" ? "ferry_pilot_agency" : undefined;

  const registerResult = await register({ 
    email, 
    password, 
    name, 
    role: role as "owner" | "pilot" | "business" | "service_provider",
    businessName: typeof businessName === "string" ? businessName : undefined,
    businessDescription: typeof businessDescription === "string" ? businessDescription : undefined,
    companyName: typeof companyName === "string" ? companyName : undefined,
    serviceProviderType: serviceProviderTypeValue
  }, request);

  if (!registerResult.user) {
    return json({ errors: { email: registerResult.error, password: null, name: null, role: null, businessName: null, businessDescription: null, companyName: null, serviceProviderType: null } }, { status: 400 });
  }

  // If verification is needed, redirect to verification page
  if (registerResult.verificationToken) {
    return redirect(`/verify?token=${registerResult.verificationToken}`);
  }

  return createUserSession(registerResult.user.id.toString(), redirectTo.toString());
}

export default function Register() {
  const actionData = useActionData<typeof action>();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  const emailRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);
  const nameRef = useRef<HTMLInputElement>(null);
  const [selectedRole, setSelectedRole] = useState<string>("");

  useEffect(() => {
    if (actionData?.errors?.email) {
      emailRef.current?.focus();
    } else if (actionData?.errors?.password) {
      passwordRef.current?.focus();
    } else if (actionData?.errors?.name) {
      nameRef.current?.focus();
    }
  }, [actionData]);

  const handleRoleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSelectedRole(e.target.value);
  };

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-6 text-center">Create Your FerryPros Account</h1>
      <Form method="post" className="space-y-4">
        <input type="hidden" name="redirectTo" value={redirectTo} />

        <div>
          <label htmlFor="role" className="block text-sm font-medium text-gray-800 mb-3">
            I am a:
          </label>
          <div className="grid grid-cols-2 gap-4 mb-6">
            <label 
              htmlFor="role-owner" 
              className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRole === "owner" 
                  ? "bg-blue-50 border-blue-500 shadow-md" 
                  : "border-gray-300 hover:bg-gray-50"
              }`}
            >
              <input
                id="role-owner"
                name="role"
                type="radio"
                value="owner"
                checked={selectedRole === "owner"}
                onChange={handleRoleChange}
                className="sr-only" // Hide the actual radio button
              />
              <div className="text-center">
                <div className="font-medium text-gray-800">Aircraft Owner</div>
              </div>
            </label>

            <label 
              htmlFor="role-business" 
              className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRole === "business" 
                  ? "bg-blue-50 border-blue-500 shadow-md" 
                  : "border-gray-300 hover:bg-gray-50"
              }`}
            >
              <input
                id="role-business"
                name="role"
                type="radio"
                value="business"
                checked={selectedRole === "business"}
                onChange={handleRoleChange}
                className="sr-only" // Hide the actual radio button
              />
              <div className="text-center">
                <div className="font-medium text-gray-800">Aircraft Broker</div>
              </div>
            </label>

            <label 
              htmlFor="role-pilot" 
              className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRole === "pilot" 
                  ? "bg-blue-50 border-blue-500 shadow-md" 
                  : "border-gray-300 hover:bg-gray-50"
              }`}
            >
              <input
                id="role-pilot"
                name="role"
                type="radio"
                value="pilot"
                checked={selectedRole === "pilot"}
                onChange={handleRoleChange}
                className="sr-only" // Hide the actual radio button
              />
              <div className="text-center">
                <div className="font-medium text-gray-800">Ferry Pilot</div>
              </div>
            </label>

            <label 
              htmlFor="role-service-provider" 
              className={`flex flex-col items-center justify-center p-4 border rounded-lg cursor-pointer transition-all ${
                selectedRole === "service_provider" 
                  ? "bg-blue-50 border-blue-500 shadow-md" 
                  : "border-gray-300 hover:bg-gray-50"
              }`}
            >
              <input
                id="role-service-provider"
                name="role"
                type="radio"
                value="service_provider"
                checked={selectedRole === "service_provider"}
                onChange={handleRoleChange}
                className="sr-only" // Hide the actual radio button
              />
              <div className="text-center">
                <div className="font-medium text-gray-800">Ferry Pilot Agency</div>
              </div>
            </label>
          </div>
          {actionData?.errors?.role && (
            <div className="text-red-500 text-sm mt-1" id="role-error">
              {actionData.errors.role}
            </div>
          )}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-800 mb-1">
            Email
          </label>
          <input
            ref={emailRef}
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            aria-invalid={actionData?.errors?.email ? true : undefined}
            aria-describedby="email-error"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {actionData?.errors?.email && (
            <div className="text-red-500 text-sm mt-1" id="email-error">
              {actionData.errors.email}
            </div>
          )}
        </div>

        <div>
          <label htmlFor="name" className="block text-sm font-medium text-gray-800 mb-1">
            Full Name
          </label>
          <input
            ref={nameRef}
            id="name"
            name="name"
            type="text"
            autoComplete="name"
            aria-invalid={actionData?.errors?.name ? true : undefined}
            aria-describedby="name-error"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {actionData?.errors?.name && (
            <div className="text-red-500 text-sm mt-1" id="name-error">
              {actionData.errors.name}
            </div>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-800 mb-1">
            Password
          </label>
          <input
            ref={passwordRef}
            id="password"
            name="password"
            type="password"
            autoComplete="new-password"
            aria-invalid={actionData?.errors?.password ? true : undefined}
            aria-describedby="password-error"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {actionData?.errors?.password && (
            <div className="text-red-500 text-sm mt-1" id="password-error">
              {actionData.errors.password}
            </div>
          )}
        </div>

        {selectedRole === "pilot" && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <p className="text-sm text-gray-700 mb-4">
              The pilot verification process will begin after registration.
            </p>
          </div>
        )}

        {selectedRole === "business" && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <p className="text-sm text-gray-700 mb-4">
              Please provide your business information. After registration, you'll be able to add employees to your account.
            </p>
            <div className="space-y-4">
              <div>
                <label htmlFor="businessName" className="block text-sm font-medium text-gray-800 mb-1">
                  Business Name
                </label>
                <input
                  id="businessName"
                  name="businessName"
                  type="text"
                  aria-invalid={actionData?.errors?.businessName ? true : undefined}
                  aria-describedby="businessName-error"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                {actionData?.errors?.businessName && (
                  <div className="text-red-500 text-sm mt-1" id="businessName-error">
                    {actionData.errors.businessName}
                  </div>
                )}
              </div>
              <div>
                <label htmlFor="businessDescription" className="block text-sm font-medium text-gray-800 mb-1">
                  Business Description
                </label>
                <textarea
                  id="businessDescription"
                  name="businessDescription"
                  rows={3}
                  aria-invalid={actionData?.errors?.businessDescription ? true : undefined}
                  aria-describedby="businessDescription-error"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Describe your aircraft brokerage business"
                ></textarea>
                {actionData?.errors?.businessDescription && (
                  <div className="text-red-500 text-sm mt-1" id="businessDescription-error">
                    {actionData.errors.businessDescription}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {selectedRole === "service_provider" && (
          <div className="border-t border-gray-200 pt-4 mt-4">
            <p className="text-sm text-gray-700 mb-4">
              Please provide your Ferry Pilot Agency information. After registration, you'll be able to add pilots to your company.
            </p>
            <div className="space-y-4">
              <div>
                <label htmlFor="companyName" className="block text-sm font-medium text-gray-800 mb-1">
                  Company Name
                </label>
                <input
                  id="companyName"
                  name="companyName"
                  type="text"
                  aria-invalid={actionData?.errors?.companyName ? true : undefined}
                  aria-describedby="companyName-error"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
                {actionData?.errors?.companyName && (
                  <div className="text-red-500 text-sm mt-1" id="companyName-error">
                    {actionData.errors.companyName}
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        <div>
          <button
            type="submit"
            className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Create Account
          </button>
        </div>
      </Form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-700">
          Already have an account?{" "}
          <Link
            to={{
              pathname: "/login",
              search: searchParams.toString(),
            }}
            className="font-medium text-blue-600 hover:text-blue-500"
          >
            Log in
          </Link>
        </p>
      </div>
    </div>
  );
}
