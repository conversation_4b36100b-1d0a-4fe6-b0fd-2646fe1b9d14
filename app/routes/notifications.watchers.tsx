import { json, LoaderFunctionArgs, ActionFunctionArgs } from "@remix-run/node";
import { useLoaderData, useActionData, Form, useSubmit, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { getUser } from "~/utils/session.server";
import { <PERSON>riteriaField, CriteriaOperator } from "~/types/verification";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await getUser(request);

  if (!user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only pilots can access this page
  if (user.role !== "pilot") {
    return json({ error: "Only pilots can access this page" }, { status: 403 });
  }

  // Fetch notification watchers from the API
  const response = await fetch(`${new URL(request.url).origin}/api/notification_watchers`, {
    headers: {
      Cookie: request.headers.get("Cookie") || ""
    }
  });

  const data = await response.json();

  return json({
    user,
    watchers: data.watchers || []
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const user = await getUser(request);

  if (!user) {
    return json({ error: "Unauthorized" }, { status: 401 });
  }

  // Only pilots can access this page
  if (user.role !== "pilot") {
    return json({ error: "Only pilots can access this page" }, { status: 403 });
  }

  // Forward the request to the API
  const formData = await request.formData();

  const response = await fetch(`${new URL(request.url).origin}/api/notification_watchers`, {
    method: "POST",
    headers: {
      Cookie: request.headers.get("Cookie") || ""
    },
    body: formData
  });

  const data = await response.json();

  return json(data);
}

export default function NotificationWatchers() {
  const { user, watchers } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const submit = useSubmit();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [showNewWatcherForm, setShowNewWatcherForm] = useState(false);
  const [editingWatcherId, setEditingWatcherId] = useState<number | null>(null);

  // State for the new watcher form
  const [newWatcherName, setNewWatcherName] = useState("");
  const [newWatcherCriteria, setNewWatcherCriteria] = useState<any[]>([]);

  // Function to add a new criterion to the form
  const addCriterion = () => {
    setNewWatcherCriteria([
      ...newWatcherCriteria,
      {
        field_name: CriteriaField.AIRCRAFT_TYPE,
        operator: CriteriaOperator.EQUALS,
        value: ""
      }
    ]);
  };

  // Function to remove a criterion from the form
  const removeCriterion = (index: number) => {
    setNewWatcherCriteria(newWatcherCriteria.filter((_, i) => i !== index));
  };

  // Function to update a criterion in the form
  const updateCriterion = (index: number, field: string, value: string) => {
    const updatedCriteria = [...newWatcherCriteria];
    updatedCriteria[index] = {
      ...updatedCriteria[index],
      [field]: value
    };
    setNewWatcherCriteria(updatedCriteria);
  };

  // Function to handle form submission
  const handleCreateWatcher = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    formData.append("action", "create");
    formData.append("name", newWatcherName);
    formData.append("enabled", "true");
    formData.append("criteria", JSON.stringify(newWatcherCriteria));

    submit(formData, { method: "post" });

    // Reset form
    setNewWatcherName("");
    setNewWatcherCriteria([]);
    setShowNewWatcherForm(false);
  };

  // Function to handle watcher deletion
  const handleDeleteWatcher = (watcherId: number) => {
    if (confirm("Are you sure you want to delete this notification watcher?")) {
      const formData = new FormData();
      formData.append("action", "delete");
      formData.append("id", watcherId.toString());

      submit(formData, { method: "post" });
    }
  };

  // Function to handle watcher toggle
  const handleToggleWatcher = (watcher: any) => {
    const formData = new FormData();
    formData.append("action", "update");
    formData.append("id", watcher.id.toString());
    formData.append("enabled", (!watcher.enabled).toString());

    submit(formData, { method: "post" });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Notification Watchers</h1>
        <button
          type="button"
          onClick={() => setShowNewWatcherForm(!showNewWatcherForm)}
          className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
        >
          {showNewWatcherForm ? "Cancel" : "Create New Watcher"}
        </button>
      </div>

      {actionData?.error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {actionData.error}
        </div>
      )}

      {actionData?.success && !actionData.error && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          Operation successful
        </div>
      )}

      {/* New Watcher Form */}
      {showNewWatcherForm && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Create New Notification Watcher</h2>
          <form onSubmit={handleCreateWatcher} className="space-y-4">
            <div>
              <label htmlFor="name" className="block text-sm font-medium text-gray-700">
                Name
              </label>
              <input
                type="text"
                id="name"
                value={newWatcherName}
                onChange={(e) => setNewWatcherName(e.target.value)}
                className="mt-1 block w-full border border-gray-300 rounded-md shadow-sm p-2"
                required
              />
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <label className="block text-sm font-medium text-gray-700">
                  Criteria
                </label>
                <button
                  type="button"
                  onClick={addCriterion}
                  className="text-sm text-blue-600 hover:text-blue-800"
                >
                  + Add Criterion
                </button>
              </div>

              {newWatcherCriteria.length === 0 ? (
                <p className="text-gray-500 text-sm">
                  No criteria added yet. Add criteria to specify when you want to be notified.
                </p>
              ) : (
                <div className="space-y-3">
                  {newWatcherCriteria.map((criterion, index) => (
                    <div key={index} className="flex items-center gap-2 p-3 bg-gray-50 rounded">
                      <select
                        value={criterion.field_name}
                        onChange={(e) => updateCriterion(index, "field_name", e.target.value)}
                        className="border border-gray-300 rounded-md shadow-sm p-2"
                      >
                        <option value={CriteriaField.AIRCRAFT_TYPE}>Aircraft Type</option>
                        <option value={CriteriaField.ORIGIN}>Origin</option>
                        <option value={CriteriaField.DESTINATION}>Destination</option>
                        <option value={CriteriaField.BUDGET}>Budget</option>
                        <option value={CriteriaField.ESTIMATED_DATE}>Estimated Date</option>
                        <option value={CriteriaField.REQUIREMENTS}>Requirements</option>
                      </select>

                      <select
                        value={criterion.operator}
                        onChange={(e) => updateCriterion(index, "operator", e.target.value)}
                        className="border border-gray-300 rounded-md shadow-sm p-2"
                      >
                        <option value={CriteriaOperator.EQUALS}>Equals</option>
                        <option value={CriteriaOperator.CONTAINS}>Contains</option>
                        {(criterion.field_name === CriteriaField.BUDGET || 
                          criterion.field_name === CriteriaField.ESTIMATED_DATE) && (
                          <>
                            <option value={CriteriaOperator.GREATER_THAN}>Greater Than</option>
                            <option value={CriteriaOperator.LESS_THAN}>Less Than</option>
                            <option value={CriteriaOperator.BETWEEN}>Between</option>
                          </>
                        )}
                      </select>

                      {criterion.operator === CriteriaOperator.BETWEEN ? (
                        <div className="flex-1">
                          <input
                            type={criterion.field_name === CriteriaField.ESTIMATED_DATE ? "date" : "text"}
                            value={criterion.value.split(',')[0] || ''}
                            onChange={(e) => {
                              const secondValue = criterion.value.split(',')[1] || '';
                              updateCriterion(index, "value", `${e.target.value},${secondValue}`);
                            }}
                            className="border border-gray-300 rounded-md shadow-sm p-2 w-full mb-1"
                            placeholder="Min value"
                          />
                          <input
                            type={criterion.field_name === CriteriaField.ESTIMATED_DATE ? "date" : "text"}
                            value={criterion.value.split(',')[1] || ''}
                            onChange={(e) => {
                              const firstValue = criterion.value.split(',')[0] || '';
                              updateCriterion(index, "value", `${firstValue},${e.target.value}`);
                            }}
                            className="border border-gray-300 rounded-md shadow-sm p-2 w-full"
                            placeholder="Max value"
                          />
                        </div>
                      ) : (
                        <input
                          type={criterion.field_name === CriteriaField.ESTIMATED_DATE ? "date" : "text"}
                          value={criterion.value}
                          onChange={(e) => updateCriterion(index, "value", e.target.value)}
                          className="flex-1 border border-gray-300 rounded-md shadow-sm p-2"
                          placeholder="Value"
                        />
                      )}

                      <button
                        type="button"
                        onClick={() => removeCriterion(index)}
                        className="text-red-600 hover:text-red-800"
                        aria-label="Remove criterion"
                      >
                        &times;
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>

            <div className="flex justify-end">
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-blue-300"
              >
                {isSubmitting ? "Creating..." : "Create Watcher"}
              </button>
            </div>
          </form>
        </div>
      )}

      {/* Watchers List */}
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="px-6 py-4 border-b">
          <h2 className="text-xl font-semibold">Your Notification Watchers</h2>
          <p className="text-gray-600">
            Get notified when new jobs match your criteria
          </p>
        </div>

        {watchers.length === 0 ? (
          <div className="p-6 text-center text-gray-500">
            You don't have any notification watchers yet. Create one to get started.
          </div>
        ) : (
          <ul className="divide-y divide-gray-200">
            {watchers.map((watcher: any) => (
              <li key={watcher.id} className="p-6">
                <div className="flex justify-between items-start">
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="text-lg font-medium">{watcher.name}</h3>
                      <span className={`px-2 py-1 text-xs rounded-full ${watcher.enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>
                        {watcher.enabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>

                    <div className="mt-2">
                      <h4 className="text-sm font-medium text-gray-700 mb-1">Criteria:</h4>
                      {watcher.criteria && watcher.criteria.length > 0 ? (
                        <ul className="space-y-1">
                          {watcher.criteria.map((criterion: any, index: number) => (
                            <li key={index} className="text-sm text-gray-600">
                              <span className="font-medium">{criterion.field_name.replace(/_/g, ' ')}:</span>
                              {' '}
                              {criterion.operator === CriteriaOperator.BETWEEN ? (
                                <>between {criterion.value.split(',')[0]} and {criterion.value.split(',')[1]}</>
                              ) : (
                                <>{criterion.operator} {criterion.value}</>
                              )}
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <p className="text-sm text-gray-500">No criteria specified (matches all jobs)</p>
                      )}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <button
                      type="button"
                      onClick={() => handleToggleWatcher(watcher)}
                      className={`px-3 py-1 text-sm rounded ${watcher.enabled ? 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200' : 'bg-green-100 text-green-800 hover:bg-green-200'}`}
                    >
                      {watcher.enabled ? 'Disable' : 'Enable'}
                    </button>
                    <button
                      type="button"
                      onClick={() => handleDeleteWatcher(watcher.id)}
                      className="px-3 py-1 bg-red-100 text-red-800 text-sm rounded hover:bg-red-200"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>
    </div>
  );
}
