import { json, LoaderFunctionArgs } from "@remix-run/node";
import { Link, useLoaderData, useFetcher } from "@remix-run/react";
import { requireUser } from "~/utils/session.server";
import { getTrashMessages, restoreMessageFromTrash } from "~/utils/messaging.server";
import { formatDistanceToNow } from "date-fns";

export async function loader({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const messages = await getTrashMessages(user.id);

  return json({ messages, user });
}

export async function action({ request }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const formData = await request.formData();
  const messageId = Number(formData.get("messageId"));
  const action = formData.get("action");
  
  if (action === "restore" && messageId) {
    await restoreMessageFromTrash(messageId, user.id);
    return json({ success: true });
  }
  
  return json({ success: false });
}

export default function Trash() {
  const { messages, user } = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  
  const handleRestore = (messageId: number) => {
    fetcher.submit(
      { messageId: messageId.toString(), action: "restore" },
      { method: "post" }
    );
  };
  
  if (messages.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-xl font-medium text-gray-500">Trash is empty</h2>
        <p className="mt-2 text-gray-400">Messages you delete will appear here.</p>
      </div>
    );
  }
  
  return (
    <div>
      <div className="bg-white shadow overflow-hidden sm:rounded-md">
        <ul className="divide-y divide-gray-200">
          {messages.map((message) => (
            <li key={message.id}>
              <div className="px-4 py-4 sm:px-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <p className="text-sm font-medium text-gray-900">
                      {message.senderId === message.recipientId ? 
                        "Note to self" : 
                        (message.senderId === user.id ? 
                          `To: ${message.recipientName}` : 
                          `From: ${message.senderName}`)}
                    </p>
                  </div>
                  <div className="ml-2 flex-shrink-0 flex space-x-2">
                    <button
                      onClick={() => handleRestore(message.id)}
                      className="px-2 py-1 text-xs font-medium text-green-700 bg-green-100 rounded-md hover:bg-green-200"
                    >
                      Restore
                    </button>
                    <p className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-100 text-gray-800">
                      {message.messageType}
                    </p>
                  </div>
                </div>
                <div className="mt-2 sm:flex sm:justify-between">
                  <div className="sm:flex">
                    <p className="text-sm text-gray-500 truncate" style={{ maxWidth: '500px' }}>
                      {message.message}
                    </p>
                  </div>
                  <div className="mt-2 flex items-center text-sm text-gray-500 sm:mt-0">
                    <p>
                      {formatDistanceToNow(new Date(message.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
                {message.jobTitle && (
                  <div className="mt-2">
                    <span className="text-xs text-gray-500">
                      Re: {message.jobTitle}
                    </span>
                  </div>
                )}
              </div>
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}