import { ActionFunction<PERSON>rgs, json, LoaderFunction<PERSON>rgs, MetaFunction, redirect } from "@remix-run/node";
import { Form, Link, useActionData, useSearchParams } from "@remix-run/react";
import { useEffect, useRef } from "react";
import { getUserId, login, createUserSession } from "~/utils/session.server";

export const meta: MetaFunction = () => {
  return [{ title: "Login | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  const userId = await getUserId(request);
  if (userId) return redirect("/");
  return json({});
}

export async function action({ request }: ActionFunctionArgs) {
  console.log("Login action called");
  const formData = await request.formData();
  const email = formData.get("email");
  const password = formData.get("password");
  const redirectTo = formData.get("redirectTo") || "/";

  console.log("Form data:", { email, redirectTo });

  if (!email || typeof email !== "string") {
    console.log("Email is required");
    return json({ errors: { email: "Email is required", password: null } }, { status: 400 });
  }

  if (!password || typeof password !== "string") {
    console.log("Password is required");
    return json({ errors: { email: null, password: "Password is required" } }, { status: 400 });
  }

  console.log("Calling login function");
  const loginResult = await login({ email, password }, request);
  console.log("Login result:", loginResult);
  console.log("Login result structure:", JSON.stringify(loginResult, null, 2));

  // Check if there's an error
  if (!loginResult.user) {
    console.log("Login error:", 'error' in loginResult ? loginResult.error : "Unknown error");
    return json({ errors: { email: 'error' in loginResult ? loginResult.error : "Unknown error", password: null } }, { status: 400 });
  }

  console.log("Creating user session with ID:", loginResult.user.id);
  console.log("User ID type:", typeof loginResult.user.id);
  console.log("User ID toString:", loginResult.user.id.toString());

  // Create user session first
  const userSession = await createUserSession(loginResult.user.id.toString(), redirectTo.toString());

  // Check if verification is needed
  if ('verificationNeeded' in loginResult && loginResult.verificationNeeded) {
    console.log("Verification needed");
    return redirect(`/verify?token=${loginResult.verificationToken}`, {
      headers: userSession.headers
    });
  }

  // Check if license information is needed for pilots
  if ('licenseInfoNeeded' in loginResult && loginResult.licenseInfoNeeded) {
    console.log("License info needed");
    return redirect("/license-info", {
      headers: userSession.headers
    });
  }

  // Check if MFA is required
  if ('mfaRequired' in loginResult && loginResult.mfaRequired) {
    console.log("MFA required");
    return redirect(`/login/mfa?userId=${loginResult.user.id}`, {
      headers: userSession.headers
    });
  }

  // If no special redirects are needed, return the original session response
  return userSession;
}

export default function Login() {
  const actionData = useActionData<typeof action>();
  const [searchParams] = useSearchParams();
  const redirectTo = searchParams.get("redirectTo") || "/";
  const emailRef = useRef<HTMLInputElement>(null);
  const passwordRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (actionData?.errors?.email) {
      emailRef.current?.focus();
    } else if (actionData?.errors?.password) {
      passwordRef.current?.focus();
    }
  }, [actionData]);

  return (
    <div className="max-w-md mx-auto mt-10 p-6 bg-white rounded-lg shadow-md">
      <h1 className="text-2xl font-bold mb-6 text-center">Login to FerryPros</h1>
      <Form method="post" className="space-y-4">
        <input type="hidden" name="redirectTo" value={redirectTo} />
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-800 mb-1">
            Email
          </label>
          <input
            ref={emailRef}
            id="email"
            name="email"
            type="email"
            autoComplete="email"
            aria-invalid={actionData?.errors?.email ? true : undefined}
            aria-describedby="email-error"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {actionData?.errors?.email && (
            <div className="text-red-500 text-sm mt-1" id="email-error">
              {actionData.errors.email}
            </div>
          )}
        </div>

        <div>
          <label htmlFor="password" className="block text-sm font-medium text-gray-800 mb-1">
            Password
          </label>
          <input
            ref={passwordRef}
            id="password"
            name="password"
            type="password"
            autoComplete="current-password"
            aria-invalid={actionData?.errors?.password ? true : undefined}
            aria-describedby="password-error"
            className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
          />
          {actionData?.errors?.password && (
            <div className="text-red-500 text-sm mt-1" id="password-error">
              {actionData.errors.password}
            </div>
          )}
        </div>

        <div>
          <button
            type="submit"
            className="w-full py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Log in
          </button>
        </div>
      </Form>

      <div className="mt-6 text-center">
        <p className="text-sm text-gray-700">
          Don't have an account?{" "}
          <Link
            to={{
              pathname: "/register",
              search: searchParams.toString(),
            }}
            className="font-medium text-blue-600 hover:text-blue-500"
          >
            Register
          </Link>
        </p>
      </div>
    </div>
  );
}
