import { json, redirect, ActionFunctionArgs, LoaderFunctionArgs, MetaFunction } from "@remix-run/node";
import { Form, useActionData, useLoaderData, Link, useFetcher } from "@remix-run/react";
import { requireUserId, getUser } from "~/utils/session.server";
import { createJob } from "~/utils/jobs.server";
import { useState, useEffect, useRef } from "react";
import { aircraftTypes, getAircraftTypesByCategory } from "~/utils/aircraftTypes";
import { canFeatureJob } from "~/utils/subscriptions.server";

export const meta: MetaFunction = () => {
  return [{ title: "Post New Job | FerryPros" }];
};

export async function loader({ request }: LoaderFunctionArgs) {
  // Ensure the user is logged in
  const userId = await requireUserId(request);

  // Get the user to check their role
  const user = await getUser(request);

  // If the user is not an owner, redirect to the dashboard
  if (user?.role !== "owner" && user?.role !== "admin") {
    return redirect("/dashboard");
  }

  // Check if the user can feature jobs based on their subscription
  const canFeature = await canFeatureJob(parseInt(userId));

  return json({ 
    user,
    canFeature
  });
}

export async function action({ request }: ActionFunctionArgs) {
  const userId = await requireUserId(request);
  const user = await getUser(request);

  // Ensure the user is an owner
  if (user?.role !== "owner") {
    return json({ error: "Only aircraft owners can post jobs" }, { status: 403 });
  }

  const formData = await request.formData();
  const title = formData.get("title");
  const description = formData.get("description");
  const aircraftType = formData.get("aircraftType");
  const origin = formData.get("origin");
  const destination = formData.get("destination");
  const estimatedDate = formData.get("estimatedDate");
  const budget = formData.get("budget");
  const requirements = formData.get("requirements");
  const isFeatured = formData.get("isFeatured") === "true";

  // Validate the form data
  const errors = {
    title: title ? null : "Title is required",
    description: description ? null : "Description is required",
    aircraftType: aircraftType ? null : "Aircraft type is required",
    origin: origin ? null : "Origin is required",
    destination: destination ? null : "Destination is required",
    estimatedDate: estimatedDate ? null : "Estimated date is required",
  };

  // Return errors if any fields are invalid
  const hasErrors = Object.values(errors).some(error => error);
  if (hasErrors) {
    return json({ errors, values: Object.fromEntries(formData) });
  }

  // Check if the user can feature jobs if they're trying to feature this job
  if (isFeatured) {
    const canFeature = await canFeatureJob(parseInt(userId));
    if (!canFeature) {
      return json({ 
        error: "Your subscription plan does not include featured listings. Please upgrade your plan to feature jobs.",
        values: Object.fromEntries(formData)
      }, { status: 403 });
    }
  }

  // Create the job in the database
  try {
    const newJob = await createJob({
      owner_id: parseInt(userId),
      title: title?.toString() || "",
      description: description?.toString() || "",
      aircraft_type: aircraftType?.toString() || "",
      origin: origin?.toString() || "",
      destination: destination?.toString() || "",
      estimated_date: estimatedDate?.toString() || "",
      budget: budget ? parseFloat(budget.toString()) : undefined,
      requirements: requirements?.toString(),
      is_featured: isFeatured,
      featured_duration_days: isFeatured ? 30 : undefined // Default to 30 days for featured listings
    });

    return redirect(`/jobs/${newJob.id}?created=true`);
  } catch (error) {
    console.error("Error creating job:", error);
    return json({ 
      error: "Failed to create job. Please try again.", 
      values: Object.fromEntries(formData) 
    }, { status: 500 });
  }
}

export default function NewJob() {
  const { user, canFeature } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const [formValues, setFormValues] = useState(actionData?.values || {});
  const [isFeatured, setIsFeatured] = useState(false);

  // Airport autocomplete state
  const airportFetcher = useFetcher();
  const [originQuery, setOriginQuery] = useState("");
  const [destinationQuery, setDestinationQuery] = useState("");
  const [originSuggestions, setOriginSuggestions] = useState([]);
  const [destinationSuggestions, setDestinationSuggestions] = useState([]);
  const [showOriginSuggestions, setShowOriginSuggestions] = useState(false);
  const [showDestinationSuggestions, setShowDestinationSuggestions] = useState(false);

  // Refs for autocomplete dropdowns
  const originRef = useRef(null);
  const destinationRef = useRef(null);

  // Group aircraft types by category
  const aircraftTypesByCategory = getAircraftTypesByCategory();

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormValues(prev => ({ ...prev, [name]: value }));
  };

  // Handle airport search for origin
  const handleOriginSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setOriginQuery(value);
    setFormValues(prev => ({ ...prev, origin: value }));

    if (value.length >= 2) {
      airportFetcher.load(`/api/airports?query=${encodeURIComponent(value)}`);
      setShowOriginSuggestions(true);
    } else {
      setOriginSuggestions([]);
      setShowOriginSuggestions(false);
    }
  };

  // Handle airport search for destination
  const handleDestinationSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setDestinationQuery(value);
    setFormValues(prev => ({ ...prev, destination: value }));

    if (value.length >= 2) {
      airportFetcher.load(`/api/airports?query=${encodeURIComponent(value)}`);
      setShowDestinationSuggestions(true);
    } else {
      setDestinationSuggestions([]);
      setShowDestinationSuggestions(false);
    }
  };

  // Handle selection of airport from suggestions
  const handleSelectOrigin = (airport: any) => {
    setFormValues(prev => ({ ...prev, origin: airport.label }));
    setOriginQuery(airport.label);
    setShowOriginSuggestions(false);
  };

  const handleSelectDestination = (airport: any) => {
    setFormValues(prev => ({ ...prev, destination: airport.label }));
    setDestinationQuery(airport.label);
    setShowDestinationSuggestions(false);
  };

  // Close suggestions when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (originRef.current && !(originRef.current as any).contains(event.target)) {
        setShowOriginSuggestions(false);
      }
      if (destinationRef.current && !(destinationRef.current as any).contains(event.target)) {
        setShowDestinationSuggestions(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Update suggestions when fetcher data changes
  useEffect(() => {
    if (airportFetcher.data && showOriginSuggestions) {
      setOriginSuggestions(airportFetcher.data);
    }
    if (airportFetcher.data && showDestinationSuggestions) {
      setDestinationSuggestions(airportFetcher.data);
    }
  }, [airportFetcher.data, showOriginSuggestions, showDestinationSuggestions]);

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-4">
        <Link to="/dashboard" className="text-blue-600 hover:underline">
          ← Back to Dashboard
        </Link>
        <h1 className="text-2xl font-bold">Post a New Ferry Job</h1>
      </div>

      <div className="bg-white shadow rounded-lg p-6">
        <Form method="post" className="space-y-6">
          <div className="grid md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="title" className="block font-medium text-gray-800 mb-1">
                  Job Title
                </label>
                <input
                  type="text"
                  id="title"
                  name="title"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.title}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.title ? true : undefined}
                  aria-errormessage={actionData?.errors?.title ? "title-error" : undefined}
                />
                {actionData?.errors?.title && (
                  <p className="text-red-500 text-sm mt-1" id="title-error">
                    {actionData.errors.title}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="aircraftType" className="block font-medium text-gray-800 mb-1">
                  Aircraft Type
                </label>
                <select
                  id="aircraftType"
                  name="aircraftType"
                  className="w-full border rounded-md px-3 py-2"
                  value={formValues.aircraftType || ""}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.aircraftType ? true : undefined}
                  aria-errormessage={actionData?.errors?.aircraftType ? "aircraftType-error" : undefined}
                >
                  <option value="">Select an aircraft type</option>
                  {Object.entries(aircraftTypesByCategory).map(([category, types]) => (
                    <optgroup key={category} label={category}>
                      {types.map((type) => (
                        <option key={type.value} value={type.value}>
                          {type.label}
                        </option>
                      ))}
                    </optgroup>
                  ))}
                </select>
                {actionData?.errors?.aircraftType && (
                  <p className="text-red-500 text-sm mt-1" id="aircraftType-error">
                    {actionData.errors.aircraftType}
                  </p>
                )}
              </div>

              <div ref={originRef} className="relative">
                <label htmlFor="origin" className="block font-medium text-gray-800 mb-1">
                  Origin (Airport Code)
                </label>
                <input
                  type="text"
                  id="origin"
                  name="origin"
                  className="w-full border rounded-md px-3 py-2"
                  value={originQuery}
                  onChange={handleOriginSearch}
                  placeholder="Search for airport by name or code"
                  aria-invalid={actionData?.errors?.origin ? true : undefined}
                  aria-errormessage={actionData?.errors?.origin ? "origin-error" : undefined}
                />
                {showOriginSuggestions && originSuggestions.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border max-h-60 overflow-auto">
                    {originSuggestions.map((airport: any, index: number) => (
                      <div
                        key={index}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleSelectOrigin(airport)}
                      >
                        {airport.label}
                      </div>
                    ))}
                  </div>
                )}
                {actionData?.errors?.origin && (
                  <p className="text-red-500 text-sm mt-1" id="origin-error">
                    {actionData.errors.origin}
                  </p>
                )}
              </div>

              <div ref={destinationRef} className="relative">
                <label htmlFor="destination" className="block font-medium text-gray-800 mb-1">
                  Destination (Airport Code)
                </label>
                <input
                  type="text"
                  id="destination"
                  name="destination"
                  className="w-full border rounded-md px-3 py-2"
                  value={destinationQuery}
                  onChange={handleDestinationSearch}
                  placeholder="Search for airport by name or code"
                  aria-invalid={actionData?.errors?.destination ? true : undefined}
                  aria-errormessage={actionData?.errors?.destination ? "destination-error" : undefined}
                />
                {showDestinationSuggestions && destinationSuggestions.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white shadow-lg rounded-md border max-h-60 overflow-auto">
                    {destinationSuggestions.map((airport: any, index: number) => (
                      <div
                        key={index}
                        className="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                        onClick={() => handleSelectDestination(airport)}
                      >
                        {airport.label}
                      </div>
                    ))}
                  </div>
                )}
                {actionData?.errors?.destination && (
                  <p className="text-red-500 text-sm mt-1" id="destination-error">
                    {actionData.errors.destination}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="estimatedDate" className="block font-medium text-gray-800 mb-1">
                  Estimated Date
                </label>
                <input
                  type="date"
                  id="estimatedDate"
                  name="estimatedDate"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.estimatedDate}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.estimatedDate ? true : undefined}
                  aria-errormessage={actionData?.errors?.estimatedDate ? "estimatedDate-error" : undefined}
                />
                {actionData?.errors?.estimatedDate && (
                  <p className="text-red-500 text-sm mt-1" id="estimatedDate-error">
                    {actionData.errors.estimatedDate}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="budget" className="block font-medium text-gray-800 mb-1">
                  Budget (USD)
                </label>
                <input
                  type="number"
                  id="budget"
                  name="budget"
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.budget}
                  onChange={handleInputChange}
                  min="0"
                  step="100"
                />
              </div>

              {canFeature && (
                <div className="mt-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="isFeatured"
                      name="isFeatured"
                      value="true"
                      checked={isFeatured}
                      onChange={(e) => setIsFeatured(e.target.checked)}
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <label htmlFor="isFeatured" className="ml-2 block text-sm text-gray-900">
                      Feature this job (Premium)
                    </label>
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Featured jobs appear at the top of search results and get more visibility.
                  </p>
                </div>
              )}
            </div>

            <div className="space-y-4">
              <div>
                <label htmlFor="description" className="block font-medium text-gray-800 mb-1">
                  Job Description
                </label>
                <textarea
                  id="description"
                  name="description"
                  rows={4}
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.description}
                  onChange={handleInputChange}
                  aria-invalid={actionData?.errors?.description ? true : undefined}
                  aria-errormessage={actionData?.errors?.description ? "description-error" : undefined}
                ></textarea>
                {actionData?.errors?.description && (
                  <p className="text-red-500 text-sm mt-1" id="description-error">
                    {actionData.errors.description}
                  </p>
                )}
              </div>

              <div>
                <label htmlFor="requirements" className="block font-medium text-gray-800 mb-1">
                  Pilot Requirements
                </label>
                <textarea
                  id="requirements"
                  name="requirements"
                  rows={4}
                  className="w-full border rounded-md px-3 py-2"
                  defaultValue={formValues.requirements}
                  onChange={handleInputChange}
                  placeholder="E.g., Minimum flight hours, certifications, experience with specific aircraft"
                ></textarea>
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-3">
            <Link
              to="/dashboard"
              className="px-4 py-2 border border-gray-300 rounded hover:bg-gray-50"
            >
              Cancel
            </Link>
            <button
              type="submit"
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Post Job
            </button>
          </div>
        </Form>
      </div>
    </div>
  );
}
