import { json, type ActionFunctionArgs, type LoaderFunctionArgs } from "@remix-run/node";
import { Form, Link, useActionData, useLoaderData, useNavigation, useParams } from "@remix-run/react";
import { useState } from "react";
import { requireUser } from "~/utils/session.server";
import { getJobById } from "~/utils/jobs.server";
import {
  findMatchingPilots,
  quickMatchJob,
  getQuickMatchSettings,
  saveQuickMatchSettings,
  getPreApprovedPilots,
  addPreApprovedPilot,
  removePreApprovedPilot,
  getInstantBookingsByJob,
  updateInstantBookingStatus,
  type InstantBooking,
  type PreApprovedPilot
} from "~/utils/instantBooking.server";
import { getUserById, searchPilots } from "~/utils/users.server";

export async function loader({ request, params }: LoaderFunctionArgs) {
  const user = await requireUser(request);
  const jobId = parseInt(params.jobId || "0", 10);

  if (!jobId) {
    throw new Response("Job ID is required", { status: 400 });
  }

  // Get job details
  const job = await getJobById(jobId);
  if (!job) {
    throw new Response("Job not found", { status: 404 });
  }

  // Check if user is the job owner
  if (job.ownerId !== user.id && user.role !== 'admin') {
    throw new Response("Not authorized", { status: 403 });
  }

  // Get quick match settings
  const quickMatchSettings = await getQuickMatchSettings(user.id);

  // Get pre-approved pilots
  const preApprovedPilots = await getPreApprovedPilots(user.id);

  // Get pilot details for pre-approved pilots
  const preApprovedPilotsWithDetails = await Promise.all(
    preApprovedPilots.map(async (pilot) => {
      const pilotUser = await getUserById(pilot.pilotId);
      return {
        ...pilot,
        pilot: pilotUser
      };
    })
  );

  // Get instant bookings for this job
  const instantBookings = await getInstantBookingsByJob(jobId);

  // Get pilot details for instant bookings
  const instantBookingsWithDetails = await Promise.all(
    instantBookings.map(async (booking) => {
      const pilotUser = await getUserById(booking.pilotId);
      return {
        ...booking,
        pilot: pilotUser
      };
    })
  );

  return json({ 
    user, 
    job, 
    quickMatchSettings, 
    preApprovedPilots: preApprovedPilotsWithDetails,
    instantBookings: instantBookingsWithDetails
  });
}

export async function action({ request, params }: ActionFunctionArgs) {
  const user = await requireUser(request);
  const jobId = parseInt(params.jobId || "0", 10);

  if (!jobId) {
    throw new Response("Job ID is required", { status: 400 });
  }

  // Get job details
  const job = await getJobById(jobId);
  if (!job) {
    throw new Response("Job not found", { status: 404 });
  }

  // Check if user is the job owner
  if (job.ownerId !== user.id && user.role !== 'admin') {
    throw new Response("Not authorized", { status: 403 });
  }

  const formData = await request.formData();
  const intent = formData.get("intent") as string;

  // Handle different form intents
  switch (intent) {
    case "saveSettings": {
      const preferredRatingMin = formData.get("preferredRatingMin") ? 
        parseInt(formData.get("preferredRatingMin") as string, 10) : null;

      const preferredExperienceHoursMin = formData.get("preferredExperienceHoursMin") ? 
        parseInt(formData.get("preferredExperienceHoursMin") as string, 10) : null;

      const preferredAircraftTypes = formData.get("preferredAircraftTypes") as string || null;

      const maxPricePerHour = formData.get("maxPricePerHour") ? 
        parseFloat(formData.get("maxPricePerHour") as string) : null;

      const preferPreApprovedOnly = formData.get("preferPreApprovedOnly") === "true";
      const autoApproveMatches = formData.get("autoApproveMatches") === "true";

      try {
        await saveQuickMatchSettings(user.id, {
          preferredRatingMin,
          preferredExperienceHoursMin,
          preferredAircraftTypes,
          maxPricePerHour,
          preferPreApprovedOnly,
          autoApproveMatches
        });

        return json({ 
          success: true, 
          message: "Quick match settings saved successfully",
          action: "saveSettings"
        });
      } catch (error) {
        return json({ 
          errors: { _form: "Failed to save quick match settings" },
          success: false,
          action: "saveSettings"
        });
      }
    }

    case "addPreApproved": {
      const pilotId = parseInt(formData.get("pilotId") as string, 10);
      const notes = formData.get("notes") as string || null;

      if (isNaN(pilotId) || pilotId <= 0) {
        return json({ 
          errors: { _form: "Valid pilot ID is required" },
          success: false,
          action: "addPreApproved"
        });
      }

      try {
        await addPreApprovedPilot(user.id, pilotId, notes);
        return json({ 
          success: true, 
          message: "Pilot added to pre-approved list",
          action: "addPreApproved"
        });
      } catch (error) {
        return json({ 
          errors: { _form: "Failed to add pilot to pre-approved list" },
          success: false,
          action: "addPreApproved"
        });
      }
    }

    case "removePreApproved": {
      const preApprovedId = parseInt(formData.get("preApprovedId") as string, 10);

      if (isNaN(preApprovedId) || preApprovedId <= 0) {
        return json({ 
          errors: { _form: "Valid pre-approved ID is required" },
          success: false,
          action: "removePreApproved"
        });
      }

      try {
        await removePreApprovedPilot(preApprovedId);
        return json({ 
          success: true, 
          message: "Pilot removed from pre-approved list",
          action: "removePreApproved"
        });
      } catch (error) {
        return json({ 
          errors: { _form: "Failed to remove pilot from pre-approved list" },
          success: false,
          action: "removePreApproved"
        });
      }
    }

    case "quickMatch": {
      try {
        const bookingIds = await quickMatchJob(jobId, user.id);

        if (bookingIds.length === 0) {
          return json({ 
            success: true, 
            message: "No matching pilots found. Try adjusting your quick match settings.",
            action: "quickMatch"
          });
        }

        return json({ 
          success: true, 
          message: `Successfully matched with ${bookingIds.length} pilots`,
          action: "quickMatch"
        });
      } catch (error) {
        return json({ 
          errors: { _form: "Failed to perform quick match" },
          success: false,
          action: "quickMatch"
        });
      }
    }

    case "updateBookingStatus": {
      const bookingId = parseInt(formData.get("bookingId") as string, 10);
      const status = formData.get("status") as "accepted" | "rejected" | "cancelled";

      if (isNaN(bookingId) || bookingId <= 0) {
        return json({ 
          errors: { _form: "Valid booking ID is required" },
          success: false,
          action: "updateBookingStatus"
        });
      }

      if (!["accepted", "rejected", "cancelled"].includes(status)) {
        return json({ 
          errors: { _form: "Valid status is required" },
          success: false,
          action: "updateBookingStatus"
        });
      }

      try {
        await updateInstantBookingStatus(bookingId, status);
        return json({ 
          success: true, 
          message: `Booking ${status} successfully`,
          action: "updateBookingStatus"
        });
      } catch (error) {
        return json({ 
          errors: { _form: "Failed to update booking status" },
          success: false,
          action: "updateBookingStatus"
        });
      }
    }

    case "findMatches": {
      try {
        const matches = await findMatchingPilots(jobId, user.id);

        if (matches.length === 0) {
          return json({
            success: true,
            message: "No matching pilots found. Try adjusting your quick match settings.",
            action: "findMatches",
            matches: []
          });
        }

        // Get pilot details for matches
        const matchesWithDetails = await Promise.all(
          matches.map(async (match) => {
            const pilotUser = await getUserById(match.pilotId);
            return {
              ...match,
              pilot: pilotUser
            };
          })
        );

        return json({
          success: true,
          message: `Found ${matches.length} matching pilots`,
          action: "findMatches",
          matches: matchesWithDetails
        });
      } catch (error) {
        return json({
          errors: { _form: "Failed to find matching pilots" },
          success: false,
          action: "findMatches",
          matches: []
        });
      }
    }

    case "searchPilots": {
      const searchQuery = formData.get("searchQuery") as string;

      if (!searchQuery || searchQuery.trim().length < 2) {
        return json({
          success: true,
          action: "searchPilots",
          searchResults: []
        });
      }

      try {
        const results = await searchPilots(searchQuery);

        // Get pre-approved pilots to filter them out
        const preApprovedPilots = await getPreApprovedPilots(user.id);

        // Filter out pilots that are already pre-approved
        const filteredResults = results.filter(
          pilot => !preApprovedPilots.some(p => p.pilotId === pilot.id)
        );

        return json({
          success: true,
          action: "searchPilots",
          searchResults: filteredResults
        });
      } catch (error) {
        return json({
          errors: { _form: "Failed to search pilots" },
          success: false,
          action: "searchPilots",
          searchResults: []
        });
      }
    }

    default:
      return json({ 
        errors: { _form: "Invalid form submission" },
        success: false
      });
  }
}

export default function InstantBookingPage() {
  const { 
    user, 
    job, 
    quickMatchSettings, 
    preApprovedPilots, 
    instantBookings 
  } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const params = useParams();
  const isSubmitting = navigation.state === "submitting";

  const [showSettingsForm, setShowSettingsForm] = useState(false);
  const [showAddPreApprovedForm, setShowAddPreApprovedForm] = useState(false);
  const [showMatches, setShowMatches] = useState(false);

  // Pilot search state
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPilot, setSelectedPilot] = useState<any | null>(null);

  // Get search results from action data
  const searchResults = actionData?.action === "searchPilots" ? (actionData.searchResults || []) : [];

  // Format date for display
  const formatDate = (date: string | Date) => {
    return new Date(date).toLocaleDateString();
  };

  // Format time for display
  const formatTime = (date: string | Date) => {
    return new Date(date).toLocaleTimeString();
  };

  // Reset forms on successful submission
  if (actionData?.success && !isSubmitting) {
    if (actionData.action === "saveSettings") {
      setShowSettingsForm(false);
    } else if (actionData.action === "addPreApproved") {
      setShowAddPreApprovedForm(false);
      setSelectedPilot(null);
      setSearchQuery("");
    } else if (actionData.action === "findMatches") {
      setShowMatches(true);
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Instant Booking</h1>
        <Link 
          to={`/jobs/${params.jobId}`}
          className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded"
        >
          Back to Job
        </Link>
      </div>

      {/* Success Message */}
      {actionData?.success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <p>{actionData.message}</p>
        </div>
      )}

      {/* Error Message */}
      {actionData?.errors?._form && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{actionData.errors._form}</p>
        </div>
      )}

      {/* Job Summary */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-semibold mb-4">Job Summary</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <p><span className="font-medium">Title:</span> {job.title}</p>
            <p><span className="font-medium">Aircraft:</span> {job.aircraftType}</p>
            <p><span className="font-medium">Origin:</span> {job.origin}</p>
            <p><span className="font-medium">Destination:</span> {job.destination}</p>
          </div>
          <div>
            <p><span className="font-medium">Start Date:</span> {formatDate(job.startDate)}</p>
            <p><span className="font-medium">End Date:</span> {formatDate(job.endDate)}</p>
            <p><span className="font-medium">Budget:</span> ${job.budget?.toLocaleString()}</p>
            <p><span className="font-medium">Status:</span> {job.status}</p>
          </div>
        </div>
      </div>

      {/* Quick Match Actions */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-xl font-semibold mb-4">Quick Match</h2>
        <div className="flex flex-wrap gap-4 mb-4">
          <button
            type="button"
            onClick={() => setShowSettingsForm(!showSettingsForm)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded"
          >
            {showSettingsForm ? "Hide Settings" : "Configure Settings"}
          </button>

          <Form method="post">
            <input type="hidden" name="intent" value="findMatches" />
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-indigo-500 hover:bg-indigo-600 text-white px-4 py-2 rounded disabled:opacity-50"
            >
              {isSubmitting && actionData?.action === "findMatches" ? "Finding..." : "Find Matching Pilots"}
            </button>
          </Form>

          <Form method="post">
            <input type="hidden" name="intent" value="quickMatch" />
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded disabled:opacity-50"
              onClick={(e) => {
                if (!confirm("This will automatically send booking requests to matching pilots. Continue?")) {
                  e.preventDefault();
                }
              }}
            >
              {isSubmitting && actionData?.action === "quickMatch" ? "Processing..." : "Quick Match Now"}
            </button>
          </Form>
        </div>

        {/* Quick Match Settings Form */}
        {showSettingsForm && (
          <div className="border-t pt-4 mt-4">
            <h3 className="text-lg font-medium mb-3">Quick Match Settings</h3>
            <Form method="post" className="space-y-4">
              <input type="hidden" name="intent" value="saveSettings" />

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Preferred Minimum Rating */}
                <div>
                  <label htmlFor="preferredRatingMin" className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Pilot Rating (1-5)
                  </label>
                  <input
                    type="number"
                    id="preferredRatingMin"
                    name="preferredRatingMin"
                    min="1"
                    max="5"
                    step="0.5"
                    defaultValue={quickMatchSettings?.preferredRatingMin || ""}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Preferred Minimum Experience Hours */}
                <div>
                  <label htmlFor="preferredExperienceHoursMin" className="block text-sm font-medium text-gray-700 mb-1">
                    Minimum Experience Hours
                  </label>
                  <input
                    type="number"
                    id="preferredExperienceHoursMin"
                    name="preferredExperienceHoursMin"
                    min="0"
                    step="100"
                    defaultValue={quickMatchSettings?.preferredExperienceHoursMin || ""}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Preferred Aircraft Types */}
                <div>
                  <label htmlFor="preferredAircraftTypes" className="block text-sm font-medium text-gray-700 mb-1">
                    Preferred Aircraft Types (comma-separated)
                  </label>
                  <input
                    type="text"
                    id="preferredAircraftTypes"
                    name="preferredAircraftTypes"
                    defaultValue={quickMatchSettings?.preferredAircraftTypes || ""}
                    placeholder="e.g., Cessna 172, Cirrus SR22"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>

                {/* Maximum Price Per Hour */}
                <div>
                  <label htmlFor="maxPricePerHour" className="block text-sm font-medium text-gray-700 mb-1">
                    Maximum Price Per Hour ($)
                  </label>
                  <input
                    type="number"
                    id="maxPricePerHour"
                    name="maxPricePerHour"
                    min="0"
                    step="10"
                    defaultValue={quickMatchSettings?.maxPricePerHour || ""}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              <div className="flex flex-col space-y-2">
                {/* Prefer Pre-Approved Only */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="preferPreApprovedOnly"
                    name="preferPreApprovedOnly"
                    value="true"
                    defaultChecked={quickMatchSettings?.preferPreApprovedOnly}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="preferPreApprovedOnly" className="ml-2 block text-sm text-gray-900">
                    Only match with pre-approved pilots
                  </label>
                </div>

                {/* Auto-Approve Matches */}
                <div className="flex items-center">
                  <input
                    type="checkbox"
                    id="autoApproveMatches"
                    name="autoApproveMatches"
                    value="true"
                    defaultChecked={quickMatchSettings?.autoApproveMatches}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <label htmlFor="autoApproveMatches" className="ml-2 block text-sm text-gray-900">
                    Automatically approve matches (when pilot also has auto-accept enabled)
                  </label>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  disabled={isSubmitting && actionData?.action === "saveSettings"}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
                >
                  {isSubmitting && actionData?.action === "saveSettings" ? "Saving..." : "Save Settings"}
                </button>
              </div>
            </Form>
          </div>
        )}

        {/* Matching Pilots Results */}
        {showMatches && actionData?.action === "findMatches" && actionData.matches && (
          <div className="border-t pt-4 mt-4">
            <h3 className="text-lg font-medium mb-3">Matching Pilots</h3>

            {actionData.matches.length === 0 ? (
              <p className="text-gray-500">No matching pilots found. Try adjusting your settings.</p>
            ) : (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Pilot
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Match Score
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Reasons
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {actionData.matches.map((match: any) => (
                      <tr key={match.pilotId}>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div>
                              <div className="text-sm font-medium text-gray-900">
                                {match.pilot?.name || `Pilot #${match.pilotId}`}
                              </div>
                              <div className="text-sm text-gray-500">
                                Rating: {match.pilot?.rating || "N/A"}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-gray-900">
                            {(match.matchScore * 100).toFixed(0)}%
                          </div>
                          <div className="w-24 bg-gray-200 rounded-full h-2.5">
                            <div 
                              className="bg-blue-600 h-2.5 rounded-full" 
                              style={{ width: `${match.matchScore * 100}%` }}
                            ></div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900">
                            {match.matchReason}
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <Form method="post" className="inline">
                            <input type="hidden" name="intent" value="addPreApproved" />
                            <input type="hidden" name="pilotId" value={match.pilotId} />
                            <button
                              type="submit"
                              className="text-indigo-600 hover:text-indigo-900 mr-3"
                            >
                              Add to Pre-approved
                            </button>
                          </Form>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Pre-Approved Pilots */}
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-semibold">Pre-Approved Pilots</h2>
          <button
            type="button"
            onClick={() => setShowAddPreApprovedForm(!showAddPreApprovedForm)}
            className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded text-sm"
          >
            {showAddPreApprovedForm ? "Cancel" : "Add Pilot"}
          </button>
        </div>

        {/* Add Pre-Approved Pilot Form */}
        {showAddPreApprovedForm && (
          <div className="border-t border-b py-4 mb-4">
            <h3 className="text-lg font-medium mb-3">Add Pre-Approved Pilot</h3>

            {/* Pilot Search */}
            <div className="mb-4">
              <label htmlFor="pilotSearch" className="block text-sm font-medium text-gray-700 mb-1">
                Search for Pilots
              </label>
              <Form method="post" className="flex gap-2">
                <input type="hidden" name="intent" value="searchPilots" />
                <div className="flex-1">
                  <input
                    type="text"
                    id="pilotSearch"
                    name="searchQuery"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search by name, email, or certifications..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <button
                  type="submit"
                  disabled={isSubmitting || searchQuery.trim().length < 2}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
                >
                  {isSubmitting && actionData?.action === "searchPilots" ? "Searching..." : "Search"}
                </button>
              </Form>
              <p className="mt-1 text-xs text-gray-500">
                Enter at least 2 characters to search
              </p>
            </div>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <div className="mb-4 max-h-60 overflow-y-auto border border-gray-200 rounded-md">
                <ul className="divide-y divide-gray-200">
                  {searchResults.map((pilot) => (
                    <li 
                      key={pilot.id}
                      className={`p-3 hover:bg-gray-50 cursor-pointer ${selectedPilot?.id === pilot.id ? 'bg-blue-50' : ''}`}
                      onClick={() => setSelectedPilot(pilot)}
                    >
                      <div className="flex justify-between">
                        <div>
                          <p className="font-medium text-gray-900">{pilot.name}</p>
                          <p className="text-sm text-gray-500">{pilot.email}</p>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-gray-700">
                            Rating: {pilot.rating ? `${pilot.rating}/5` : 'N/A'}
                          </p>
                          <p className="text-sm text-gray-700">
                            Experience: {pilot.experienceHours ? `${pilot.experienceHours} hrs` : 'N/A'}
                          </p>
                        </div>
                      </div>
                      {pilot.certifications && (
                        <p className="text-xs text-gray-500 mt-1">
                          Certifications: {pilot.certifications}
                        </p>
                      )}
                    </li>
                  ))}
                </ul>
              </div>
            )}

            {actionData?.action === "searchPilots" && searchResults.length === 0 && searchQuery.length >= 2 && (
              <div className="mb-4 p-3 border border-gray-200 rounded-md bg-gray-50 text-gray-500">
                No pilots found matching your search criteria
              </div>
            )}

            {/* Selected Pilot */}
            {selectedPilot && (
              <div className="mb-4 p-4 border border-green-200 rounded-md bg-green-50">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="font-medium text-gray-900">{selectedPilot.name}</p>
                    <p className="text-sm text-gray-600">{selectedPilot.email}</p>
                    {selectedPilot.certifications && (
                      <p className="text-xs text-gray-500 mt-1">
                        Certifications: {selectedPilot.certifications}
                      </p>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={() => setSelectedPilot(null)}
                    className="text-sm text-red-600 hover:text-red-800"
                  >
                    Clear
                  </button>
                </div>
              </div>
            )}

            {/* Add Pilot Form */}
            <Form method="post" className="space-y-4">
              <input type="hidden" name="intent" value="addPreApproved" />
              <input 
                type="hidden" 
                name="pilotId" 
                value={selectedPilot?.id || ""} 
              />

              <div>
                <label htmlFor="notes" className="block text-sm font-medium text-gray-700 mb-1">
                  Notes (optional)
                </label>
                <textarea
                  id="notes"
                  name="notes"
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Add any notes about this pilot..."
                ></textarea>
              </div>

              <div className="flex justify-between">
                <button
                  type="button"
                  onClick={() => {
                    setShowAddPreApprovedForm(false);
                    setSelectedPilot(null);
                    setSearchQuery("");
                  }}
                  className="bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                >
                  Cancel
                </button>

                <button
                  type="submit"
                  disabled={isSubmitting || !selectedPilot}
                  className="bg-blue-600 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline disabled:opacity-50"
                >
                  {isSubmitting && actionData?.action === "addPreApproved" ? "Adding..." : "Add to Pre-Approved List"}
                </button>
              </div>
            </Form>
          </div>
        )}

        {/* Pre-Approved Pilots List */}
        {preApprovedPilots.length === 0 ? (
          <p className="text-gray-500">No pre-approved pilots yet. Add pilots to your pre-approved list for faster matching.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pilot
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Approval Date
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notes
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {preApprovedPilots.map((preApproved: PreApprovedPilot) => (
                  <tr key={preApproved.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {preApproved.pilot?.name || `Pilot #${preApproved.pilotId}`}
                          </div>
                          <div className="text-sm text-gray-500">
                            Rating: {preApproved.pilot?.rating || "N/A"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(preApproved.approvalDate)}
                      </div>
                    </td>
                    <td className="px-6 py-4">
                      <div className="text-sm text-gray-900">
                        {preApproved.notes || "No notes"}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Form method="post" className="inline">
                        <input type="hidden" name="intent" value="removePreApproved" />
                        <input type="hidden" name="preApprovedId" value={preApproved.id} />
                        <button
                          type="submit"
                          className="text-red-600 hover:text-red-900"
                          onClick={(e) => {
                            if (!confirm("Are you sure you want to remove this pilot from your pre-approved list?")) {
                              e.preventDefault();
                            }
                          }}
                        >
                          Remove
                        </button>
                      </Form>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Instant Bookings */}
      <div className="bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Instant Bookings</h2>

        {instantBookings.length === 0 ? (
          <p className="text-gray-500">No instant bookings for this job yet. Use Quick Match to find and book pilots instantly.</p>
        ) : (
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Pilot
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Match Score
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Response Deadline
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {instantBookings.map((booking: InstantBooking) => (
                  <tr key={booking.id}>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        <div>
                          <div className="text-sm font-medium text-gray-900">
                            {booking.pilot?.name || `Pilot #${booking.pilotId}`}
                          </div>
                          <div className="text-sm text-gray-500">
                            Rating: {booking.pilot?.rating || "N/A"}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {booking.matchScore ? `${(booking.matchScore * 100).toFixed(0)}%` : "N/A"}
                      </div>
                      {booking.matchScore && (
                        <div className="w-24 bg-gray-200 rounded-full h-2.5">
                          <div 
                            className="bg-blue-600 h-2.5 rounded-full" 
                            style={{ width: `${booking.matchScore * 100}%` }}
                          ></div>
                        </div>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                        ${booking.status === 'accepted' ? 'bg-green-100 text-green-800' : 
                          booking.status === 'rejected' ? 'bg-red-100 text-red-800' : 
                          booking.status === 'cancelled' ? 'bg-gray-100 text-gray-800' : 
                          booking.status === 'completed' ? 'bg-blue-100 text-blue-800' : 
                          'bg-yellow-100 text-yellow-800'}`}
                      >
                        {booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="text-sm text-gray-900">
                        {formatDate(booking.createdAt)}
                      </div>
                      <div className="text-sm text-gray-500">
                        {formatTime(booking.createdAt)}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      {booking.responseDeadline ? (
                        <div>
                          <div className="text-sm text-gray-900">
                            {formatDate(booking.responseDeadline)}
                          </div>
                          <div className="text-sm text-gray-500">
                            {formatTime(booking.responseDeadline)}
                          </div>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-500">None</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      {booking.status === 'pending' && (
                        <div className="space-y-2">
                          <Form method="post" className="inline-block mr-2">
                            <input type="hidden" name="intent" value="updateBookingStatus" />
                            <input type="hidden" name="bookingId" value={booking.id} />
                            <input type="hidden" name="status" value="cancelled" />
                            <button
                              type="submit"
                              className="text-red-600 hover:text-red-900"
                              onClick={(e) => {
                                if (!confirm("Are you sure you want to cancel this booking request?")) {
                                  e.preventDefault();
                                }
                              }}
                            >
                              Cancel Request
                            </button>
                          </Form>
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
}
