import { json, redirect } from "@remix-run/node";
import { Form, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import { useState } from "react";
import { requireUser } from "~/utils/session.server";
import { generateShortUrl, getActiveShortUrlsForUser, getFullUrl, ShortUrlData } from "~/utils/shortUrl.server";
import { IdDocumentType } from "~/types/verification";
import { QRCodeSVG } from "qrcode.react";

export const loader = async ({ request }) => {
  const user = await requireUser(request);

  // Only pilots can access this page
  if (user.role !== "pilot") {
    return redirect("/dashboard");
  }

  const userId = parseInt(user.id);

  // Get active short URLs for the user
  const activeUrls = await getActiveShortUrlsForUser(userId);

  // Add full URLs to each active URL
  const activeUrlsWithFullUrls = activeUrls.map(url => ({
    ...url,
    fullUrl: getFullUrl(url.shortCode)
  }));

  return json({
    user,
    activeUrls: activeUrlsWithFullUrls
  });
};

export const action = async ({ request }) => {
  const user = await requireUser(request);

  // Only pilots can generate short URLs
  if (user.role !== "pilot") {
    return redirect("/dashboard");
  }

  const userId = parseInt(user.id);
  const formData = await request.formData();
  const action = formData.get("action");

  if (action === "generate-url") {
    const documentType = formData.get("documentType") as IdDocumentType;
    const expirationMinutes = parseInt(formData.get("expirationMinutes")?.toString() || "30");

    if (!documentType) {
      return json({ error: "Document type is required" }, { status: 400 });
    }

    try {
      const shortUrlData = await generateShortUrl(userId, documentType, expirationMinutes);
      const fullUrl = getFullUrl(shortUrlData.shortCode);

      return json({ 
        success: true, 
        shortUrlData,
        fullUrl
      });
    } catch (error) {
      console.error("Error generating short URL:", error);
      return json({ error: "Failed to generate short URL" }, { status: 500 });
    }
  }

  return json({ error: "Invalid action" }, { status: 400 });
};

export default function MobileCaptureSettings() {
  const { user, activeUrls } = useLoaderData();
  const actionData = useActionData();
  const navigation = useNavigation();
  const isSubmitting = navigation.state === "submitting";

  const [selectedDocumentType, setSelectedDocumentType] = useState<IdDocumentType>(IdDocumentType.DRIVERS_LICENSE);
  const [expirationMinutes, setExpirationMinutes] = useState<number>(30);

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Mobile Document Capture</h1>
      
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">How It Works</h2>
        <p className="text-blue-700 mb-2">
          Generate a short URL or QR code that you can use to capture document photos from your mobile device.
          The captured images will be automatically uploaded and processed for verification.
        </p>
        <ol className="list-decimal list-inside text-blue-700 ml-2">
          <li>Generate a URL for the document you want to capture</li>
          <li>Open the URL on your mobile device or scan the QR code</li>
          <li>Take a photo of your document</li>
          <li>The image will be automatically uploaded and processed</li>
        </ol>
      </div>

      {actionData?.error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <p>{actionData.error}</p>
        </div>
      )}

      {actionData?.success && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-6">
          <h3 className="font-bold mb-2">URL Generated Successfully!</h3>
          <p className="mb-2">Share this link or scan the QR code to capture your document:</p>
          
          <div className="flex flex-col md:flex-row items-center gap-6 mt-4">
            <div className="bg-white p-4 rounded-lg shadow-md">
              <QRCodeSVG value={actionData.fullUrl} size={200} />
            </div>
            
            <div className="flex-1">
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Capture URL:
                </label>
                <div className="flex">
                  <input
                    type="text"
                    readOnly
                    value={actionData.fullUrl}
                    className="flex-1 px-3 py-2 border border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      navigator.clipboard.writeText(actionData.fullUrl);
                      alert("URL copied to clipboard!");
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-r-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
                  >
                    Copy
                  </button>
                </div>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Document Type:</span> {actionData.shortUrlData.documentType}
                </p>
                <p className="text-sm text-gray-600">
                  <span className="font-medium">Expires:</span> {new Date(actionData.shortUrlData.expiresAt).toLocaleString()}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="bg-white shadow-md rounded-lg p-6 mb-6">
        <h2 className="text-xl font-semibold mb-4">Generate Capture URL</h2>
        
        <Form method="post" className="space-y-4">
          <input type="hidden" name="action" value="generate-url" />
          
          <div>
            <label htmlFor="documentType" className="block text-gray-700 font-semibold mb-2">
              Document Type
            </label>
            <select
              id="documentType"
              name="documentType"
              value={selectedDocumentType}
              onChange={(e) => setSelectedDocumentType(e.target.value as IdDocumentType)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value={IdDocumentType.DRIVERS_LICENSE}>Driver's License</option>
              <option value={IdDocumentType.PASSPORT}>Passport</option>
              <option value={IdDocumentType.GOVERNMENT_ID}>Government ID</option>
              <option value={IdDocumentType.PILOT_LICENSE}>Pilot License</option>
            </select>
          </div>
          
          <div>
            <label htmlFor="expirationMinutes" className="block text-gray-700 font-semibold mb-2">
              URL Expiration
            </label>
            <select
              id="expirationMinutes"
              name="expirationMinutes"
              value={expirationMinutes}
              onChange={(e) => setExpirationMinutes(parseInt(e.target.value))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="15">15 minutes</option>
              <option value="30">30 minutes</option>
              <option value="60">1 hour</option>
              <option value="120">2 hours</option>
              <option value="1440">24 hours</option>
            </select>
          </div>
          
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50"
          >
            {isSubmitting ? "Generating..." : "Generate Capture URL"}
          </button>
        </Form>
      </div>

      {activeUrls && activeUrls.length > 0 && (
        <div className="bg-white shadow-md rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-4">Active Capture URLs</h2>
          
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Document Type
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Created
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Expires
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    URL
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {activeUrls.map((url: ShortUrlData) => (
                  <tr key={url.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {url.documentType}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(url.createdAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(url.expiresAt).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {url.usedAt ? (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                          Used
                        </span>
                      ) : (
                        <span className="px-2 py-1 text-xs font-semibold rounded-full bg-yellow-100 text-yellow-800">
                          Active
                        </span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <a
                        href={url.fullUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-blue-600 hover:text-blue-800"
                      >
                        {url.fullUrl}
                      </a>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}