import type { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "Contact FerryPros - Get in Touch" },
    { name: "description", content: "Contact the FerryPros team with your questions, feedback, or support requests. We're here to help with all your aircraft ferry needs." },
  ];
};

export default function Contact() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-primary-900 text-white overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-900 to-primary-800"></div>
          <div className="absolute inset-0" style={{ 
            backgroundImage: "url('https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2074&q=80')",
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            mixBlendMode: 'overlay'
          }}></div>
        </div>
        <div className="container-custom relative z-10 py-20 md:py-32">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6 leading-tight">
              Contact Us
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-10 leading-relaxed">
              Have questions or feedback? We're here to help.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-900 to-transparent"></div>
      </section>

      {/* Contact Form Section */}
      <section className="py-20 bg-gray-900">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 gap-12 items-start">
            <div>
              <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">Get in Touch</h2>
              <div className="w-24 h-1 bg-primary-500 mb-6"></div>
              <p className="text-xl text-gray-300 mb-10 leading-relaxed">
                Whether you have questions about our services, need support with your account, or want to provide feedback, our team is ready to assist you.
              </p>

              <div className="space-y-8">
                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="h-6 w-6 text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-white">Email</h3>
                    <p className="mt-1 text-gray-300"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="h-6 w-6 text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-white">Phone</h3>
                    <p className="mt-1 text-gray-300">(*************</p>
                  </div>
                </div>

                <div className="flex items-start">
                  <div className="flex-shrink-0 mt-1">
                    <svg className="h-6 w-6 text-primary-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-semibold text-white">Office</h3>
                    <p className="mt-1 text-gray-300">
                      123 Aviation Way<br />
                      Suite 456<br />
                      Phoenix, AZ 85001
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="card p-8 bg-gray-800 rounded-lg shadow-xl">
              <h3 className="text-2xl font-display font-semibold mb-6">Send us a Message</h3>
              <form className="space-y-6">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-1">
                    Your Name
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-primary-500 focus:border-primary-500"
                    placeholder="John Doe"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-primary-500 focus:border-primary-500"
                    placeholder="<EMAIL>"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="subject" className="block text-sm font-medium text-gray-300 mb-1">
                    Subject
                  </label>
                  <input
                    type="text"
                    id="subject"
                    name="subject"
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-primary-500 focus:border-primary-500"
                    placeholder="How can we help you?"
                    required
                  />
                </div>

                <div>
                  <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-1">
                    Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    rows={5}
                    className="w-full px-4 py-2 bg-gray-700 border border-gray-600 rounded-md text-white focus:ring-primary-500 focus:border-primary-500"
                    placeholder="Your message here..."
                    required
                  ></textarea>
                </div>

                <div>
                  <button
                    type="submit"
                    className="w-full bg-primary-600 hover:bg-primary-700 text-white font-medium py-3 px-4 rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
                  >
                    Send Message
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      </section>

      {/* FAQ Section */}
      <section className="py-20 bg-gray-800">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-4">Frequently Asked Questions</h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Find quick answers to common questions about our services.
            </p>
          </div>

          <div className="max-w-4xl mx-auto space-y-6">
            <div className="card p-6 bg-gray-700 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">How does the escrow payment system work?</h3>
              <p className="text-gray-300">
                Our escrow system holds payment securely until the ferry job is completed successfully. Funds are only released to the pilot after the client (aircraft owner, broker, or Ferry Pilot Agency) confirms safe delivery, providing protection for all parties involved.
              </p>
            </div>

            <div className="card p-6 bg-gray-700 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">What verification do pilots undergo?</h3>
              <p className="text-gray-300">
                All pilots on our platform undergo thorough verification of their licenses, ratings, flight hours, and background checks. We also collect reviews from previous clients to ensure quality service.
              </p>
            </div>

            <div className="card p-6 bg-gray-700 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">How can I track my aircraft during ferry?</h3>
              <p className="text-gray-300">
                Our platform provides real-time tracking of your aircraft during the ferry process. You'll receive updates on location, estimated arrival times, and any important notifications throughout the journey.
              </p>
            </div>

            <div className="card p-6 bg-gray-700 rounded-lg">
              <h3 className="text-xl font-semibold mb-2">What if there's a dispute during the ferry process?</h3>
              <p className="text-gray-300">
                We have a comprehensive dispute resolution process. Our team will review all evidence, communication, and tracking data to help resolve any issues fairly between all parties involved, including aircraft owners, brokers, Ferry Pilot Agencies, and pilots.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-primary-800 to-primary-900 text-white text-center">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">Ready to Get Started?</h2>
            <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto mb-10">
              Join the FerryPros platform today as an aircraft owner, broker, Ferry Pilot Agency, or pilot and experience a safer, more efficient way to connect and manage ferry jobs.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="/register" className="btn-primary bg-gray-800 text-primary-300 hover:bg-gray-700 hover:text-primary-200 text-lg px-8 py-3 border border-primary-700">
                Create Your Account
              </a>
              <a href="/about" className="btn-secondary border-gray-700 text-gray-200 hover:bg-primary-900 hover:text-primary-300 text-lg px-8 py-3">
                Learn More
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
