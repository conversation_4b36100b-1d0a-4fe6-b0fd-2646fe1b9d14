import type { MetaFunction } from "@remix-run/node";

export const meta: MetaFunction = () => {
  return [
    { title: "About FerryPros - Our Mission and Team" },
    { name: "description", content: "Learn about FerryPros, our mission to connect aircraft owners, brokers, and Ferry Pilot Agencies with trusted pilots, and the team behind our platform." },
  ];
};

export default function About() {
  return (
    <div>
      {/* Hero Section */}
      <section className="relative bg-primary-900 text-white overflow-hidden">
        <div className="absolute inset-0 z-0 opacity-20">
          <div className="absolute inset-0 bg-gradient-to-r from-primary-900 to-primary-800"></div>
          <div className="absolute inset-0" style={{ 
            backgroundImage: "url('https://images.unsplash.com/photo-1559628233-100c798642d4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80')",
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            mixBlendMode: 'overlay'
          }}></div>
        </div>
        <div className="container-custom relative z-10 py-20 md:py-32">
          <div className="max-w-3xl mx-auto text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display font-bold mb-6 leading-tight">
              About FerryPros
            </h1>
            <p className="text-xl md:text-2xl text-primary-100 mb-10 leading-relaxed">
              Connecting aircraft owners, brokers, and Ferry Pilot Agencies with trusted pilots through innovation, security, and transparency.
            </p>
          </div>
        </div>
        <div className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-gray-900 to-transparent"></div>
      </section>

      {/* Our Mission Section */}
      <section className="py-20 bg-gray-900">
        <div className="container-custom">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">Our Mission</h2>
              <div className="w-24 h-1 bg-primary-500 mb-6"></div>
              <p className="text-xl text-gray-300 mb-6 leading-relaxed">
                At FerryPros, our mission is to revolutionize the aircraft ferry industry by creating a secure, transparent marketplace that connects aircraft owners, brokers, and Ferry Pilot Agencies with qualified, verified pilots.
              </p>
              <p className="text-xl text-gray-300 mb-6 leading-relaxed">
                We believe in building trust through technology, providing real-time tracking, secure payment systems, and comprehensive verification processes that protect all parties involved.
              </p>
              <p className="text-xl text-gray-300 leading-relaxed">
                Our goal is to make aircraft ferrying safer, more efficient, and more accessible for everyone in the aviation community.
              </p>
            </div>
            <div className="rounded-lg overflow-hidden shadow-2xl">
              <img 
                src="https://images.unsplash.com/photo-1540962351504-03099e0a754b?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=2070&q=80" 
                alt="Aircraft in flight" 
                className="w-full h-auto"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-20 bg-gray-800">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-4">Our Story</h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
          </div>

          <div className="max-w-4xl mx-auto">
            <p className="text-xl text-gray-300 mb-6 leading-relaxed">
              FerryPros was founded in 2022 by a team of aviation professionals who recognized a critical gap in the aircraft ferry market. After experiencing the challenges of finding reliable pilots and ensuring secure transactions firsthand, our founders set out to create a solution.
            </p>
            <p className="text-xl text-gray-300 mb-6 leading-relaxed">
              What began as a simple idea has grown into a comprehensive platform that addresses the key pain points in the industry: verification, security, and transparency. By leveraging modern technology and industry expertise, we've built a service that benefits aircraft owners, brokers, Ferry Pilot Agencies, and pilots.
            </p>
            <p className="text-xl text-gray-300 leading-relaxed">
              Today, FerryPros continues to innovate and expand, with a growing community of verified pilots, satisfied aircraft owners, brokers, and Ferry Pilot Agencies. Our commitment to safety, security, and exceptional service remains at the core of everything we do.
            </p>
          </div>
        </div>
      </section>

      {/* Our Team Section */}
      <section className="py-20 bg-gray-900">
        <div className="container-custom">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-4">Our Leadership Team</h2>
            <div className="w-24 h-1 bg-primary-500 mx-auto mb-6"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Meet the experienced professionals behind FerryPros who are passionate about aviation and technology.
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-10">
            <div className="card hover:shadow-lg transition-shadow duration-300 text-center">
              <div className="mb-6 mx-auto w-40 h-40 rounded-full overflow-hidden">
                <img 
                  src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=774&q=80" 
                  alt="CEO" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-display font-semibold mb-2">Michael Johnson</h3>
              <p className="text-primary-400 mb-4">CEO & Co-Founder</p>
              <p className="text-gray-300">Former commercial pilot with over 15 years of experience in aviation management and technology startups.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300 text-center">
              <div className="mb-6 mx-auto w-40 h-40 rounded-full overflow-hidden">
                <img 
                  src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=776&q=80" 
                  alt="COO" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-display font-semibold mb-2">Sarah Martinez</h3>
              <p className="text-primary-400 mb-4">COO & Co-Founder</p>
              <p className="text-gray-300">Aviation industry veteran with expertise in operations, compliance, and building scalable business processes.</p>
            </div>

            <div className="card hover:shadow-lg transition-shadow duration-300 text-center">
              <div className="mb-6 mx-auto w-40 h-40 rounded-full overflow-hidden">
                <img 
                  src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=774&q=80" 
                  alt="CTO" 
                  className="w-full h-full object-cover"
                />
              </div>
              <h3 className="text-xl font-display font-semibold mb-2">David Chen</h3>
              <p className="text-primary-400 mb-4">CTO</p>
              <p className="text-gray-300">Technology leader with a background in secure payment systems and real-time tracking applications.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action Section */}
      <section className="py-20 bg-gradient-to-r from-primary-800 to-primary-900 text-white text-center">
        <div className="container-custom">
          <div className="max-w-4xl mx-auto">
            <h2 className="text-3xl md:text-4xl font-display font-bold mb-6">Join the FerryPros Community</h2>
            <p className="text-xl md:text-2xl text-primary-100 max-w-3xl mx-auto mb-10">
              Whether you're an aircraft owner, broker, Ferry Pilot Agency looking for trusted pilots, or a pilot seeking new opportunities, we're here to help.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-4">
              <a href="/register" className="btn-primary bg-gray-800 text-primary-300 hover:bg-gray-700 hover:text-primary-200 text-lg px-8 py-3 border border-primary-700">
                Create Your Account
              </a>
              <a href="/contact" className="btn-secondary border-gray-700 text-gray-200 hover:bg-primary-900 hover:text-primary-300 text-lg px-8 py-3">
                Contact Us
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
