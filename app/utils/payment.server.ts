import { pool } from './db.server';

// Define types for payment-related entities
export type EscrowAccount = {
  id: number;
  owner_id: number;
  job_id: number;
  balance: number;
  status: 'pending' | 'active' | 'released' | 'refunded' | 'disputed';
  created_at: Date;
  updated_at: Date | null;
};

export type Transaction = {
  id: number;
  escrow_account_id: number;
  amount: number;
  type: 'deposit' | 'withdrawal' | 'refund' | 'fee';
  status: 'pending' | 'completed' | 'failed';
  description: string;
  dispute_id?: number;
  dispute_resolution_type?: string;
  created_at: Date;
  updated_at: Date | null;
};

export type Dispute = {
  id: number;
  job_id: number;
  escrow_account_id: number;
  initiated_by_id: number;
  reason: string;
  description: string;
  status: 'open' | 'under_review' | 'resolved' | 'cancelled';
  admin_notes: string | null;
  resolution_details: string | null;
  created_at: Date;
  updated_at: Date | null;
  resolved_at: Date | null;
};

export type Expense = {
  id: number;
  job_id: number;
  pilot_id: number;
  amount: number;
  category: string;
  description: string;
  receipt_url: string | null;
  status: 'pending' | 'approved' | 'rejected';
  created_at: Date;
  updated_at: Date | null;
};

// Initialize payment-related tables
export async function initializePaymentTables() {
  // Create escrow_accounts table
  await pool.query(`
    CREATE TABLE IF NOT EXISTS escrow_accounts (
      id SERIAL PRIMARY KEY,
      owner_id INTEGER REFERENCES users(id) NOT NULL,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      balance DECIMAL(10, 2) NOT NULL DEFAULT 0,
      status TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP
    )
  `);

  // Create transactions table
  await pool.query(`
    CREATE TABLE IF NOT EXISTS transactions (
      id SERIAL PRIMARY KEY,
      escrow_account_id INTEGER REFERENCES escrow_accounts(id) NOT NULL,
      amount DECIMAL(10, 2) NOT NULL,
      type TEXT NOT NULL,
      status TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP
    )
  `);

  // Create expenses table
  await pool.query(`
    CREATE TABLE IF NOT EXISTS expenses (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      pilot_id INTEGER REFERENCES users(id) NOT NULL,
      amount DECIMAL(10, 2) NOT NULL,
      category TEXT NOT NULL,
      description TEXT,
      receipt_url TEXT,
      status TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP
    )
  `);

  console.log('Payment tables initialized');
}

// Create an escrow account for a job
export async function createEscrowAccount(ownerId: number, jobId: number): Promise<EscrowAccount> {
  const result = await pool.query(
    `INSERT INTO escrow_accounts (
      owner_id, 
      job_id, 
      balance, 
      status, 
      created_at
    ) VALUES ($1, $2, $3, $4, NOW()) RETURNING *`,
    [ownerId, jobId, 0, 'pending']
  );

  return result.rows[0];
}

// Get an escrow account by ID
export async function getEscrowAccount(escrowAccountId: number): Promise<EscrowAccount | null> {
  const result = await pool.query(
    'SELECT * FROM escrow_accounts WHERE id = $1',
    [escrowAccountId]
  );

  return result.rows[0] || null;
}

// Get escrow accounts for a job
export async function getEscrowAccountForJob(jobId: number): Promise<EscrowAccount | null> {
  const result = await pool.query(
    'SELECT * FROM escrow_accounts WHERE job_id = $1',
    [jobId]
  );

  return result.rows[0] || null;
}

// Get escrow accounts for an owner
export async function getEscrowAccountsForOwner(ownerId: number): Promise<EscrowAccount[]> {
  const result = await pool.query(
    'SELECT * FROM escrow_accounts WHERE owner_id = $1 ORDER BY created_at DESC',
    [ownerId]
  );

  return result.rows;
}

// Fund an escrow account
export async function fundEscrowAccount(
  escrowAccountId: number, 
  amount: number, 
  description: string = 'Escrow account funding'
): Promise<{ escrowAccount: EscrowAccount, transaction: Transaction }> {
  // Start a transaction
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Update escrow account balance
    const escrowResult = await client.query(
      `UPDATE escrow_accounts 
       SET balance = balance + $1, status = 'active', updated_at = NOW() 
       WHERE id = $2 
       RETURNING *`,
      [amount, escrowAccountId]
    );

    if (escrowResult.rows.length === 0) {
      throw new Error('Escrow account not found');
    }

    // Create a transaction record
    const transactionResult = await client.query(
      `INSERT INTO transactions (
        escrow_account_id, 
        amount, 
        type, 
        status, 
        description, 
        created_at
      ) VALUES ($1, $2, $3, $4, $5, NOW()) RETURNING *`,
      [escrowAccountId, amount, 'deposit', 'completed', description]
    );

    await client.query('COMMIT');

    return {
      escrowAccount: escrowResult.rows[0],
      transaction: transactionResult.rows[0]
    };
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Release funds from an escrow account to a pilot
export async function releaseEscrowFunds(
  escrowAccountId: number, 
  pilotId: number, 
  amount: number, 
  description: string = 'Payment to pilot'
): Promise<{ escrowAccount: EscrowAccount, transaction: Transaction }> {
  // Start a transaction
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Get the escrow account
    const escrowResult = await client.query(
      'SELECT * FROM escrow_accounts WHERE id = $1 FOR UPDATE',
      [escrowAccountId]
    );

    if (escrowResult.rows.length === 0) {
      throw new Error('Escrow account not found');
    }

    const escrowAccount = escrowResult.rows[0];

    // Check if there are sufficient funds
    if (escrowAccount.balance < amount) {
      throw new Error('Insufficient funds in escrow account');
    }

    // Update escrow account balance
    const updatedEscrowResult = await client.query(
      `UPDATE escrow_accounts 
       SET balance = balance - $1, updated_at = NOW() 
       WHERE id = $2 
       RETURNING *`,
      [amount, escrowAccountId]
    );

    // Create a transaction record
    const transactionResult = await client.query(
      `INSERT INTO transactions (
        escrow_account_id, 
        amount, 
        type, 
        status, 
        description, 
        created_at
      ) VALUES ($1, $2, $3, $4, $5, NOW()) RETURNING *`,
      [escrowAccountId, amount, 'withdrawal', 'completed', description]
    );

    // If balance is 0, mark escrow as released
    if (updatedEscrowResult.rows[0].balance === 0) {
      await client.query(
        `UPDATE escrow_accounts 
         SET status = 'released', updated_at = NOW() 
         WHERE id = $1`,
        [escrowAccountId]
      );
    }

    await client.query('COMMIT');

    return {
      escrowAccount: updatedEscrowResult.rows[0],
      transaction: transactionResult.rows[0]
    };
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Create an expense
export async function createExpense(
  jobId: number, 
  pilotId: number, 
  amount: number, 
  category: string, 
  description: string, 
  receiptUrl: string | null = null
): Promise<Expense> {
  const result = await pool.query(
    `INSERT INTO expenses (
      job_id, 
      pilot_id, 
      amount, 
      category, 
      description, 
      receipt_url, 
      status, 
      created_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW()) RETURNING *`,
    [jobId, pilotId, amount, category, description, receiptUrl, 'pending']
  );

  return result.rows[0];
}

// Get expenses for a job
export async function getExpensesForJob(jobId: number): Promise<Expense[]> {
  const result = await pool.query(
    'SELECT * FROM expenses WHERE job_id = $1 ORDER BY created_at DESC',
    [jobId]
  );

  return result.rows;
}

// Get expenses for a pilot
export async function getExpensesForPilot(pilotId: number): Promise<Expense[]> {
  const result = await pool.query(
    'SELECT * FROM expenses WHERE pilot_id = $1 ORDER BY created_at DESC',
    [pilotId]
  );

  return result.rows;
}

// Approve an expense
export async function approveExpense(expenseId: number): Promise<Expense> {
  const result = await pool.query(
    `UPDATE expenses 
     SET status = 'approved', updated_at = NOW() 
     WHERE id = $1 
     RETURNING *`,
    [expenseId]
  );

  if (result.rows.length === 0) {
    throw new Error('Expense not found');
  }

  return result.rows[0];
}

// Reject an expense
export async function rejectExpense(expenseId: number): Promise<Expense> {
  const result = await pool.query(
    `UPDATE expenses 
     SET status = 'rejected', updated_at = NOW() 
     WHERE id = $1 
     RETURNING *`,
    [expenseId]
  );

  if (result.rows.length === 0) {
    throw new Error('Expense not found');
  }

  return result.rows[0];
}

// Get transactions for an escrow account
export async function getTransactionsForEscrowAccount(escrowAccountId: number): Promise<Transaction[]> {
  const result = await pool.query(
    'SELECT * FROM transactions WHERE escrow_account_id = $1 ORDER BY created_at DESC',
    [escrowAccountId]
  );

  return result.rows;
}

// Create a dispute for a job
export async function createDispute(
  jobId: number,
  escrowAccountId: number,
  initiatedById: number,
  reason: string,
  description: string
): Promise<Dispute> {
  // Start a transaction
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Create the dispute record
    const disputeResult = await client.query(
      `INSERT INTO disputes (
        job_id,
        escrow_account_id,
        initiated_by_id,
        reason,
        description,
        status,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW()) RETURNING *`,
      [jobId, escrowAccountId, initiatedById, reason, description, 'open']
    );

    // Update the escrow account status to disputed
    await client.query(
      `UPDATE escrow_accounts 
       SET status = 'disputed', updated_at = NOW() 
       WHERE id = $1`,
      [escrowAccountId]
    );

    await client.query('COMMIT');

    return disputeResult.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Get a dispute by ID
export async function getDisputeById(disputeId: number): Promise<Dispute | null> {
  const result = await pool.query(
    'SELECT * FROM disputes WHERE id = $1',
    [disputeId]
  );

  return result.rows[0] || null;
}

// Get disputes for a job
export async function getDisputesForJob(jobId: number): Promise<Dispute[]> {
  const result = await pool.query(
    'SELECT * FROM disputes WHERE job_id = $1 ORDER BY created_at DESC',
    [jobId]
  );

  return result.rows;
}

// Get disputes initiated by a user
export async function getDisputesInitiatedByUser(userId: number): Promise<Dispute[]> {
  const result = await pool.query(
    'SELECT * FROM disputes WHERE initiated_by_id = $1 ORDER BY created_at DESC',
    [userId]
  );

  return result.rows;
}

// Get disputes by status
export async function getDisputesByStatus(status: string): Promise<Dispute[]> {
  const result = await pool.query(
    'SELECT * FROM disputes WHERE status = $1 ORDER BY created_at DESC',
    [status]
  );

  return result.rows;
}

// Update dispute status
export async function updateDisputeStatus(
  disputeId: number,
  status: 'open' | 'under_review' | 'resolved' | 'cancelled',
  adminNotes: string | null = null
): Promise<Dispute> {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const updateFields = ['status', 'updated_at'];
    const updateValues = [status, 'NOW()'];
    const params = [disputeId];
    let paramIndex = 2;

    if (adminNotes !== null) {
      updateFields.push('admin_notes');
      updateValues.push(`$${paramIndex}`);
      params.push(adminNotes);
      paramIndex++;
    }

    // If resolving or cancelling, set resolved_at
    if (status === 'resolved' || status === 'cancelled') {
      updateFields.push('resolved_at');
      updateValues.push('NOW()');
    }

    const updateQuery = `
      UPDATE disputes 
      SET ${updateFields.map((field, index) => `${field} = ${updateValues[index]}`).join(', ')} 
      WHERE id = $1 
      RETURNING *
    `;

    const result = await client.query(updateQuery, params);

    if (result.rows.length === 0) {
      throw new Error('Dispute not found');
    }

    await client.query('COMMIT');

    return result.rows[0];
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Resolve dispute with partial payment
export async function resolveDisputeWithPartialPayment(
  disputeId: number,
  escrowAccountId: number,
  pilotId: number,
  amount: number,
  resolutionDetails: string,
  adminNotes: string | null = null
): Promise<{ dispute: Dispute, transaction: Transaction, escrowAccount: EscrowAccount }> {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Get the dispute
    const disputeResult = await client.query(
      'SELECT * FROM disputes WHERE id = $1 FOR UPDATE',
      [disputeId]
    );

    if (disputeResult.rows.length === 0) {
      throw new Error('Dispute not found');
    }

    // Get the escrow account
    const escrowResult = await client.query(
      'SELECT * FROM escrow_accounts WHERE id = $1 FOR UPDATE',
      [escrowAccountId]
    );

    if (escrowResult.rows.length === 0) {
      throw new Error('Escrow account not found');
    }

    const escrowAccount = escrowResult.rows[0];

    // Check if there are sufficient funds
    if (escrowAccount.balance < amount) {
      throw new Error('Insufficient funds in escrow account');
    }

    // Update escrow account balance
    const updatedEscrowResult = await client.query(
      `UPDATE escrow_accounts 
       SET balance = balance - $1, status = 'released', updated_at = NOW() 
       WHERE id = $2 
       RETURNING *`,
      [amount, escrowAccountId]
    );

    // Create a transaction record for the partial payment
    const transactionResult = await client.query(
      `INSERT INTO transactions (
        escrow_account_id, 
        amount, 
        type, 
        status, 
        description, 
        dispute_id,
        dispute_resolution_type,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW()) RETURNING *`,
      [escrowAccountId, amount, 'withdrawal', 'completed', 'Dispute resolution payment', disputeId, 'partial_payment']
    );

    // Update the dispute status to resolved
    const updatedDisputeResult = await client.query(
      `UPDATE disputes 
       SET status = 'resolved', 
           resolution_details = $1, 
           admin_notes = $2, 
           updated_at = NOW(), 
           resolved_at = NOW() 
       WHERE id = $3 
       RETURNING *`,
      [resolutionDetails, adminNotes, disputeId]
    );

    await client.query('COMMIT');

    return {
      dispute: updatedDisputeResult.rows[0],
      transaction: transactionResult.rows[0],
      escrowAccount: updatedEscrowResult.rows[0]
    };
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

// Add additional funds to escrow during dispute
export async function addFundsToDisputedEscrow(
  disputeId: number,
  escrowAccountId: number,
  amount: number,
  description: string = 'Additional funds for dispute resolution'
): Promise<{ escrowAccount: EscrowAccount, transaction: Transaction }> {
  // Start a transaction
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    // Get the dispute
    const disputeResult = await client.query(
      'SELECT * FROM disputes WHERE id = $1 FOR UPDATE',
      [disputeId]
    );

    if (disputeResult.rows.length === 0) {
      throw new Error('Dispute not found');
    }

    // Update escrow account balance
    const escrowResult = await client.query(
      `UPDATE escrow_accounts 
       SET balance = balance + $1, updated_at = NOW() 
       WHERE id = $2 
       RETURNING *`,
      [amount, escrowAccountId]
    );

    if (escrowResult.rows.length === 0) {
      throw new Error('Escrow account not found');
    }

    // Create a transaction record
    const transactionResult = await client.query(
      `INSERT INTO transactions (
        escrow_account_id, 
        amount, 
        type, 
        status, 
        description, 
        dispute_id,
        dispute_resolution_type,
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW()) RETURNING *`,
      [escrowAccountId, amount, 'deposit', 'completed', description, disputeId, 'additional_funds']
    );

    await client.query('COMMIT');

    return {
      escrowAccount: escrowResult.rows[0],
      transaction: transactionResult.rows[0]
    };
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}
