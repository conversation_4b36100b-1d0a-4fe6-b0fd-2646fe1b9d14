import { pool, query } from './db.server';
import { TrackingStatus, FlightStatus } from './tracking.server';

// Define notification types

/**
 * Send a notification to a user
 * @param params Object containing notification parameters
 * @returns Result of the notification creation
 */
export async function sendNotification(params: {
  userId: number;
  message: string;
  type: string;
  metadata?: any;
}) {
  return createNotification({
    userId: params.userId,
    message: params.message,
    type: params.type as NotificationType,
    read: false,
    data: params.metadata
  });
}
export enum NotificationType {
  TRACKING_STARTED = 'tracking_started',
  TRACKING_UPDATED = 'tracking_updated',
  TRACKING_COMPLETED = 'tracking_completed',
  FLIGHT_DEPARTED = 'flight_departed',
  FLIGHT_LANDED = 'flight_landed',
  FLIGHT_DIVERTED = 'flight_diverted',
  FLIGHT_DELAYED = 'flight_delayed',
  FLIGHT_CANCELLED = 'flight_cancelled',
  POSITION_UPDATE = 'position_update',
  SYSTEM = 'system',
  // Job-related notification types
  JOB_CREATED = 'job_created',
  JOB_UPDATED = 'job_updated',
  JOB_CANCELLED = 'job_cancelled',
  JOB_APPLICATION_RECEIVED = 'job_application_received',
  JOB_APPLICATION_ACCEPTED = 'job_application_accepted',
  JOB_APPLICATION_REJECTED = 'job_application_rejected',
  ESCROW_FUNDED = 'escrow_funded',
  ESCROW_RELEASED = 'escrow_released',
  EXPENSE_SUBMITTED = 'expense_submitted',
  EXPENSE_APPROVED = 'expense_approved',
  EXPENSE_REJECTED = 'expense_rejected'
}

// Interface for notification
interface Notification {
  id?: number;
  trackingId?: number;
  jobId?: number;
  userId: number;
  message: string;
  type: NotificationType;
  read: boolean;
  createdAt?: Date;
  data?: any; // Additional data for the notification (e.g., job details, application details)
}

/**
 * Create a new notification
 * @param notification The notification to create
 */
export async function createNotification(notification: Notification) {
  try {
    // Check if we need to update the tracking_notifications table schema
    await ensureNotificationTableSchema();

    // Build the query dynamically based on the provided fields
    let fields = ['user_id', 'message', 'type', 'read', 'created_at'];
    let values = [
      notification.userId,
      notification.message,
      notification.type,
      notification.read || false,
      'NOW()'
    ];
    let placeholders = ['$1', '$2', '$3', '$4', '$5'];
    let paramIndex = 6;

    // Add tracking_id if provided
    if (notification.trackingId !== undefined) {
      fields.push('tracking_id');
      values.push(notification.trackingId);
      placeholders.push(`$${paramIndex}`);
      paramIndex++;
    }

    // Add job_id if provided
    if (notification.jobId !== undefined) {
      fields.push('job_id');
      values.push(notification.jobId);
      placeholders.push(`$${paramIndex}`);
      paramIndex++;
    }

    // Add data if provided
    if (notification.data !== undefined) {
      fields.push('data');
      values.push(JSON.stringify(notification.data));
      placeholders.push(`$${paramIndex}`);
      paramIndex++;
    }

    // Remove NOW() from values as it's a SQL function
    values.pop();

    const result = await query(
      `INSERT INTO tracking_notifications (${fields.join(', ')}) 
       VALUES (${placeholders.join(', ')}) RETURNING *`,
      values
    );

    return {
      success: true,
      notification: result.rows[0]
    };
  } catch (error) {
    console.error('Error creating notification:', error);
    return {
      success: false,
      message: 'Failed to create notification'
    };
  }
}

/**
 * Ensure the tracking_notifications table has the necessary columns
 * This is a helper function to make sure the table schema is up to date
 */
async function ensureNotificationTableSchema() {
  try {
    // Check if job_id column exists
    const jobIdColumnCheck = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'tracking_notifications' AND column_name = 'job_id'
    `);

    // Add job_id column if it doesn't exist
    if (jobIdColumnCheck.rows.length === 0) {
      await query(`
        ALTER TABLE tracking_notifications 
        ADD COLUMN job_id INTEGER REFERENCES jobs(id)
      `);
    }

    // Check if data column exists
    const dataColumnCheck = await query(`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'tracking_notifications' AND column_name = 'data'
    `);

    // Add data column if it doesn't exist
    if (dataColumnCheck.rows.length === 0) {
      await query(`
        ALTER TABLE tracking_notifications 
        ADD COLUMN data JSONB
      `);
    }

    // Make tracking_id nullable if it's not already
    const trackingIdNullableCheck = await query(`
      SELECT is_nullable 
      FROM information_schema.columns 
      WHERE table_name = 'tracking_notifications' AND column_name = 'tracking_id'
    `);

    if (trackingIdNullableCheck.rows.length > 0 && trackingIdNullableCheck.rows[0].is_nullable === 'NO') {
      await query(`
        ALTER TABLE tracking_notifications 
        ALTER COLUMN tracking_id DROP NOT NULL
      `);
    }

    return true;
  } catch (error) {
    console.error('Error ensuring notification table schema:', error);
    return false;
  }
}

/**
 * Get notifications for a user
 * @param userId The user ID to get notifications for
 * @param limit The maximum number of notifications to return
 * @param offset The offset for pagination
 * @param unreadOnly Whether to only return unread notifications
 */
export async function getUserNotifications(
  userId: number,
  limit: number = 20,
  offset: number = 0,
  unreadOnly: boolean = false
) {
  try {
    // Ensure the notification table schema is up to date
    await ensureNotificationTableSchema();

    // Query that handles both tracking-related and job-related notifications
    let queryText = `
      SELECT 
        n.*,
        CASE 
          WHEN n.tracking_id IS NOT NULL THEN 'tracking'
          WHEN n.job_id IS NOT NULL THEN 'job'
          ELSE 'system'
        END as notification_source,
        j.title as job_title,
        j.job_type,
        COALESCE(j.origin, ft.origin) as origin,
        COALESCE(j.destination, ft.destination) as destination,
        ft.registration,
        ft.aircraft_type
      FROM tracking_notifications n
      LEFT JOIN flight_tracking ft ON n.tracking_id = ft.id
      LEFT JOIN jobs j ON (n.job_id = j.id OR (n.tracking_id IS NOT NULL AND ft.job_id = j.id))
      WHERE n.user_id = $1
    `;

    if (unreadOnly) {
      queryText += ` AND n.read = false`;
    }

    queryText += ` ORDER BY n.created_at DESC LIMIT $2 OFFSET $3`;

    const result = await pool.query(queryText, [userId, limit, offset]);

    // Process the notifications to include the data field if it exists
    const notifications = result.rows.map(row => {
      try {
        if (row.data) {
          row.data = JSON.parse(row.data);
        }
      } catch (e) {
        console.error('Error parsing notification data:', e);
      }
      return row;
    });

    return {
      success: true,
      notifications
    };
  } catch (error) {
    console.error('Error getting user notifications:', error);
    return {
      success: false,
      message: 'Failed to get notifications'
    };
  }
}

/**
 * Mark notifications as read
 * @param notificationIds The notification IDs to mark as read
 * @param userId The user ID to verify ownership
 */
export async function markNotificationsAsRead(notificationIds: number[], userId: number) {
  try {
    const result = await query(
      `UPDATE tracking_notifications 
       SET read = true 
       WHERE id = ANY($1) AND user_id = $2 
       RETURNING *`,
      [notificationIds, userId]
    );

    return {
      success: true,
      updatedCount: result.rowCount,
      notifications: result.rows
    };
  } catch (error) {
    console.error('Error marking notifications as read:', error);
    return {
      success: false,
      message: 'Failed to mark notifications as read'
    };
  }
}

/**
 * Create tracking status change notification
 * @param trackingId The tracking ID
 * @param jobId The job ID
 * @param oldStatus The old tracking status
 * @param newStatus The new tracking status
 */
export async function createTrackingStatusNotification(
  trackingId: number,
  jobId: number,
  oldStatus: TrackingStatus,
  newStatus: TrackingStatus
) {
  try {
    // Get job owner and assigned pilot
    const jobResult = await query(
      `SELECT j.owner_id, ja.pilot_id 
       FROM jobs j
       LEFT JOIN job_applications ja ON j.id = ja.job_id AND ja.status = 'accepted'
       WHERE j.id = $1`,
      [jobId]
    );

    if (jobResult.rows.length === 0) {
      return {
        success: false,
        message: 'Job not found'
      };
    }

    const { owner_id, pilot_id } = jobResult.rows[0];

    // Determine notification type and message
    let type: NotificationType;
    let message: string;

    switch (newStatus) {
      case TrackingStatus.IN_PROGRESS:
        type = NotificationType.TRACKING_STARTED;
        message = 'Flight tracking has been initiated';
        break;
      case TrackingStatus.COMPLETED:
        type = NotificationType.TRACKING_COMPLETED;
        message = 'Flight has been completed successfully';
        break;
      case TrackingStatus.DELAYED:
        type = NotificationType.FLIGHT_DELAYED;
        message = 'Flight has been delayed';
        break;
      case TrackingStatus.CANCELLED:
        type = NotificationType.FLIGHT_CANCELLED;
        message = 'Flight has been cancelled';
        break;
      default:
        type = NotificationType.TRACKING_UPDATED;
        message = `Flight status updated to ${newStatus}`;
    }

    // Create notifications for owner and pilot
    const notifications = [];

    if (owner_id) {
      const ownerNotification = await createNotification({
        trackingId,
        userId: owner_id,
        message,
        type,
        read: false
      });

      if (ownerNotification.success) {
        notifications.push(ownerNotification.notification);
      }
    }

    if (pilot_id) {
      const pilotNotification = await createNotification({
        trackingId,
        userId: pilot_id,
        message,
        type,
        read: false
      });

      if (pilotNotification.success) {
        notifications.push(pilotNotification.notification);
      }
    }

    return {
      success: true,
      notifications
    };
  } catch (error) {
    console.error('Error creating tracking status notification:', error);
    return {
      success: false,
      message: 'Failed to create tracking status notification'
    };
  }
}

/**
 * Create flight status change notification
 * @param trackingId The tracking ID
 * @param jobId The job ID
 * @param flightStatus The flight status
 */
export async function createFlightStatusNotification(
  trackingId: number,
  jobId: number,
  flightStatus: FlightStatus
) {
  try {
    // Get job owner and assigned pilot
    const jobResult = await query(
      `SELECT j.owner_id, ja.pilot_id 
       FROM jobs j
       LEFT JOIN job_applications ja ON j.id = ja.job_id AND ja.status = 'accepted'
       WHERE j.id = $1`,
      [jobId]
    );

    if (jobResult.rows.length === 0) {
      return {
        success: false,
        message: 'Job not found'
      };
    }

    const { owner_id, pilot_id } = jobResult.rows[0];

    // Determine notification type and message
    let type: NotificationType;
    let message: string;

    switch (flightStatus) {
      case FlightStatus.DEPARTED:
        type = NotificationType.FLIGHT_DEPARTED;
        message = 'Aircraft has departed';
        break;
      case FlightStatus.LANDED:
        type = NotificationType.FLIGHT_LANDED;
        message = 'Aircraft has landed at destination';
        break;
      case FlightStatus.DIVERTED:
        type = NotificationType.FLIGHT_DIVERTED;
        message = 'Flight has been diverted';
        break;
      case FlightStatus.CANCELLED:
        type = NotificationType.FLIGHT_CANCELLED;
        message = 'Flight has been cancelled';
        break;
      default:
        type = NotificationType.TRACKING_UPDATED;
        message = `Flight status updated to ${flightStatus}`;
    }

    // Create notifications for owner and pilot
    const notifications = [];

    if (owner_id) {
      const ownerNotification = await createNotification({
        trackingId,
        userId: owner_id,
        message,
        type,
        read: false
      });

      if (ownerNotification.success) {
        notifications.push(ownerNotification.notification);
      }
    }

    if (pilot_id) {
      const pilotNotification = await createNotification({
        trackingId,
        userId: pilot_id,
        message,
        type,
        read: false
      });

      if (pilotNotification.success) {
        notifications.push(pilotNotification.notification);
      }
    }

    return {
      success: true,
      notifications
    };
  } catch (error) {
    console.error('Error creating flight status notification:', error);
    return {
      success: false,
      message: 'Failed to create flight status notification'
    };
  }
}

/**
 * Get unread notification count for a user
 * @param userId The user ID to get notification count for
 */
export async function getUnreadNotificationCount(userId: number) {
  try {
    const result = await query(
      'SELECT COUNT(*) FROM tracking_notifications WHERE user_id = $1 AND read = false',
      [userId]
    );

    return {
      success: true,
      count: parseInt(result.rows[0].count)
    };
  } catch (error) {
    console.error('Error getting unread notification count:', error);
    return {
      success: false,
      message: 'Failed to get unread notification count'
    };
  }
}

/**
 * Create a job update notification
 * @param jobId The job ID
 * @param updateType The type of update (created, updated, cancelled, etc.)
 * @param data Additional data for the notification
 */
export async function createJobUpdateNotification(
  jobId: number,
  updateType: NotificationType,
  data: any = {}
) {
  try {
    // Get job details including owner and assigned pilot
    const jobResult = await query(
      `SELECT j.*, j.owner_id, ja.pilot_id 
       FROM jobs j
       LEFT JOIN job_applications ja ON j.id = ja.job_id AND ja.status = 'accepted'
       WHERE j.id = $1`,
      [jobId]
    );

    if (jobResult.rows.length === 0) {
      return {
        success: false,
        message: 'Job not found'
      };
    }

    const job = jobResult.rows[0];
    const { owner_id, pilot_id, title, job_type } = job;

    // Determine message based on update type
    let message: string;

    switch (updateType) {
      case NotificationType.JOB_CREATED:
        message = `New job created: ${title}`;
        break;
      case NotificationType.JOB_UPDATED:
        message = `Job updated: ${title}`;
        break;
      case NotificationType.JOB_CANCELLED:
        message = `Job cancelled: ${title}`;
        break;
      case NotificationType.JOB_APPLICATION_RECEIVED:
        message = `New application received for job: ${title}`;
        break;
      case NotificationType.JOB_APPLICATION_ACCEPTED:
        message = `Your application for job "${title}" has been accepted`;
        break;
      case NotificationType.JOB_APPLICATION_REJECTED:
        message = `Your application for job "${title}" has been rejected`;
        break;
      case NotificationType.ESCROW_FUNDED:
        message = `Escrow account for job "${title}" has been funded`;
        break;
      case NotificationType.ESCROW_RELEASED:
        message = `Payment for job "${title}" has been released from escrow`;
        break;
      case NotificationType.EXPENSE_SUBMITTED:
        message = `New expense submitted for job: ${title}`;
        break;
      case NotificationType.EXPENSE_APPROVED:
        message = `Your expense for job "${title}" has been approved`;
        break;
      case NotificationType.EXPENSE_REJECTED:
        message = `Your expense for job "${title}" has been rejected`;
        break;
      default:
        message = `Job update: ${title}`;
    }

    // Create notifications for owner and/or pilot based on the update type
    const notifications = [];

    // Determine who should receive the notification
    let notifyOwner = false;
    let notifyPilot = false;

    switch (updateType) {
      case NotificationType.JOB_APPLICATION_RECEIVED:
        notifyOwner = true;
        break;
      case NotificationType.JOB_APPLICATION_ACCEPTED:
      case NotificationType.JOB_APPLICATION_REJECTED:
      case NotificationType.EXPENSE_APPROVED:
      case NotificationType.EXPENSE_REJECTED:
        notifyPilot = true;
        break;
      case NotificationType.ESCROW_FUNDED:
        notifyPilot = true;
        break;
      case NotificationType.ESCROW_RELEASED:
        notifyPilot = true;
        break;
      case NotificationType.EXPENSE_SUBMITTED:
        notifyOwner = true;
        break;
      default:
        notifyOwner = true;
        notifyPilot = true;
    }

    // Create notification for owner if needed
    if (notifyOwner && owner_id) {
      const ownerNotification = await createNotification({
        jobId,
        userId: owner_id,
        message,
        type: updateType,
        read: false,
        data: {
          ...data,
          job_title: title,
          job_type
        }
      });

      if (ownerNotification.success) {
        notifications.push(ownerNotification.notification);
      }
    }

    // Create notification for pilot if needed
    if (notifyPilot && pilot_id) {
      const pilotNotification = await createNotification({
        jobId,
        userId: pilot_id,
        message,
        type: updateType,
        read: false,
        data: {
          ...data,
          job_title: title,
          job_type
        }
      });

      if (pilotNotification.success) {
        notifications.push(pilotNotification.notification);
      }
    }

    return {
      success: true,
      notifications
    };
  } catch (error) {
    console.error('Error creating job update notification:', error);
    return {
      success: false,
      message: 'Failed to create job update notification'
    };
  }
}
