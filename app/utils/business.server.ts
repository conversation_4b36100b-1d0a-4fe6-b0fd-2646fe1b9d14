import { db, query } from './db.server';
import { encrypt } from './encryption.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { User } from './users.server';

// Define Business type
export interface Business {
  id: number;
  name: string;
  description?: string;
  ownerId: number;
  createdAt: Date;
  updatedAt?: Date;
}

// Define Role type
export interface Role {
  id: number;
  name: string;
  businessId: number;
  createdAt: Date;
  updatedAt?: Date;
  permissions?: Permission[];
}

// Define Permission type
export interface Permission {
  id: number;
  name: string;
  description?: string;
  createdAt: Date;
}

// Define Employee type (extends User)
export interface Employee extends User {
  businessId: number;
  isBusinessAdmin: boolean;
  roles?: Role[];
}

/**
 * Create a new business
 * @param ownerId User ID of the business owner
 * @param name Business name
 * @param description Business description
 * @returns Newly created business
 */
export async function createBusiness(
  ownerId: number,
  name: string,
  description?: string
): Promise<Business> {
  const result = await query(
    `INSERT INTO businesses (
      owner_id,
      name,
      description,
      created_at
    ) VALUES ($1, $2, $3, NOW()) RETURNING *`,
    [ownerId, name, description || null]
  );

  const business = result.rows[0];

  // Update the owner's account to be associated with the business
  await query(
    `UPDATE users SET 
      account_type = 'business', 
      business_id = $1, 
      is_business_admin = true,
      updated_at = NOW()
    WHERE id = $2`,
    [business.id, ownerId]
  );

  // Log business creation
  logSecurityEventAsync({
    user_id: ownerId,
    event_type: 'business_created',
    description: `Business created: ${name} (ID: ${business.id})`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  // Create default roles for the business
  await createDefaultRoles(business.id);

  return {
    id: business.id,
    name: business.name,
    description: business.description,
    ownerId: business.owner_id,
    createdAt: new Date(business.created_at),
    updatedAt: business.updated_at ? new Date(business.updated_at) : undefined
  };
}

/**
 * Get a business by ID
 * @param businessId Business ID
 * @returns Business object or null if not found
 */
export async function getBusinessById(businessId: number): Promise<Business | null> {
  const result = await query('SELECT * FROM businesses WHERE id = $1', [businessId]);

  if (result.rows.length === 0) {
    return null;
  }

  const business = result.rows[0];
  return {
    id: business.id,
    name: business.name,
    description: business.description,
    ownerId: business.owner_id,
    createdAt: new Date(business.created_at),
    updatedAt: business.updated_at ? new Date(business.updated_at) : undefined
  };
}

/**
 * Get businesses owned by a user
 * @param userId User ID
 * @returns Array of businesses owned by the user
 */
export async function getBusinessesByOwnerId(userId: number): Promise<Business[]> {
  const result = await query('SELECT * FROM businesses WHERE owner_id = $1', [userId]);

  return result.rows.map(business => ({
    id: business.id,
    name: business.name,
    description: business.description,
    ownerId: business.owner_id,
    createdAt: new Date(business.created_at),
    updatedAt: business.updated_at ? new Date(business.updated_at) : undefined
  }));
}

/**
 * Update a business
 * @param businessId Business ID
 * @param updates Object containing fields to update
 * @returns Updated business
 */
export async function updateBusiness(
  businessId: number,
  updates: {
    name?: string;
    description?: string;
  }
): Promise<Business | null> {
  const { name, description } = updates;
  
  // Build the SET clause dynamically based on provided updates
  const setClauses = [];
  const values = [];
  let paramIndex = 1;

  if (name !== undefined) {
    setClauses.push(`name = $${paramIndex}`);
    values.push(name);
    paramIndex++;
  }

  if (description !== undefined) {
    setClauses.push(`description = $${paramIndex}`);
    values.push(description);
    paramIndex++;
  }

  // Add updated_at timestamp
  setClauses.push(`updated_at = NOW()`);

  // If no updates provided, return the current business
  if (setClauses.length === 1) {
    return getBusinessById(businessId);
  }

  // Add businessId to values array
  values.push(businessId);

  const result = await query(
    `UPDATE businesses SET ${setClauses.join(', ')} WHERE id = $${paramIndex} RETURNING *`,
    values
  );

  if (result.rows.length === 0) {
    return null;
  }

  const business = result.rows[0];
  return {
    id: business.id,
    name: business.name,
    description: business.description,
    ownerId: business.owner_id,
    createdAt: new Date(business.created_at),
    updatedAt: business.updated_at ? new Date(business.updated_at) : undefined
  };
}

/**
 * Add an employee to a business
 * @param businessId Business ID
 * @param email Employee email
 * @param name Employee name
 * @param password Employee password
 * @param isAdmin Whether the employee is a business admin
 * @returns Newly created employee
 */
export async function addEmployeeToBusiness(
  businessId: number,
  email: string,
  name: string,
  password: string,
  isAdmin: boolean = false
): Promise<Employee | null> {
  // Check if the business exists
  const business = await getBusinessById(businessId);
  if (!business) {
    return null;
  }

  // Check if the user already exists
  const existingUserResult = await query('SELECT * FROM users WHERE email = $1', [email]);
  if (existingUserResult.rows.length > 0) {
    return null;
  }

  // Encrypt sensitive data
  const encryptedEmail = encrypt(email);
  const bcrypt = require('bcryptjs');
  const passwordHash = await bcrypt.hash(password, 10);

  // Create the user with business association
  const result = await query(
    `INSERT INTO users (
      email,
      password_hash,
      name,
      role,
      account_type,
      business_id,
      is_business_admin,
      created_at,
      verification_status
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, NOW(), 'verified') RETURNING *`,
    [encryptedEmail, passwordHash, name, 'owner', 'business_employee', businessId, isAdmin]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const user = result.rows[0];

  // Log employee addition
  logSecurityEventAsync({
    user_id: business.ownerId,
    event_type: 'employee_added',
    description: `Employee added to business: ${email} (Business ID: ${businessId})`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return {
    id: user.id,
    email: email, // Use the original email
    name: user.name,
    role: user.role,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined
  };
}

/**
 * Get employees of a business
 * @param businessId Business ID
 * @returns Array of employees
 */
export async function getBusinessEmployees(businessId: number): Promise<Employee[]> {
  const result = await query(
    'SELECT * FROM users WHERE business_id = $1 ORDER BY name',
    [businessId]
  );

  return result.rows.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined
  }));
}

/**
 * Update an employee's business admin status
 * @param userId User ID of the employee
 * @param isAdmin New admin status
 * @returns Updated employee
 */
export async function updateEmployeeAdminStatus(
  userId: number,
  isAdmin: boolean
): Promise<Employee | null> {
  const result = await query(
    `UPDATE users SET 
      is_business_admin = $1,
      updated_at = NOW()
    WHERE id = $2 RETURNING *`,
    [isAdmin, userId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const user = result.rows[0];
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined
  };
}

/**
 * Remove an employee from a business
 * @param userId User ID of the employee
 * @returns True if successful, false otherwise
 */
export async function removeEmployeeFromBusiness(userId: number): Promise<boolean> {
  // First, remove any roles assigned to the user
  await query('DELETE FROM user_roles WHERE user_id = $1', [userId]);

  // Then update the user to remove business association
  const result = await query(
    `UPDATE users SET 
      business_id = NULL,
      is_business_admin = FALSE,
      account_type = 'individual',
      updated_at = NOW()
    WHERE id = $1 RETURNING *`,
    [userId]
  );

  return result.rows.length > 0;
}

/**
 * Create default roles for a business
 * @param businessId Business ID
 */
async function createDefaultRoles(businessId: number): Promise<void> {
  // Create Admin role with all permissions
  const adminRoleResult = await query(
    `INSERT INTO roles (name, business_id, created_at) 
     VALUES ('Admin', $1, NOW()) RETURNING *`,
    [businessId]
  );
  
  const adminRoleId = adminRoleResult.rows[0].id;
  
  // Get all permissions
  const permissionsResult = await query('SELECT id FROM permissions');
  
  // Assign all permissions to Admin role
  for (const permission of permissionsResult.rows) {
    await query(
      `INSERT INTO role_permissions (role_id, permission_id, created_at) 
       VALUES ($1, $2, NOW())`,
      [adminRoleId, permission.id]
    );
  }
  
  // Create Manager role with most permissions except manage_roles
  const managerRoleResult = await query(
    `INSERT INTO roles (name, business_id, created_at) 
     VALUES ('Manager', $1, NOW()) RETURNING *`,
    [businessId]
  );
  
  const managerRoleId = managerRoleResult.rows[0].id;
  
  // Assign manager permissions
  const managerPermissions = [
    'view_jobs', 'create_jobs', 'edit_jobs', 'delete_jobs', 'approve_jobs',
    'view_applications', 'approve_applications',
    'manage_employees', 'view_finances', 'manage_finances',
    'approve_bid_inquiry'
  ];
  
  for (const permName of managerPermissions) {
    const permResult = await query('SELECT id FROM permissions WHERE name = $1', [permName]);
    if (permResult.rows.length > 0) {
      await query(
        `INSERT INTO role_permissions (role_id, permission_id, created_at) 
         VALUES ($1, $2, NOW())`,
        [managerRoleId, permResult.rows[0].id]
      );
    }
  }
  
  // Create Employee role with basic permissions
  const employeeRoleResult = await query(
    `INSERT INTO roles (name, business_id, created_at) 
     VALUES ('Employee', $1, NOW()) RETURNING *`,
    [businessId]
  );
  
  const employeeRoleId = employeeRoleResult.rows[0].id;
  
  // Assign employee permissions
  const employeePermissions = [
    'view_jobs', 'create_jobs', 'view_applications'
  ];
  
  for (const permName of employeePermissions) {
    const permResult = await query('SELECT id FROM permissions WHERE name = $1', [permName]);
    if (permResult.rows.length > 0) {
      await query(
        `INSERT INTO role_permissions (role_id, permission_id, created_at) 
         VALUES ($1, $2, NOW())`,
        [employeeRoleId, permResult.rows[0].id]
      );
    }
  }
}

/**
 * Get all roles for a business
 * @param businessId Business ID
 * @returns Array of roles
 */
export async function getBusinessRoles(businessId: number): Promise<Role[]> {
  const result = await query(
    'SELECT * FROM roles WHERE business_id = $1 ORDER BY name',
    [businessId]
  );

  return result.rows.map(role => ({
    id: role.id,
    name: role.name,
    businessId: role.business_id,
    createdAt: new Date(role.created_at),
    updatedAt: role.updated_at ? new Date(role.updated_at) : undefined
  }));
}

/**
 * Get a role by ID with its permissions
 * @param roleId Role ID
 * @returns Role with permissions or null if not found
 */
export async function getRoleWithPermissions(roleId: number): Promise<Role | null> {
  const roleResult = await query('SELECT * FROM roles WHERE id = $1', [roleId]);
  
  if (roleResult.rows.length === 0) {
    return null;
  }
  
  const role = roleResult.rows[0];
  
  // Get permissions for this role
  const permissionsResult = await query(
    `SELECT p.* FROM permissions p
     JOIN role_permissions rp ON p.id = rp.permission_id
     WHERE rp.role_id = $1
     ORDER BY p.name`,
    [roleId]
  );
  
  const permissions = permissionsResult.rows.map(perm => ({
    id: perm.id,
    name: perm.name,
    description: perm.description,
    createdAt: new Date(perm.created_at)
  }));
  
  return {
    id: role.id,
    name: role.name,
    businessId: role.business_id,
    createdAt: new Date(role.created_at),
    updatedAt: role.updated_at ? new Date(role.updated_at) : undefined,
    permissions
  };
}

/**
 * Assign a role to a user
 * @param userId User ID
 * @param roleId Role ID
 * @returns True if successful, false otherwise
 */
export async function assignRoleToUser(userId: number, roleId: number): Promise<boolean> {
  try {
    await query(
      `INSERT INTO user_roles (user_id, role_id, created_at) 
       VALUES ($1, $2, NOW())
       ON CONFLICT (user_id, role_id) DO NOTHING`,
      [userId, roleId]
    );
    return true;
  } catch (error) {
    console.error('Error assigning role to user:', error);
    return false;
  }
}

/**
 * Remove a role from a user
 * @param userId User ID
 * @param roleId Role ID
 * @returns True if successful, false otherwise
 */
export async function removeRoleFromUser(userId: number, roleId: number): Promise<boolean> {
  const result = await query(
    'DELETE FROM user_roles WHERE user_id = $1 AND role_id = $2',
    [userId, roleId]
  );
  
  return result.rowCount > 0;
}

/**
 * Get all roles assigned to a user
 * @param userId User ID
 * @returns Array of roles
 */
export async function getUserRoles(userId: number): Promise<Role[]> {
  const result = await query(
    `SELECT r.* FROM roles r
     JOIN user_roles ur ON r.id = ur.role_id
     WHERE ur.user_id = $1
     ORDER BY r.name`,
    [userId]
  );
  
  return result.rows.map(role => ({
    id: role.id,
    name: role.name,
    businessId: role.business_id,
    createdAt: new Date(role.created_at),
    updatedAt: role.updated_at ? new Date(role.updated_at) : undefined
  }));
}

/**
 * Check if a user has a specific permission
 * @param userId User ID
 * @param permissionName Permission name
 * @returns True if the user has the permission, false otherwise
 */
export async function userHasPermission(userId: number, permissionName: string): Promise<boolean> {
  // First check if the user is a business admin
  const userResult = await query(
    'SELECT is_business_admin FROM users WHERE id = $1',
    [userId]
  );
  
  if (userResult.rows.length === 0) {
    return false;
  }
  
  // Business admins have all permissions
  if (userResult.rows[0].is_business_admin) {
    return true;
  }
  
  // Check if the user has a role with the specified permission
  const result = await query(
    `SELECT 1 FROM permissions p
     JOIN role_permissions rp ON p.id = rp.permission_id
     JOIN user_roles ur ON rp.role_id = ur.role_id
     WHERE ur.user_id = $1 AND p.name = $2
     LIMIT 1`,
    [userId, permissionName]
  );
  
  return result.rows.length > 0;
}

/**
 * Create a new role for a business
 * @param businessId Business ID
 * @param name Role name
 * @param permissionIds Array of permission IDs to assign to the role
 * @returns Newly created role
 */
export async function createRole(
  businessId: number,
  name: string,
  permissionIds: number[]
): Promise<Role | null> {
  // Create the role
  const roleResult = await query(
    `INSERT INTO roles (name, business_id, created_at) 
     VALUES ($1, $2, NOW()) RETURNING *`,
    [name, businessId]
  );
  
  if (roleResult.rows.length === 0) {
    return null;
  }
  
  const roleId = roleResult.rows[0].id;
  
  // Assign permissions to the role
  for (const permId of permissionIds) {
    await query(
      `INSERT INTO role_permissions (role_id, permission_id, created_at) 
       VALUES ($1, $2, NOW())`,
      [roleId, permId]
    );
  }
  
  // Return the role with permissions
  return getRoleWithPermissions(roleId);
}

/**
 * Update a role's permissions
 * @param roleId Role ID
 * @param name New role name
 * @param permissionIds Array of permission IDs to assign to the role
 * @returns Updated role
 */
export async function updateRole(
  roleId: number,
  name: string,
  permissionIds: number[]
): Promise<Role | null> {
  // Update the role name
  const roleResult = await query(
    `UPDATE roles SET name = $1, updated_at = NOW() WHERE id = $2 RETURNING *`,
    [name, roleId]
  );
  
  if (roleResult.rows.length === 0) {
    return null;
  }
  
  // Remove existing permissions
  await query('DELETE FROM role_permissions WHERE role_id = $1', [roleId]);
  
  // Assign new permissions
  for (const permId of permissionIds) {
    await query(
      `INSERT INTO role_permissions (role_id, permission_id, created_at) 
       VALUES ($1, $2, NOW())`,
      [roleId, permId]
    );
  }
  
  // Return the updated role with permissions
  return getRoleWithPermissions(roleId);
}

/**
 * Delete a role
 * @param roleId Role ID
 * @returns True if successful, false otherwise
 */
export async function deleteRole(roleId: number): Promise<boolean> {
  // First remove all user assignments to this role
  await query('DELETE FROM user_roles WHERE role_id = $1', [roleId]);
  
  // Then remove all permission assignments
  await query('DELETE FROM role_permissions WHERE role_id = $1', [roleId]);
  
  // Finally delete the role
  const result = await query('DELETE FROM roles WHERE id = $1', [roleId]);
  
  return result.rowCount > 0;
}

/**
 * Get all permissions
 * @returns Array of all permissions
 */
export async function getAllPermissions(): Promise<Permission[]> {
  const result = await query('SELECT * FROM permissions ORDER BY name');
  
  return result.rows.map(perm => ({
    id: perm.id,
    name: perm.name,
    description: perm.description,
    createdAt: new Date(perm.created_at)
  }));
}