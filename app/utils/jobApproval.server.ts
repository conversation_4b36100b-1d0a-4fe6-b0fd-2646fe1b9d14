import { query } from './db.server';
import { userHasPermission } from './business.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { sendNotification } from './notifications.server';

// Define JobApproval type
export interface JobApproval {
  id: number;
  jobId: number;
  requestedBy: number;
  approvedBy?: number;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Request approval for a job
 * @param jobId Job ID
 * @param requestedBy User ID of the requester
 * @param notes Optional notes for the approval request
 * @returns Newly created job approval request
 */
export async function requestJobApproval(
  jobId: number,
  requestedBy: number,
  notes?: string
): Promise<JobApproval | null> {
  // Check if there's already a pending approval request for this job
  const existingResult = await query(
    'SELECT * FROM job_approvals WHERE job_id = $1 AND status = $2',
    [jobId, 'pending']
  );

  if (existingResult.rows.length > 0) {
    return mapJobApproval(existingResult.rows[0]);
  }

  // Create a new approval request
  const result = await query(
    `INSERT INTO job_approvals (
      job_id,
      requested_by,
      status,
      notes,
      created_at
    ) VALUES ($1, $2, $3, $4, NOW()) RETURNING *`,
    [jobId, requestedBy, 'pending', notes || null]
  );

  if (result.rows.length === 0) {
    return null;
  }

  // Get the business ID of the requester
  const userResult = await query(
    'SELECT business_id FROM users WHERE id = $1',
    [requestedBy]
  );

  if (userResult.rows.length > 0 && userResult.rows[0].business_id) {
    const businessId = userResult.rows[0].business_id;

    // Find users with approval permission in the same business
    const approversResult = await query(
      `SELECT u.id FROM users u
       JOIN user_roles ur ON u.id = ur.user_id
       JOIN role_permissions rp ON ur.role_id = rp.role_id
       JOIN permissions p ON rp.permission_id = p.id
       WHERE u.business_id = $1 AND p.name = $2`,
      [businessId, 'approve_jobs']
    );

    // Send notifications to all potential approvers
    for (const approver of approversResult.rows) {
      await sendNotification({
        userId: approver.id,
        message: `A job requires your approval`,
        type: 'job_approval_request',
        metadata: {
          jobId,
          requestedBy
        }
      });
    }
  }

  // Log the approval request
  logSecurityEventAsync({
    user_id: requestedBy,
    event_type: 'job_approval_requested',
    description: `Job approval requested for job ID: ${jobId}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return mapJobApproval(result.rows[0]);
}

/**
 * Approve or reject a job approval request
 * @param approvalId Approval request ID
 * @param approvedBy User ID of the approver
 * @param approved Whether the job is approved or rejected
 * @param notes Optional notes for the approval decision
 * @returns Updated job approval request
 */
export async function processJobApproval(
  approvalId: number,
  approvedBy: number,
  approved: boolean,
  notes?: string
): Promise<JobApproval | null> {
  // Check if the user has permission to approve jobs
  const hasPermission = await userHasPermission(approvedBy, 'approve_jobs');
  if (!hasPermission) {
    return null;
  }

  // Update the approval request
  const result = await query(
    `UPDATE job_approvals SET
      approved_by = $1,
      status = $2,
      notes = CASE WHEN $3 IS NOT NULL THEN $3 ELSE notes END,
      updated_at = NOW()
    WHERE id = $4 RETURNING *`,
    [approvedBy, approved ? 'approved' : 'rejected', notes, approvalId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const approval = result.rows[0];

  // If approved, update the job status to active
  if (approved) {
    await query(
      'UPDATE jobs SET status = $1, updated_at = NOW() WHERE id = $2',
      ['active', approval.job_id]
    );
  }

  // Send notification to the requester
  await sendNotification({
    userId: approval.requested_by,
    message: `Your job has been ${approved ? 'approved' : 'rejected'}`,
    type: 'job_approval_processed',
    metadata: {
      jobId: approval.job_id,
      approved,
      approvedBy
    }
  });

  // Log the approval decision
  logSecurityEventAsync({
    user_id: approvedBy,
    event_type: approved ? 'job_approval_approved' : 'job_approval_rejected',
    description: `Job ${approved ? 'approved' : 'rejected'} for job ID: ${approval.job_id}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return mapJobApproval(approval);
}

/**
 * Get a job approval request by ID
 * @param approvalId Approval request ID
 * @returns Job approval request or null if not found
 */
export async function getJobApprovalById(approvalId: number): Promise<JobApproval | null> {
  const result = await query(
    'SELECT * FROM job_approvals WHERE id = $1',
    [approvalId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  return mapJobApproval(result.rows[0]);
}

/**
 * Get job approval request for a job
 * @param jobId Job ID
 * @returns Job approval request or null if not found
 */
export async function getJobApprovalForJob(jobId: number): Promise<JobApproval | null> {
  const result = await query(
    'SELECT * FROM job_approvals WHERE job_id = $1 ORDER BY created_at DESC LIMIT 1',
    [jobId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  return mapJobApproval(result.rows[0]);
}

/**
 * Get pending job approval requests for a business
 * @param businessId Business ID
 * @returns Array of pending job approval requests
 */
export async function getPendingJobApprovalsForBusiness(businessId: number): Promise<JobApproval[]> {
  const result = await query(
    `SELECT ja.* FROM job_approvals ja
     JOIN users u ON ja.requested_by = u.id
     WHERE u.business_id = $1 AND ja.status = $2
     ORDER BY ja.created_at DESC`,
    [businessId, 'pending']
  );

  return result.rows.map(mapJobApproval);
}

/**
 * Get job approval requests requested by a user
 * @param userId User ID
 * @returns Array of job approval requests
 */
export async function getJobApprovalsRequestedByUser(userId: number): Promise<JobApproval[]> {
  const result = await query(
    'SELECT * FROM job_approvals WHERE requested_by = $1 ORDER BY created_at DESC',
    [userId]
  );

  return result.rows.map(mapJobApproval);
}

/**
 * Map database row to JobApproval interface
 * @param row Database row
 * @returns JobApproval object
 */
function mapJobApproval(row: any): JobApproval {
  return {
    id: row.id,
    jobId: row.job_id,
    requestedBy: row.requested_by,
    approvedBy: row.approved_by,
    status: row.status,
    notes: row.notes,
    createdAt: new Date(row.created_at),
    updatedAt: row.updated_at ? new Date(row.updated_at) : undefined
  };
}