import { db } from './db.server';
import { v4 as uuidv4 } from 'uuid';
import { logSecurityEvent } from './securityAudit.server';
import { IdDocumentType } from './idVerification.server';

// Interface for short URL data
export interface ShortUrlData {
  id: number;
  shortCode: string;
  userId: number;
  documentType: IdDocumentType;
  createdAt: Date;
  expiresAt: Date;
  usedAt?: Date;
  ipAddress?: string;
  userAgent?: string;
}

/**
 * Generate a short URL for mobile document capture
 * @param userId User ID
 * @param documentType Document type
 * @param expirationMinutes Minutes until the URL expires (default: 30)
 * @returns Short URL data
 */
export async function generateShortUrl(
  userId: number,
  documentType: IdDocumentType,
  expirationMinutes: number = 30
): Promise<ShortUrlData> {
  try {
    // Generate a short, unique code
    const shortCode = generateShortCode();
    
    // Calculate expiration time
    const now = new Date();
    const expiresAt = new Date(now.getTime() + expirationMinutes * 60 * 1000);
    
    // Save to database
    const result = await db
      .insertInto('document_capture_urls')
      .values({
        short_code: shortCode,
        user_id: userId,
        document_type: documentType,
        created_at: now,
        expires_at: expiresAt
      })
      .returning(['id', 'short_code', 'user_id', 'document_type', 'created_at', 'expires_at'])
      .executeTakeFirstOrThrow();
    
    // Log the short URL creation
    await logSecurityEvent({
      user_id: userId,
      event_type: 'short_url_created',
      description: `Short URL created for document capture: ${documentType}`,
      ip_address: '127.0.0.1', // This should be passed from the request
      user_agent: 'Server' // This should be passed from the request
    });
    
    return {
      id: result.id,
      shortCode: result.short_code,
      userId: result.user_id,
      documentType: result.document_type as IdDocumentType,
      createdAt: new Date(result.created_at),
      expiresAt: new Date(result.expires_at)
    };
  } catch (error) {
    console.error('Error generating short URL:', error);
    throw error;
  }
}

/**
 * Get short URL data by code
 * @param shortCode Short URL code
 * @returns Short URL data if valid and not expired, null otherwise
 */
export async function getShortUrlData(shortCode: string): Promise<ShortUrlData | null> {
  try {
    const result = await db
      .selectFrom('document_capture_urls')
      .selectAll()
      .where('short_code', '=', shortCode)
      .executeTakeFirst();
    
    if (!result) {
      return null;
    }
    
    // Check if expired
    const now = new Date();
    const expiresAt = new Date(result.expires_at);
    
    if (now > expiresAt) {
      return null;
    }
    
    return {
      id: result.id,
      shortCode: result.short_code,
      userId: result.user_id,
      documentType: result.document_type as IdDocumentType,
      createdAt: new Date(result.created_at),
      expiresAt: expiresAt,
      usedAt: result.used_at ? new Date(result.used_at) : undefined,
      ipAddress: result.ip_address,
      userAgent: result.user_agent
    };
  } catch (error) {
    console.error('Error getting short URL data:', error);
    return null;
  }
}

/**
 * Mark a short URL as used
 * @param shortCode Short URL code
 * @param ipAddress IP address of the user
 * @param userAgent User agent of the user
 * @returns Updated short URL data
 */
export async function markShortUrlAsUsed(
  shortCode: string,
  ipAddress: string,
  userAgent: string
): Promise<ShortUrlData | null> {
  try {
    const now = new Date();
    
    const result = await db
      .updateTable('document_capture_urls')
      .set({
        used_at: now,
        ip_address: ipAddress,
        user_agent: userAgent
      })
      .where('short_code', '=', shortCode)
      .returning(['id', 'short_code', 'user_id', 'document_type', 'created_at', 'expires_at', 'used_at', 'ip_address', 'user_agent'])
      .executeTakeFirst();
    
    if (!result) {
      return null;
    }
    
    // Log the short URL usage
    await logSecurityEvent({
      user_id: result.user_id,
      event_type: 'short_url_used',
      description: `Short URL used for document capture: ${result.document_type}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      id: result.id,
      shortCode: result.short_code,
      userId: result.user_id,
      documentType: result.document_type as IdDocumentType,
      createdAt: new Date(result.created_at),
      expiresAt: new Date(result.expires_at),
      usedAt: result.used_at ? new Date(result.used_at) : undefined,
      ipAddress: result.ip_address,
      userAgent: result.user_agent
    };
  } catch (error) {
    console.error('Error marking short URL as used:', error);
    return null;
  }
}

/**
 * Generate a short, unique code for the URL
 * @returns Short code
 */
function generateShortCode(): string {
  // Generate a UUID and take the first 8 characters
  const uuid = uuidv4();
  return uuid.substring(0, 8);
}

/**
 * Get the full URL for a short code
 * @param shortCode Short URL code
 * @returns Full URL
 */
export function getFullUrl(shortCode: string): string {
  const baseUrl = process.env.APP_URL || 'http://localhost:3000';
  return `${baseUrl}/capture/${shortCode}`;
}

/**
 * Get active short URLs for a user
 * @param userId User ID
 * @returns List of active short URLs
 */
export async function getActiveShortUrlsForUser(userId: number): Promise<ShortUrlData[]> {
  try {
    const now = new Date();
    
    const results = await db
      .selectFrom('document_capture_urls')
      .selectAll()
      .where('user_id', '=', userId)
      .where('expires_at', '>', now)
      .orderBy('created_at', 'desc')
      .execute();
    
    return results.map(result => ({
      id: result.id,
      shortCode: result.short_code,
      userId: result.user_id,
      documentType: result.document_type as IdDocumentType,
      createdAt: new Date(result.created_at),
      expiresAt: new Date(result.expires_at),
      usedAt: result.used_at ? new Date(result.used_at) : undefined,
      ipAddress: result.ip_address,
      userAgent: result.user_agent
    }));
  } catch (error) {
    console.error('Error getting active short URLs for user:', error);
    return [];
  }
}

/**
 * Delete expired short URLs
 * @returns Number of deleted URLs
 */
export async function cleanupExpiredShortUrls(): Promise<number> {
  try {
    const now = new Date();
    
    const result = await db
      .deleteFrom('document_capture_urls')
      .where('expires_at', '<', now)
      .execute();
    
    return result.length;
  } catch (error) {
    console.error('Error cleaning up expired short URLs:', error);
    return 0;
  }
}