import { db } from './db.server';
import { logSecurityEvent } from './securityAudit.server';
import { VerificationStatus } from './verification.server';
import { initiateBackgroundCheck, getBackgroundCheckStatus, BackgroundCheckType } from './backgroundCheck.server';
import { hasValidIdVerification, getIdVerificationStatus } from './idVerification.server';
import { CredentialType, VerificationRequirement } from '~/types/verification';
import axios from 'axios';

// Re-export the enums for backward compatibility
export { CredentialType, VerificationRequirement };

// Define credential verification configuration
export const credentialRequirements = {
  [CredentialType.IDENTITY]: {
    requirement: VerificationRequirement.REQUIRED,
    displayName: 'Identity Verification',
    description: 'Government-issued photo ID verification'
  },
  [CredentialType.PILOT_LICENSE]: {
    requirement: VerificationRequirement.REQUIRED,
    displayName: 'Pilot License',
    description: 'Valid FAA pilot license'
  },
  [CredentialType.MEDICAL_CERTIFICATE]: {
    requirement: VerificationRequirement.REQUIRED,
    displayName: 'Medical Certificate',
    description: 'Valid FAA medical certificate'
  },
  [CredentialType.BACKGROUND_CHECK]: {
    requirement: VerificationRequirement.REQUIRED,
    displayName: 'Background Check',
    description: 'Criminal background check'
  },
  [CredentialType.FLIGHT_EXPERIENCE]: {
    requirement: VerificationRequirement.RECOMMENDED,
    displayName: 'Flight Experience',
    description: 'Verified flight hours and experience'
  }
};

// Define credential verification result
export interface CredentialVerificationResult {
  success: boolean;
  message: string;
  credentialType: CredentialType;
  status: VerificationStatus;
  verificationDate?: Date;
  details?: any;
}

// Define comprehensive verification status
export interface ComprehensiveVerificationStatus {
  overallStatus: VerificationStatus;
  isFullyVerified: boolean;
  credentials: {
    [key in CredentialType]?: {
      status: VerificationStatus;
      verified: boolean;
      lastChecked?: Date;
      details?: any;
    }
  };
  missingRequiredCredentials: CredentialType[];
  missingRecommendedCredentials: CredentialType[];
}

/**
 * Verify a pilot's FAA license with the FAA database
 * In a production environment, this would call the actual FAA API
 */
export async function verifyPilotLicense(
  userId: number,
  licenseNumber: string,
  licenseType: string,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<CredentialVerificationResult> {
  try {
    // Log the verification attempt
    await logSecurityEvent({
      user_id: userId,
      event_type: 'pilot_license_verification_attempt',
      description: `Attempting to verify pilot license: ${licenseNumber} (${licenseType})`,
      ip_address: ipAddress,
      user_agent: userAgent
    });

    // In a production environment, this would call the FAA API
    // For now, we'll simulate a verification process
    
    // Example API call (commented out)
    /*
    const response = await axios.post('https://api.faa.gov/licenses/verify', {
      license_number: licenseNumber,
      license_type: licenseType
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.FAA_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    const isValid = response.data.status === 'valid';
    const expiryDate = response.data.expiry_date;
    */
    
    // For demo purposes, we'll consider licenses with specific formats as valid
    const isValid = /^[A-Z]{3}[0-9]{6}$/.test(licenseNumber);
    const expiryDate = new Date();
    expiryDate.setFullYear(expiryDate.getFullYear() + 2); // Set expiry to 2 years from now
    
    // Update user's license information
    await db
      .updateTable('users')
      .set({
        license_number: licenseNumber,
        license_type: licenseType,
        license_expiry: expiryDate,
        faa_verification_status: isValid ? VerificationStatus.VERIFIED : VerificationStatus.REJECTED,
        faa_verification_date: new Date()
      })
      .where('id', '=', userId)
      .execute();
    
    // Log the verification result
    await logSecurityEvent({
      user_id: userId,
      event_type: 'pilot_license_verification_result',
      description: `Pilot license verification ${isValid ? 'successful' : 'failed'}: ${licenseNumber} (${licenseType})`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    if (isValid) {
      return {
        success: true,
        message: 'Pilot license verified successfully',
        credentialType: CredentialType.PILOT_LICENSE,
        status: VerificationStatus.VERIFIED,
        verificationDate: new Date(),
        details: {
          licenseNumber,
          licenseType,
          expiryDate
        }
      };
    } else {
      return {
        success: false,
        message: 'Pilot license verification failed. Please check the license number and try again.',
        credentialType: CredentialType.PILOT_LICENSE,
        status: VerificationStatus.REJECTED,
        verificationDate: new Date()
      };
    }
  } catch (error) {
    console.error('Error verifying pilot license:', error);
    
    // Log the error
    await logSecurityEvent({
      user_id: userId,
      event_type: 'pilot_license_verification_error',
      description: `Error verifying pilot license: ${error.message || 'Unknown error'}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: false,
      message: 'An error occurred during pilot license verification',
      credentialType: CredentialType.PILOT_LICENSE,
      status: VerificationStatus.REJECTED,
      verificationDate: new Date()
    };
  }
}

/**
 * Verify a pilot's medical certificate
 */
export async function verifyMedicalCertificate(
  userId: number,
  certificateType: string,
  certificateNumber: string,
  expiryDate: Date,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<CredentialVerificationResult> {
  try {
    // Log the verification attempt
    await logSecurityEvent({
      user_id: userId,
      event_type: 'medical_certificate_verification_attempt',
      description: `Attempting to verify medical certificate: ${certificateNumber} (${certificateType})`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    // In a production environment, this would call the FAA medical certificate API
    // For now, we'll simulate a verification process
    
    // Check if the certificate is expired
    const isExpired = expiryDate < new Date();
    
    // For demo purposes, we'll consider certificates with specific formats as valid
    const isValid = /^[A-Z]{2}[0-9]{6}$/.test(certificateNumber) && !isExpired;
    
    // Update user's medical certificate information
    await db
      .updateTable('users')
      .set({
        medical_certificate_type: certificateType,
        medical_certificate_expiry: expiryDate,
        medical_verified: isValid,
        medical_verification_status: isValid ? VerificationStatus.VERIFIED : VerificationStatus.REJECTED,
        medical_verification_date: new Date()
      })
      .where('id', '=', userId)
      .execute();
    
    // Log the verification result
    await logSecurityEvent({
      user_id: userId,
      event_type: 'medical_certificate_verification_result',
      description: `Medical certificate verification ${isValid ? 'successful' : 'failed'}: ${certificateNumber} (${certificateType})`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    if (isValid) {
      return {
        success: true,
        message: 'Medical certificate verified successfully',
        credentialType: CredentialType.MEDICAL_CERTIFICATE,
        status: VerificationStatus.VERIFIED,
        verificationDate: new Date(),
        details: {
          certificateType,
          certificateNumber,
          expiryDate
        }
      };
    } else {
      return {
        success: false,
        message: isExpired ? 'Medical certificate is expired' : 'Medical certificate verification failed',
        credentialType: CredentialType.MEDICAL_CERTIFICATE,
        status: VerificationStatus.REJECTED,
        verificationDate: new Date()
      };
    }
  } catch (error) {
    console.error('Error verifying medical certificate:', error);
    
    // Log the error
    await logSecurityEvent({
      user_id: userId,
      event_type: 'medical_certificate_verification_error',
      description: `Error verifying medical certificate: ${error.message || 'Unknown error'}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: false,
      message: 'An error occurred during medical certificate verification',
      credentialType: CredentialType.MEDICAL_CERTIFICATE,
      status: VerificationStatus.REJECTED,
      verificationDate: new Date()
    };
  }
}

/**
 * Verify flight experience
 */
export async function verifyFlightExperience(
  userId: number,
  experienceHours: number,
  certifications: string,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<CredentialVerificationResult> {
  try {
    // Log the verification attempt
    await logSecurityEvent({
      user_id: userId,
      event_type: 'flight_experience_verification_attempt',
      description: `Attempting to verify flight experience: ${experienceHours} hours, certifications: ${certifications}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    // Update user's experience information
    await db
      .updateTable('users')
      .set({
        experience_hours: experienceHours,
        certifications,
        experience_verified: true,
        experience_verification_status: VerificationStatus.VERIFIED,
        experience_verification_date: new Date()
      })
      .where('id', '=', userId)
      .execute();
    
    // Log the verification result
    await logSecurityEvent({
      user_id: userId,
      event_type: 'flight_experience_verification_result',
      description: `Flight experience verification successful: ${experienceHours} hours, certifications: ${certifications}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: true,
      message: 'Flight experience verified successfully',
      credentialType: CredentialType.FLIGHT_EXPERIENCE,
      status: VerificationStatus.VERIFIED,
      verificationDate: new Date(),
      details: {
        experienceHours,
        certifications
      }
    };
  } catch (error) {
    console.error('Error verifying flight experience:', error);
    
    // Log the error
    await logSecurityEvent({
      user_id: userId,
      event_type: 'flight_experience_verification_error',
      description: `Error verifying flight experience: ${error.message || 'Unknown error'}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: false,
      message: 'An error occurred during flight experience verification',
      credentialType: CredentialType.FLIGHT_EXPERIENCE,
      status: VerificationStatus.REJECTED,
      verificationDate: new Date()
    };
  }
}

/**
 * Get comprehensive verification status for a pilot
 */
export async function getComprehensiveVerificationStatus(userId: number): Promise<ComprehensiveVerificationStatus> {
  try {
    // Get user information
    const user = await db
      .selectFrom('users')
      .select([
        'identity_verified',
        'identity_verification_status',
        'identity_verification_date',
        'license_number',
        'license_type',
        'license_expiry',
        'faa_verification_status',
        'faa_verification_date',
        'medical_certificate_type',
        'medical_certificate_expiry',
        'medical_verified',
        'medical_verification_status',
        'medical_verification_date',
        'background_check_status',
        'background_check_date',
        'experience_verified',
        'experience_verification_status',
        'experience_verification_date',
        'experience_hours',
        'certifications'
      ])
      .where('id', '=', userId)
      .executeTakeFirst();
    
    if (!user) {
      return {
        overallStatus: VerificationStatus.PENDING,
        isFullyVerified: false,
        credentials: {},
        missingRequiredCredentials: Object.keys(credentialRequirements)
          .filter(key => credentialRequirements[key].requirement === VerificationRequirement.REQUIRED)
          .map(key => key as CredentialType),
        missingRecommendedCredentials: Object.keys(credentialRequirements)
          .filter(key => credentialRequirements[key].requirement === VerificationRequirement.RECOMMENDED)
          .map(key => key as CredentialType)
      };
    }
    
    // Get ID verification status
    const idStatus = await getIdVerificationStatus(userId);
    
    // Get background check status
    const backgroundCheckStatus = await getBackgroundCheckStatus(userId);
    
    // Compile comprehensive status
    const credentials = {
      [CredentialType.IDENTITY]: {
        status: user.identity_verification_status as VerificationStatus || VerificationStatus.PENDING,
        verified: user.identity_verified || false,
        lastChecked: user.identity_verification_date ? new Date(user.identity_verification_date) : undefined,
        details: idStatus.documents
      },
      [CredentialType.PILOT_LICENSE]: {
        status: user.faa_verification_status as VerificationStatus || VerificationStatus.PENDING,
        verified: user.faa_verification_status === VerificationStatus.VERIFIED,
        lastChecked: user.faa_verification_date ? new Date(user.faa_verification_date) : undefined,
        details: {
          licenseNumber: user.license_number,
          licenseType: user.license_type,
          expiryDate: user.license_expiry ? new Date(user.license_expiry) : undefined
        }
      },
      [CredentialType.MEDICAL_CERTIFICATE]: {
        status: user.medical_verification_status as VerificationStatus || VerificationStatus.PENDING,
        verified: user.medical_verified || false,
        lastChecked: user.medical_verification_date ? new Date(user.medical_verification_date) : undefined,
        details: {
          certificateType: user.medical_certificate_type,
          expiryDate: user.medical_certificate_expiry ? new Date(user.medical_certificate_expiry) : undefined
        }
      },
      [CredentialType.BACKGROUND_CHECK]: {
        status: user.background_check_status as VerificationStatus || VerificationStatus.PENDING,
        verified: user.background_check_status === VerificationStatus.VERIFIED,
        lastChecked: user.background_check_date ? new Date(user.background_check_date) : undefined,
        details: {
          reportId: backgroundCheckStatus.reportId,
          reportUrl: backgroundCheckStatus.reportUrl
        }
      },
      [CredentialType.FLIGHT_EXPERIENCE]: {
        status: user.experience_verification_status as VerificationStatus || VerificationStatus.PENDING,
        verified: user.experience_verified || false,
        lastChecked: user.experience_verification_date ? new Date(user.experience_verification_date) : undefined,
        details: {
          experienceHours: user.experience_hours,
          certifications: user.certifications
        }
      }
    };
    
    // Determine missing required credentials
    const missingRequiredCredentials = Object.keys(credentialRequirements)
      .filter(key => {
        const requirement = credentialRequirements[key].requirement;
        const credential = credentials[key as CredentialType];
        return requirement === VerificationRequirement.REQUIRED && 
               (!credential || !credential.verified || credential.status !== VerificationStatus.VERIFIED);
      })
      .map(key => key as CredentialType);
    
    // Determine missing recommended credentials
    const missingRecommendedCredentials = Object.keys(credentialRequirements)
      .filter(key => {
        const requirement = credentialRequirements[key].requirement;
        const credential = credentials[key as CredentialType];
        return requirement === VerificationRequirement.RECOMMENDED && 
               (!credential || !credential.verified || credential.status !== VerificationStatus.VERIFIED);
      })
      .map(key => key as CredentialType);
    
    // Determine overall status
    let overallStatus = VerificationStatus.VERIFIED;
    
    if (missingRequiredCredentials.length > 0) {
      overallStatus = VerificationStatus.REJECTED;
    } else if (Object.values(credentials).some(c => c.status === VerificationStatus.IN_PROGRESS)) {
      overallStatus = VerificationStatus.IN_PROGRESS;
    } else if (Object.values(credentials).some(c => c.status === VerificationStatus.PENDING)) {
      overallStatus = VerificationStatus.PENDING;
    }
    
    return {
      overallStatus,
      isFullyVerified: missingRequiredCredentials.length === 0,
      credentials,
      missingRequiredCredentials,
      missingRecommendedCredentials
    };
  } catch (error) {
    console.error('Error getting comprehensive verification status:', error);
    return {
      overallStatus: VerificationStatus.PENDING,
      isFullyVerified: false,
      credentials: {},
      missingRequiredCredentials: Object.keys(credentialRequirements)
        .filter(key => credentialRequirements[key].requirement === VerificationRequirement.REQUIRED)
        .map(key => key as CredentialType),
      missingRecommendedCredentials: Object.keys(credentialRequirements)
        .filter(key => credentialRequirements[key].requirement === VerificationRequirement.RECOMMENDED)
        .map(key => key as CredentialType)
    };
  }
}

/**
 * Check if a pilot is fully verified and eligible to fly
 */
export async function isPilotVerifiedToFly(userId: number): Promise<{
  verified: boolean;
  status: VerificationStatus;
  missingCredentials: CredentialType[];
  message: string;
}> {
  const status = await getComprehensiveVerificationStatus(userId);
  
  if (status.isFullyVerified) {
    return {
      verified: true,
      status: VerificationStatus.VERIFIED,
      missingCredentials: [],
      message: 'Pilot is fully verified and eligible to fly'
    };
  } else {
    return {
      verified: false,
      status: status.overallStatus,
      missingCredentials: status.missingRequiredCredentials,
      message: `Pilot is missing required credentials: ${status.missingRequiredCredentials.map(c => credentialRequirements[c].displayName).join(', ')}`
    };
  }
}