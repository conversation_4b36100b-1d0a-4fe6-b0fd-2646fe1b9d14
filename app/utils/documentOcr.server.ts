import { db } from './db.server';
import { logSecurityEvent } from './securityAudit.server';
import { IdDocumentType } from './idVerification.server';

// Interface for OCR extracted data from ID documents
export interface OcrExtractedData {
  documentType?: IdDocumentType;
  fullName?: string;
  documentNumber?: string;
  dateOfBirth?: Date;
  expiryDate?: Date;
  issueDate?: Date;
  issuingCountry?: string;
  issuingAuthority?: string;
  address?: string;
  pilotLicenseType?: string;
  pilotLicenseRatings?: string[];
  confidence: number;
  rawText: string;
}

/**
 * Process an ID document image with OCR and extract data
 * @param fileBuffer File buffer
 * @param fileType File MIME type
 * @param expectedDocumentType Expected document type (optional)
 * @returns Extracted data from the document
 */
export async function processDocumentWithOcr(
  fileBuffer: Buffer,
  fileType: string,
  expectedDocumentType?: IdDocumentType
): Promise<OcrExtractedData> {
  // Check if file type is an image that can be processed with OCR
  const ocrCompatibleTypes = ['image/jpeg', 'image/png', 'image/gif'];

  if (!ocrCompatibleTypes.includes(fileType)) {
    console.log(`File type ${fileType} not compatible with OCR`);
    return { confidence: 0, rawText: '' };
  }

  try {
    // Extract text from image using OCR
    const extractedText = await extractTextFromImage(fileBuffer);

    // Determine document type if not provided
    const detectedType = expectedDocumentType || detectDocumentType(extractedText);

    // Extract structured data from OCR text based on document type
    const extractedData = extractDataFromOcrText(extractedText, detectedType);

    console.log('OCR extracted data:', extractedData);
    return extractedData;
  } catch (error) {
    console.error('Error processing document with OCR:', error);
    return { confidence: 0, rawText: '' };
  }
}

/**
 * Extract text from an image using OCR
 * @param imageBuffer Image buffer
 * @returns Extracted text
 */
async function extractTextFromImage(imageBuffer: Buffer): Promise<string> {
  // In a real implementation, this would use a proper OCR library like Tesseract.js
  // or a cloud OCR service like Google Cloud Vision, AWS Textract, etc.
  
  // For this example, we'll simulate OCR with a mock implementation
  console.log('Simulating OCR text extraction from image');

  // In a production environment, you would replace this with actual OCR API calls
  // Example with Google Cloud Vision:
  /*
  const vision = require('@google-cloud/vision');
  const client = new vision.ImageAnnotatorClient();
  const [result] = await client.textDetection(imageBuffer);
  return result.fullTextAnnotation.text;
  */

  // Return a mock result for demonstration purposes
  // This simulates different document types based on the first few bytes of the image
  const firstByte = imageBuffer[0] || 0;
  
  if (firstByte % 3 === 0) {
    // Simulate passport
    return "PASSPORT\nUnited States of America\nPassport No: *********\nSurname: SMITH\nGiven Names: JOHN MICHAEL\nNationality: UNITED STATES OF AMERICA\nDate of Birth: 15 JAN 1985\nPlace of Birth: NEW YORK, USA\nDate of Issue: 01 JUN 2018\nDate of Expiry: 01 JUN 2028\nAuthority: UNITED STATES DEPARTMENT OF STATE";
  } else if (firstByte % 3 === 1) {
    // Simulate driver's license
    return "DRIVER LICENSE\nUSA\nDL NO: ********\nCLASS: C\nEXP: 05-12-2025\nISS: 05-12-2020\nDOB: 01-15-1985\nNAME: SMITH, JOHN M\nADDRESS: 123 MAIN ST, ANYTOWN, CA 90210\nSEX: M HGT: 5-10 EYES: BRN\nRESTRICTIONS: NONE";
  } else {
    // Simulate pilot license
    return "FEDERAL AVIATION ADMINISTRATION\nCERTIFICATE OF PILOT PROFICIENCY\nPILOT LICENSE\nNAME: SMITH, JOHN M\nCERTIFICATE NO: *********\nDOB: 01/15/1985\nISSUE DATE: 06/01/2019\nRATINGS: COMMERCIAL PILOT\nAIRPLANE SINGLE ENGINE LAND\nINSTRUMENT AIRPLANE\nEXPIRATION: 06/01/2024";
  }
}

/**
 * Detect document type from OCR text
 * @param text OCR extracted text
 * @returns Detected document type
 */
function detectDocumentType(text: string): IdDocumentType {
  const lowerText = text.toLowerCase();
  
  if (lowerText.includes('passport')) {
    return IdDocumentType.PASSPORT;
  } else if (lowerText.includes('driver') && (lowerText.includes('license') || lowerText.includes('licence'))) {
    return IdDocumentType.DRIVERS_LICENSE;
  } else if (lowerText.includes('pilot') && lowerText.includes('license')) {
    return IdDocumentType.PILOT_LICENSE;
  } else {
    return IdDocumentType.GOVERNMENT_ID;
  }
}

/**
 * Extract structured data from OCR text
 * @param text OCR extracted text
 * @param documentType Document type
 * @returns Structured data extracted from the text
 */
function extractDataFromOcrText(text: string, documentType: IdDocumentType): OcrExtractedData {
  const result: OcrExtractedData = {
    documentType,
    confidence: 0.8, // Default confidence level
    rawText: text
  };

  // Extract data based on document type
  switch (documentType) {
    case IdDocumentType.PASSPORT:
      extractPassportData(text, result);
      break;
    case IdDocumentType.DRIVERS_LICENSE:
      extractDriversLicenseData(text, result);
      break;
    case IdDocumentType.PILOT_LICENSE:
      extractPilotLicenseData(text, result);
      break;
    default:
      extractGenericIdData(text, result);
  }

  // Calculate confidence based on how many fields were extracted
  const extractedFieldCount = Object.keys(result).filter(key => 
    key !== 'confidence' && key !== 'rawText' && result[key] !== undefined
  ).length;
  
  // More fields extracted = higher confidence
  result.confidence = Math.min(0.5 + (extractedFieldCount * 0.1), 0.95);
  
  return result;
}

/**
 * Extract data from passport OCR text
 */
function extractPassportData(text: string, result: OcrExtractedData): void {
  // Extract passport number
  const passportNumberMatch = text.match(/Passport No:?\s*([A-Z0-9]+)/i);
  if (passportNumberMatch && passportNumberMatch[1]) {
    result.documentNumber = passportNumberMatch[1].trim();
  }

  // Extract name
  const nameMatch = text.match(/Given Names:?\s*([A-Z\s]+)/i) || text.match(/Name:?\s*([A-Z\s]+)/i);
  const surnameMatch = text.match(/Surname:?\s*([A-Z\s]+)/i);
  
  if (nameMatch && nameMatch[1]) {
    result.fullName = nameMatch[1].trim();
    if (surnameMatch && surnameMatch[1]) {
      result.fullName = `${surnameMatch[1].trim()} ${result.fullName}`;
    }
  }

  // Extract date of birth
  const dobMatch = text.match(/Date of Birth:?\s*(\d{1,2}\s*[A-Z]{3}\s*\d{4})/i) || 
                   text.match(/DOB:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (dobMatch && dobMatch[1]) {
    try {
      result.dateOfBirth = new Date(dobMatch[1]);
      // Check if date is valid
      if (isNaN(result.dateOfBirth.getTime())) {
        delete result.dateOfBirth;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract expiry date
  const expiryMatch = text.match(/Date of Expiry:?\s*(\d{1,2}\s*[A-Z]{3}\s*\d{4})/i) || 
                      text.match(/Expiry:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (expiryMatch && expiryMatch[1]) {
    try {
      result.expiryDate = new Date(expiryMatch[1]);
      // Check if date is valid
      if (isNaN(result.expiryDate.getTime())) {
        delete result.expiryDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract issuing country
  const countryMatch = text.match(/Nationality:?\s*([A-Z\s]+)/i);
  if (countryMatch && countryMatch[1]) {
    result.issuingCountry = countryMatch[1].trim();
  }

  // Extract issuing authority
  const authorityMatch = text.match(/Authority:?\s*([A-Z\s]+)/i);
  if (authorityMatch && authorityMatch[1]) {
    result.issuingAuthority = authorityMatch[1].trim();
  }
}

/**
 * Extract data from driver's license OCR text
 */
function extractDriversLicenseData(text: string, result: OcrExtractedData): void {
  // Extract license number
  const licenseNumberMatch = text.match(/DL(?:\s+|\s*NO:?\s*)([A-Z0-9]+)/i);
  if (licenseNumberMatch && licenseNumberMatch[1]) {
    result.documentNumber = licenseNumberMatch[1].trim();
  }

  // Extract name
  const nameMatch = text.match(/NAME:?\s*([A-Z\s,]+)/i);
  if (nameMatch && nameMatch[1]) {
    result.fullName = nameMatch[1].trim();
  }

  // Extract date of birth
  const dobMatch = text.match(/DOB:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (dobMatch && dobMatch[1]) {
    try {
      result.dateOfBirth = new Date(dobMatch[1]);
      // Check if date is valid
      if (isNaN(result.dateOfBirth.getTime())) {
        delete result.dateOfBirth;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract expiry date
  const expiryMatch = text.match(/EXP(?:IRES|IRATION)?:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (expiryMatch && expiryMatch[1]) {
    try {
      result.expiryDate = new Date(expiryMatch[1]);
      // Check if date is valid
      if (isNaN(result.expiryDate.getTime())) {
        delete result.expiryDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract issue date
  const issueMatch = text.match(/ISS(?:UED)?:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (issueMatch && issueMatch[1]) {
    try {
      result.issueDate = new Date(issueMatch[1]);
      // Check if date is valid
      if (isNaN(result.issueDate.getTime())) {
        delete result.issueDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract address
  const addressMatch = text.match(/ADDRESS:?\s*([A-Z0-9\s,.-]+)/i);
  if (addressMatch && addressMatch[1]) {
    result.address = addressMatch[1].trim();
  }
}

/**
 * Extract data from pilot license OCR text
 */
function extractPilotLicenseData(text: string, result: OcrExtractedData): void {
  // Extract license number
  const licenseNumberMatch = text.match(/CERTIFICATE NO:?\s*([A-Z0-9]+)/i);
  if (licenseNumberMatch && licenseNumberMatch[1]) {
    result.documentNumber = licenseNumberMatch[1].trim();
  }

  // Extract name
  const nameMatch = text.match(/NAME:?\s*([A-Z\s,]+)/i);
  if (nameMatch && nameMatch[1]) {
    result.fullName = nameMatch[1].trim();
  }

  // Extract date of birth
  const dobMatch = text.match(/DOB:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (dobMatch && dobMatch[1]) {
    try {
      result.dateOfBirth = new Date(dobMatch[1]);
      // Check if date is valid
      if (isNaN(result.dateOfBirth.getTime())) {
        delete result.dateOfBirth;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract expiry date
  const expiryMatch = text.match(/EXPIRATION:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (expiryMatch && expiryMatch[1]) {
    try {
      result.expiryDate = new Date(expiryMatch[1]);
      // Check if date is valid
      if (isNaN(result.expiryDate.getTime())) {
        delete result.expiryDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract issue date
  const issueMatch = text.match(/ISSUE DATE:?\s*(\d{1,2}[-/]\d{1,2}[-/]\d{2,4})/i);
  if (issueMatch && issueMatch[1]) {
    try {
      result.issueDate = new Date(issueMatch[1]);
      // Check if date is valid
      if (isNaN(result.issueDate.getTime())) {
        delete result.issueDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract license type
  const typeMatch = text.match(/RATINGS:?\s*([A-Z\s]+)/i);
  if (typeMatch && typeMatch[1]) {
    result.pilotLicenseType = typeMatch[1].trim().split('\n')[0];
    
    // Extract ratings
    const ratingsText = text.substring(text.indexOf('RATINGS:') + 8);
    const ratings = ratingsText.split('\n')
      .map(line => line.trim())
      .filter(line => line.length > 0 && !line.includes('EXPIRATION'));
    
    if (ratings.length > 0) {
      result.pilotLicenseRatings = ratings;
    }
  }
}

/**
 * Extract data from generic ID document OCR text
 */
function extractGenericIdData(text: string, result: OcrExtractedData): void {
  // Extract document number (look for ID, No, #, etc.)
  const idNumberMatch = text.match(/(?:ID|No|Number|#):?\s*([A-Z0-9]+)/i);
  if (idNumberMatch && idNumberMatch[1]) {
    result.documentNumber = idNumberMatch[1].trim();
  }

  // Extract name (look for Name, Full Name, etc.)
  const nameMatch = text.match(/(?:Name|Full Name):?\s*([A-Z\s]+)/i);
  if (nameMatch && nameMatch[1]) {
    result.fullName = nameMatch[1].trim();
  }

  // Extract date of birth (look for DOB, Birth Date, etc.)
  const dobMatch = text.match(/(?:DOB|Date of Birth|Birth Date):?\s*(\d{1,2}[-/\s]\d{1,2}[-/\s]\d{2,4})/i);
  if (dobMatch && dobMatch[1]) {
    try {
      result.dateOfBirth = new Date(dobMatch[1]);
      // Check if date is valid
      if (isNaN(result.dateOfBirth.getTime())) {
        delete result.dateOfBirth;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract expiry date (look for Expiry, Expires, Valid Until, etc.)
  const expiryMatch = text.match(/(?:Expiry|Expires|Valid Until):?\s*(\d{1,2}[-/\s]\d{1,2}[-/\s]\d{2,4})/i);
  if (expiryMatch && expiryMatch[1]) {
    try {
      result.expiryDate = new Date(expiryMatch[1]);
      // Check if date is valid
      if (isNaN(result.expiryDate.getTime())) {
        delete result.expiryDate;
      }
    } catch (e) {
      // Invalid date format
    }
  }

  // Extract address (look for Address, Residence, etc.)
  const addressMatch = text.match(/(?:Address|Residence):?\s*([A-Z0-9\s,.-]+)/i);
  if (addressMatch && addressMatch[1]) {
    result.address = addressMatch[1].trim();
  }
}

/**
 * Save OCR extracted data to the database
 * @param documentId ID of the document in the database
 * @param extractedData OCR extracted data
 */
export async function saveOcrExtractedData(documentId: number, extractedData: OcrExtractedData): Promise<void> {
  try {
    await db
      .updateTable('documents')
      .set({
        ocr_data: JSON.stringify(extractedData),
        ocr_confidence: extractedData.confidence,
        ocr_processed_at: new Date()
      })
      .where('id', '=', documentId)
      .execute();
    
    console.log(`OCR data saved for document ${documentId}`);
  } catch (error) {
    console.error('Error saving OCR data:', error);
    throw error;
  }
}

/**
 * Get OCR extracted data for a document
 * @param documentId ID of the document in the database
 * @returns OCR extracted data
 */
export async function getOcrExtractedData(documentId: number): Promise<OcrExtractedData | null> {
  try {
    const result = await db
      .selectFrom('documents')
      .select(['ocr_data', 'ocr_confidence', 'ocr_processed_at'])
      .where('id', '=', documentId)
      .executeTakeFirst();
    
    if (!result || !result.ocr_data) {
      return null;
    }
    
    return JSON.parse(result.ocr_data);
  } catch (error) {
    console.error('Error getting OCR data:', error);
    return null;
  }
}