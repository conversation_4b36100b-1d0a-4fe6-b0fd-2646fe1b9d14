import { db } from './db.server';
import { createCookieSessionStorage, redirect } from "@remix-run/node";

// Define User type
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'pilot' | 'owner' | 'admin';
  rating?: number;
  experienceHours?: number;
  certifications?: string;
  createdAt: Date;
  updatedAt: Date;
  accountType?: 'individual' | 'business' | 'business_employee' | 'service_provider';
  businessId?: number;
  isBusinessAdmin?: boolean;
  serviceProviderType?: string;
}

/**
 * Get a user by ID
 * @param id User ID
 * @returns User object or null if not found
 */
export async function getUserById(id: number): Promise<User | null> {
  const user = await db
    .selectFrom('users')
    .where('id', '=', id)
    .selectAll()
    .executeTakeFirst();

  if (!user) {
    return null;
  }

  // Map database column names to camelCase for the interface
  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role as 'pilot' | 'owner' | 'admin',
    rating: user.rating,
    experienceHours: user.experience_hours,
    certifications: user.certifications,
    createdAt: new Date(user.created_at),
    updatedAt: new Date(user.updated_at),
    accountType: user.account_type,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    serviceProviderType: user.service_provider_type
  };
}

/**
 * Search for pilots by name, rating, or experience
 * @param query Search query
 * @param limit Maximum number of results to return
 * @returns Array of matching pilots
 */
export async function searchPilots(query: string, limit: number = 10): Promise<User[]> {
  // If query is empty, return an empty array
  if (!query.trim()) {
    return [];
  }

  const pilots = await db
    .selectFrom('users')
    .where('role', '=', 'pilot')
    .where(eb => eb.or([
      eb('name', 'like', `%${query}%`),
      eb('email', 'like', `%${query}%`),
      eb('certifications', 'like', `%${query}%`)
    ]))
    .limit(limit)
    .selectAll()
    .execute();

  // Map database column names to camelCase for the interface
  return pilots.map(pilot => ({
    id: pilot.id,
    email: pilot.email,
    name: pilot.name,
    role: pilot.role as 'pilot' | 'owner' | 'admin',
    rating: pilot.rating,
    experienceHours: pilot.experience_hours,
    certifications: pilot.certifications,
    createdAt: new Date(pilot.created_at),
    updatedAt: new Date(pilot.updated_at),
    accountType: pilot.account_type,
    businessId: pilot.business_id,
    isBusinessAdmin: pilot.is_business_admin,
    serviceProviderType: pilot.service_provider_type
  }));
}

/**
 * Get all pilots
 * @param limit Maximum number of results to return
 * @returns Array of pilots
 */
export async function getAllPilots(limit: number = 100): Promise<User[]> {
  const pilots = await db
    .selectFrom('users')
    .where('role', '=', 'pilot')
    .limit(limit)
    .selectAll()
    .orderBy('name')
    .execute();

  // Map database column names to camelCase for the interface
  return pilots.map(pilot => ({
    id: pilot.id,
    email: pilot.email,
    name: pilot.name,
    role: pilot.role as 'pilot' | 'owner' | 'admin',
    rating: pilot.rating,
    experienceHours: pilot.experience_hours,
    certifications: pilot.certifications,
    createdAt: new Date(pilot.created_at),
    updatedAt: new Date(pilot.updated_at),
    accountType: pilot.account_type,
    businessId: pilot.business_id,
    isBusinessAdmin: pilot.is_business_admin,
    serviceProviderType: pilot.service_provider_type
  }));
}

/**
 * Get all aircraft owners
 * @param limit Maximum number of results to return
 * @returns Array of owners
 */
export async function getAllOwners(limit: number = 100): Promise<User[]> {
  const owners = await db
    .selectFrom('users')
    .where('role', '=', 'owner')
    .limit(limit)
    .selectAll()
    .orderBy('name')
    .execute();

  // Map database column names to camelCase for the interface
  return owners.map(owner => ({
    id: owner.id,
    email: owner.email,
    name: owner.name,
    role: owner.role as 'pilot' | 'owner' | 'admin',
    createdAt: new Date(owner.created_at),
    updatedAt: new Date(owner.updated_at),
    accountType: owner.account_type,
    businessId: owner.business_id,
    isBusinessAdmin: owner.is_business_admin,
    serviceProviderType: owner.service_provider_type
  }));
}

/**
 * Get all users (for admin dashboard)
 * @param limit Maximum number of results to return
 * @returns Array of all users
 */
export async function getAllUsers(limit: number = 500): Promise<User[]> {
  const users = await db
    .selectFrom('users')
    .limit(limit)
    .selectAll()
    .orderBy('name')
    .execute();

  // Map database column names to camelCase for the interface
  return users.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role as 'pilot' | 'owner' | 'admin',
    rating: user.rating,
    experienceHours: user.experience_hours,
    certifications: user.certifications,
    createdAt: new Date(user.created_at),
    updatedAt: new Date(user.updated_at),
    accountType: user.account_type,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    serviceProviderType: user.service_provider_type
  }));
}

/**
 * Check if a user has admin privileges
 * @param userId User ID to check
 * @returns Boolean indicating if user is an admin
 */
export async function isUserAdmin(userId: number): Promise<boolean> {
  const user = await getUserById(userId);
  return user?.role === 'admin';
}
