import axios from 'axios';
import { pool, query } from './db.server';
import { TrackingStatus, FlightStatus } from '~/types/verification';

/**
 * Flight Tracking Module
 * 
 * This module provides functionality for tracking aircraft using ADS-B data.
 * It integrates with the AviationStack API to fetch real-time flight information.
 * 
 * To use this module, you need to:
 * 1. Set up an AviationStack API key in your .env file as AVIATIONSTACK_API_KEY
 * 2. Initialize tracking for a job using initializeTracking()
 * 3. Update tracking status using updateTrackingStatus()
 * 4. Get tracking data using getTrackingData()
 * 
 * If the AviationStack API key is not available or the API call fails,
 * the module will fall back to generating mock data for testing purposes.
 */

// Re-export the enums for backward compatibility
export { TrackingStatus, FlightStatus };

// Define position update interface
interface PositionUpdate {
  latitude: number;
  longitude: number;
  altitude: number;
  groundSpeed: number;
  heading: number;
  timestamp: Date;
}

// Define flight data interface
interface FlightData {
  flightId: string;
  registration: string;
  aircraftType: string;
  origin: string;
  destination: string;
  status: FlightStatus;
  departureTime?: Date;
  arrivalTime?: Date;
  estimatedArrivalTime?: Date;
  positions: PositionUpdate[];
}

/**
 * Initialize tracking for a job
 * @param jobId The job ID to track
 * @param registration The aircraft registration number
 * @param aircraftType The type of aircraft
 * @param origin The origin airport code
 * @param destination The destination airport code
 */
export async function initializeTracking(
  jobId: number,
  registration: string,
  aircraftType: string,
  origin: string,
  destination: string
) {
  try {
    // Check if tracking already exists for this job
    const existingTracking = await query(
      'SELECT * FROM flight_tracking WHERE job_id = $1',
      [jobId]
    );

    if (existingTracking.rows.length > 0) {
      return {
        success: false,
        message: 'Tracking already initialized for this job',
        trackingId: existingTracking.rows[0].id
      };
    }

    // Create a new tracking record
    const result = await query(
      `INSERT INTO flight_tracking (
        job_id, 
        registration, 
        aircraft_type, 
        origin, 
        destination, 
        status, 
        created_at
      ) VALUES ($1, $2, $3, $4, $5, $6, NOW()) RETURNING *`,
      [
        jobId,
        registration,
        aircraftType,
        origin,
        destination,
        TrackingStatus.NOT_STARTED
      ]
    );

    return {
      success: true,
      message: 'Tracking initialized successfully',
      tracking: result.rows[0]
    };
  } catch (error) {
    console.error('Error initializing tracking:', error);
    return {
      success: false,
      message: 'Failed to initialize tracking'
    };
  }
}

/**
 * Update tracking status for a job
 * @param trackingId The tracking ID to update
 * @param status The new tracking status
 */
export async function updateTrackingStatus(trackingId: number, status: TrackingStatus) {
  try {
    const result = await query(
      `UPDATE flight_tracking 
       SET status = $1, 
           updated_at = NOW() 
       WHERE id = $2 
       RETURNING *`,
      [status, trackingId]
    );

    if (result.rows.length === 0) {
      return {
        success: false,
        message: 'Tracking record not found'
      };
    }

    return {
      success: true,
      message: 'Tracking status updated successfully',
      tracking: result.rows[0]
    };
  } catch (error) {
    console.error('Error updating tracking status:', error);
    return {
      success: false,
      message: 'Failed to update tracking status'
    };
  }
}

/**
 * Helper function to make API requests with retries
 */
async function fetchWithRetry(url: string, options: RequestInit, maxRetries = 5, initialDelay = 2000) {
  let retries = 0;
  let delay = initialDelay;

  while (retries < maxRetries) {
    try {
      const response = await fetch(url, options);

      // If response is successful, return it
      if (response.ok) {
        return response;
      }

      // If we get a 502 error or other server errors (5xx), retry
      if (response.status >= 500 && response.status < 600) {
        retries++;
        if (retries >= maxRetries) {
          throw new Error(`API request to ${url} failed with status ${response.status} after ${maxRetries} retries`);
        }

        console.log(`Retry ${retries}/${maxRetries} for API request to ${url} due to ${response.status} error. Waiting ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
        continue;
      }

      // For other errors, throw immediately
      throw new Error(`API request to ${url} failed with status ${response.status}`);
    } catch (error) {
      // If it's a network error (not a response error), retry
      if (!(error instanceof Error) || !error.message.includes('API request to')) {
        retries++;
        if (retries >= maxRetries) {
          if (error instanceof Error) {
            throw new Error(`Network error when requesting ${url}: ${error.message}`);
          } else {
            throw new Error(`Unknown network error when requesting ${url}`);
          }
        }

        console.log(`Retry ${retries}/${maxRetries} for API request to ${url} due to network error. Waiting ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        delay *= 2; // Exponential backoff
        continue;
      }

      throw error;
    }
  }

  throw new Error(`API request to ${url} failed after ${maxRetries} retries`);
}

/**
 * Get ADS-B data for an aircraft
 * Real implementation using AviationStack API or other ADS-B data providers
 * @param registration The aircraft registration number
 */
export async function getADSBData(registration: string): Promise<FlightData | null> {
  try {
    // Check if we have an API key
    const apiKey = process.env.AVIATIONSTACK_API_KEY;
    if (!apiKey) {
      console.error('AVIATIONSTACK_API_KEY is not defined in environment variables');
      // Fall back to mock data if no API key is available
      return getMockADSBData(registration);
    }

    // Make API request to AviationStack
    const response = await fetchWithRetry(
      `http://api.aviationstack.com/v1/flights?access_key=${apiKey}&flight_icao=${registration}`,
      {
        headers: {
          'Accept': 'application/json'
        }
      }
    );

    const data = await response.json();

    // Check if the response has the expected structure
    if (!data.data || !Array.isArray(data.data) || data.data.length === 0) {
      console.warn('No flight data found for registration:', registration);
      // Fall back to mock data if no flight data is found
      return getMockADSBData(registration);
    }

    // Get the first flight (most relevant)
    const flight = data.data[0];

    // Extract flight information
    const flightData: FlightData = {
      flightId: flight.flight.iata || flight.flight.icao || `FP${Math.floor(1000 + Math.random() * 9000)}`,
      registration: registration,
      aircraftType: flight.aircraft?.icao || 'Unknown',
      origin: flight.departure?.iata || 'Unknown',
      destination: flight.arrival?.iata || 'Unknown',
      status: mapAviationStackStatusToFlightStatus(flight.flight_status),
      departureTime: flight.departure?.scheduled ? new Date(flight.departure.scheduled) : undefined,
      arrivalTime: flight.arrival?.actual ? new Date(flight.arrival.actual) : undefined,
      estimatedArrivalTime: flight.arrival?.estimated ? new Date(flight.arrival.estimated) : undefined,
      positions: []
    };

    // If we have live position data
    if (flight.live) {
      flightData.positions = [{
        latitude: flight.live.latitude,
        longitude: flight.live.longitude,
        altitude: flight.live.altitude,
        groundSpeed: flight.live.speed_horizontal,
        heading: flight.live.heading,
        timestamp: new Date()
      }];
    } else {
      // If no live data, we need to use a different API or generate positions
      // For now, we'll generate mock positions based on departure and arrival
      flightData.positions = generateMockPositions(
        { lat: flight.departure?.latitude || 0, lng: flight.departure?.longitude || 0 },
        { lat: flight.arrival?.latitude || 0, lng: flight.arrival?.longitude || 0 },
        flightData.departureTime || new Date(Date.now() - 2 * 60 * 60 * 1000),
        flightData.estimatedArrivalTime || new Date(Date.now() + 1 * 60 * 60 * 1000)
      );
    }

    return flightData;
  } catch (error) {
    console.error('Error fetching ADS-B data:', error);
    // Fall back to mock data if there's an error
    return getMockADSBData(registration);
  }
}

/**
 * Map AviationStack flight status to our FlightStatus enum
 */
function mapAviationStackStatusToFlightStatus(status: string): FlightStatus {
  switch (status?.toLowerCase()) {
    case 'scheduled':
      return FlightStatus.SCHEDULED;
    case 'active':
      return FlightStatus.EN_ROUTE;
    case 'landed':
      return FlightStatus.LANDED;
    case 'diverted':
      return FlightStatus.DIVERTED;
    case 'cancelled':
      return FlightStatus.CANCELLED;
    default:
      return FlightStatus.SCHEDULED;
  }
}

/**
 * Generate mock positions between two points
 */
function generateMockPositions(
  origin: { lat: number, lng: number },
  destination: { lat: number, lng: number },
  departureTime: Date,
  arrivalTime: Date
): PositionUpdate[] {
  const positions: PositionUpdate[] = [];
  const numPositions = 10;
  const now = new Date();

  // If origin or destination coordinates are invalid, use default values
  if (!origin.lat || !origin.lng) {
    origin = { lat: 33.9416, lng: -118.4085 }; // Default to LAX
  }

  if (!destination.lat || !destination.lng) {
    destination = { lat: 37.6213, lng: -122.3790 }; // Default to SFO
  }

  for (let i = 0; i < numPositions; i++) {
    const ratio = i / (numPositions - 1);
    const lat = origin.lat + ratio * (destination.lat - origin.lat);
    const lng = origin.lng + ratio * (destination.lng - origin.lng);

    // Calculate timestamp based on departure and arrival times
    const timestamp = new Date(
      departureTime.getTime() + ratio * (arrivalTime.getTime() - departureTime.getTime())
    );

    // Only include positions up to current time
    if (timestamp <= now) {
      positions.push({
        latitude: lat,
        longitude: lng,
        altitude: 30000 - Math.random() * 2000, // Around 30,000 ft with some variation
        groundSpeed: 450 + Math.random() * 50, // Around 450 knots with some variation
        heading: calculateHeading(origin.lat, origin.lng, destination.lat, destination.lng),
        timestamp
      });
    }
  }

  return positions;
}

/**
 * Calculate heading between two points
 */
function calculateHeading(lat1: number, lng1: number, lat2: number, lng2: number): number {
  const dLng = (lng2 - lng1) * Math.PI / 180;
  lat1 = lat1 * Math.PI / 180;
  lat2 = lat2 * Math.PI / 180;

  const y = Math.sin(dLng) * Math.cos(lat2);
  const x = Math.cos(lat1) * Math.sin(lat2) - Math.sin(lat1) * Math.cos(lat2) * Math.cos(dLng);
  let heading = Math.atan2(y, x) * 180 / Math.PI;

  // Normalize to 0-360
  heading = (heading + 360) % 360;

  return heading;
}

/**
 * Get mock ADS-B data for testing or when API is unavailable
 */
function getMockADSBData(registration: string): FlightData {
  const now = new Date();
  const departureTime = new Date(now.getTime() - 2 * 60 * 60 * 1000); // 2 hours ago
  const estimatedArrivalTime = new Date(now.getTime() + 1 * 60 * 60 * 1000); // 1 hour from now

  // Generate random positions along a path
  const positions: PositionUpdate[] = [];
  const numPositions = 10;

  // Define start and end coordinates (example: LAX to SFO)
  const startLat = 33.9416;
  const startLng = -118.4085;
  const endLat = 37.6213;
  const endLng = -122.3790;

  for (let i = 0; i < numPositions; i++) {
    const ratio = i / (numPositions - 1);
    const lat = startLat + ratio * (endLat - startLat);
    const lng = startLng + ratio * (endLng - startLng);
    const timestamp = new Date(departureTime.getTime() + ratio * (now.getTime() - departureTime.getTime()));

    positions.push({
      latitude: lat,
      longitude: lng,
      altitude: 30000 - Math.random() * 2000, // Around 30,000 ft with some variation
      groundSpeed: 450 + Math.random() * 50, // Around 450 knots with some variation
      heading: 315 + Math.random() * 10, // Roughly northwest with some variation
      timestamp
    });
  }

  return {
    flightId: `FP${Math.floor(1000 + Math.random() * 9000)}`, // Random flight number
    registration,
    aircraftType: 'C172', // Example aircraft type
    origin: 'LAX',
    destination: 'SFO',
    status: FlightStatus.EN_ROUTE,
    departureTime,
    estimatedArrivalTime,
    positions
  };
}

/**
 * Record a position update for a tracking record
 * @param trackingId The tracking ID to update
 * @param position The position update
 */
export async function recordPositionUpdate(trackingId: number, position: PositionUpdate) {
  try {
    const result = await query(
      `INSERT INTO position_updates (
        tracking_id, 
        latitude, 
        longitude, 
        altitude, 
        ground_speed, 
        heading, 
        timestamp
      ) VALUES ($1, $2, $3, $4, $5, $6, $7) RETURNING *`,
      [
        trackingId,
        position.latitude,
        position.longitude,
        position.altitude,
        position.groundSpeed,
        position.heading,
        position.timestamp
      ]
    );

    return {
      success: true,
      message: 'Position update recorded successfully',
      positionUpdate: result.rows[0]
    };
  } catch (error) {
    console.error('Error recording position update:', error);
    return {
      success: false,
      message: 'Failed to record position update'
    };
  }
}

/**
 * Get tracking data for a job
 * @param jobId The job ID to get tracking data for
 */
export async function getTrackingData(jobId: number) {
  try {
    // Get tracking record
    const trackingResult = await query(
      'SELECT * FROM flight_tracking WHERE job_id = $1',
      [jobId]
    );

    if (trackingResult.rows.length === 0) {
      return {
        success: false,
        message: 'No tracking data found for this job'
      };
    }

    const tracking = trackingResult.rows[0];

    // Get position updates
    const positionsResult = await query(
      'SELECT * FROM position_updates WHERE tracking_id = $1 ORDER BY timestamp ASC',
      [tracking.id]
    );

    // Get ADS-B data if tracking is in progress
    let adsbData = null;
    if (tracking.status === TrackingStatus.IN_PROGRESS) {
      adsbData = await getADSBData(tracking.registration);
    }

    return {
      success: true,
      tracking,
      positions: positionsResult.rows,
      adsbData
    };
  } catch (error) {
    console.error('Error getting tracking data:', error);
    return {
      success: false,
      message: 'Failed to get tracking data'
    };
  }
}

/**
 * Create database tables for flight tracking
 * This would typically be done in a migration, but included here for completeness
 */
export async function createTrackingTables() {
  try {
    // Create flight_tracking table
    await query(`
      CREATE TABLE IF NOT EXISTS flight_tracking (
        id SERIAL PRIMARY KEY,
        job_id INTEGER REFERENCES jobs(id) NOT NULL,
        registration TEXT NOT NULL,
        aircraft_type TEXT NOT NULL,
        origin TEXT NOT NULL,
        destination TEXT NOT NULL,
        status TEXT NOT NULL,
        departure_time TIMESTAMP,
        arrival_time TIMESTAMP,
        estimated_arrival_time TIMESTAMP,
        created_at TIMESTAMP NOT NULL,
        updated_at TIMESTAMP
      )
    `);

    // Create position_updates table
    await query(`
      CREATE TABLE IF NOT EXISTS position_updates (
        id SERIAL PRIMARY KEY,
        tracking_id INTEGER REFERENCES flight_tracking(id) NOT NULL,
        latitude DECIMAL(10, 6) NOT NULL,
        longitude DECIMAL(10, 6) NOT NULL,
        altitude DECIMAL(10, 2),
        ground_speed DECIMAL(10, 2),
        heading DECIMAL(10, 2),
        timestamp TIMESTAMP NOT NULL
      )
    `);

    console.log('Tracking tables created successfully');
    return {
      success: true,
      message: 'Tracking tables created successfully'
    };
  } catch (error) {
    console.error('Error creating tracking tables:', error);
    return {
      success: false,
      message: 'Failed to create tracking tables'
    };
  }
}
