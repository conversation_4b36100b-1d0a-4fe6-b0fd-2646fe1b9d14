import { query } from './db.server';
import { sendNewMessageNotification } from './emailNotifications.server';
import { sendNotification } from './notifications.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { getUserById } from './users.server';

// Define Message type
export interface Message {
  id: number;
  senderId: number;
  recipientId: number;
  jobId?: number;
  message: string;
  isRead: boolean;
  isNew: boolean;
  isDeleted: boolean;
  messageType: 'general' | 'dispute' | 'inquiry' | 'job_update';
  createdAt: Date;
}

// Define MessageThread type
export interface MessageThread {
  otherUserId: number;
  otherUserName: string;
  lastMessage: string;
  lastMessageDate: Date;
  unreadCount: number;
  messageType: string;
  jobId?: number;
}

/**
 * Send a message from one user to another
 * @param senderId Sender user ID
 * @param recipientId Recipient user ID
 * @param message Message content
 * @param jobId Optional job ID if the message is related to a job
 * @param messageType Type of message
 * @returns Newly created message
 */
export async function sendMessage(
  senderId: number,
  recipientId: number,
  message: string,
  jobId?: number,
  messageType: 'general' | 'dispute' | 'inquiry' | 'job_update' = 'general'
): Promise<Message | null> {
  // Create the message
  const result = await query(
    `INSERT INTO messages (
      sender_id,
      recipient_id,
      job_id,
      message,
      is_read,
      is_new,
      is_deleted,
      message_type,
      created_at
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW()) RETURNING *`,
    [senderId, recipientId, jobId || null, message, false, true, false, messageType]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const newMessage = mapMessage(result.rows[0]);

  // Get sender and recipient information for notifications
  const sender = await getUserById(senderId);
  const recipient = await getUserById(recipientId);

  if (sender && recipient) {
    // Send in-app notification
    await sendNotification({
      userId: recipientId,
      message: `New message from ${sender.name}`,
      type: 'new_message',
      metadata: {
        senderId,
        messageId: newMessage.id,
        messageType,
        jobId
      }
    });

    // Send email notification
    await sendNewMessageNotification(
      recipient.email,
      recipient.name,
      sender.name,
      message,
      messageType
    );
  }

  // Log the message
  logSecurityEventAsync({
    user_id: senderId,
    event_type: 'message_sent',
    description: `Message sent to user ID: ${recipientId}${jobId ? ` for job ID: ${jobId}` : ''}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return newMessage;
}

/**
 * Get messages between two users
 * @param userId Current user ID
 * @param otherUserId Other user ID
 * @param jobId Optional job ID to filter messages by job
 * @param limit Maximum number of messages to return
 * @param offset Offset for pagination
 * @returns Array of messages
 */
export async function getMessagesBetweenUsers(
  userId: number,
  otherUserId: number,
  jobId?: number,
  limit: number = 50,
  offset: number = 0
): Promise<Message[]> {
  let queryText = `
    SELECT * FROM messages
    WHERE ((sender_id = $1 AND recipient_id = $2) OR (sender_id = $2 AND recipient_id = $1))
  `;
  const params = [userId, otherUserId];

  if (jobId) {
    queryText += ` AND job_id = $${params.length + 1}`;
    params.push(jobId);
  }

  queryText += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
  params.push(limit, offset);

  const result = await query(queryText, params);

  // Mark messages as read
  await query(
    `UPDATE messages
     SET is_read = true
     WHERE recipient_id = $1 AND sender_id = $2 AND is_read = false`,
    [userId, otherUserId]
  );

  return result.rows.map(mapMessage);
}

/**
 * Get message threads for a user
 * @param userId User ID
 * @returns Array of message threads
 */
export async function getMessageThreadsForUser(userId: number): Promise<MessageThread[]> {
  const result = await query(
    `WITH latest_messages AS (
      SELECT DISTINCT ON (
        CASE
          WHEN sender_id = $1 THEN recipient_id
          ELSE sender_id
        END
      )
        CASE
          WHEN sender_id = $1 THEN recipient_id
          ELSE sender_id
        END AS other_user_id,
        message,
        created_at,
        message_type,
        job_id
      FROM messages
      WHERE sender_id = $1 OR recipient_id = $1
      ORDER BY other_user_id, created_at DESC
    ),
    unread_counts AS (
      SELECT
        sender_id AS other_user_id,
        COUNT(*) AS unread_count
      FROM messages
      WHERE recipient_id = $1 AND is_read = false
      GROUP BY sender_id
    )
    SELECT
      lm.other_user_id,
      u.name AS other_user_name,
      lm.message AS last_message,
      lm.created_at AS last_message_date,
      COALESCE(uc.unread_count, 0) AS unread_count,
      lm.message_type,
      lm.job_id
    FROM latest_messages lm
    JOIN users u ON lm.other_user_id = u.id
    LEFT JOIN unread_counts uc ON lm.other_user_id = uc.other_user_id
    ORDER BY lm.created_at DESC`,
    [userId]
  );

  return result.rows.map(row => ({
    otherUserId: row.other_user_id,
    otherUserName: row.other_user_name,
    lastMessage: row.last_message,
    lastMessageDate: new Date(row.last_message_date),
    unreadCount: parseInt(row.unread_count),
    messageType: row.message_type,
    jobId: row.job_id
  }));
}

/**
 * Get messages for a specific job
 * @param jobId Job ID
 * @param limit Maximum number of messages to return
 * @param offset Offset for pagination
 * @returns Array of messages
 */
export async function getMessagesForJob(
  jobId: number,
  limit: number = 50,
  offset: number = 0
): Promise<Message[]> {
  const result = await query(
    `SELECT * FROM messages
     WHERE job_id = $1
     ORDER BY created_at DESC
     LIMIT $2 OFFSET $3`,
    [jobId, limit, offset]
  );

  return result.rows.map(mapMessage);
}

/**
 * Get unread message count for a user
 * @param userId User ID
 * @returns Number of unread messages
 */
export async function getUnreadMessageCount(userId: number): Promise<number> {
  const result = await query(
    `SELECT COUNT(*) AS count
     FROM messages
     WHERE recipient_id = $1 AND is_read = false`,
    [userId]
  );

  return parseInt(result.rows[0].count);
}

/**
 * Mark a message as read
 * @param messageId Message ID
 * @param userId User ID (to ensure the user is the recipient)
 * @returns True if successful, false otherwise
 */
export async function markMessageAsRead(messageId: number, userId: number): Promise<boolean> {
  const result = await query(
    `UPDATE messages
     SET is_read = true, is_new = false
     WHERE id = $1 AND recipient_id = $2
     RETURNING *`,
    [messageId, userId]
  );

  return result.rows.length > 0;
}

/**
 * Move a message to trash
 * @param messageId Message ID
 * @param userId User ID (to ensure the user is the sender or recipient)
 * @returns True if successful, false otherwise
 */
export async function moveMessageToTrash(messageId: number, userId: number): Promise<boolean> {
  const result = await query(
    `UPDATE messages
     SET is_deleted = true
     WHERE id = $1 AND (sender_id = $2 OR recipient_id = $2)
     RETURNING *`,
    [messageId, userId]
  );

  return result.rows.length > 0;
}

/**
 * Restore a message from trash
 * @param messageId Message ID
 * @param userId User ID (to ensure the user is the sender or recipient)
 * @returns True if successful, false otherwise
 */
export async function restoreMessageFromTrash(messageId: number, userId: number): Promise<boolean> {
  const result = await query(
    `UPDATE messages
     SET is_deleted = false
     WHERE id = $1 AND (sender_id = $2 OR recipient_id = $2)
     RETURNING *`,
    [messageId, userId]
  );

  return result.rows.length > 0;
}

/**
 * Mark all messages from a user as read
 * @param fromUserId User ID of the sender
 * @param toUserId User ID of the recipient
 * @returns Number of messages marked as read
 */
export async function markAllMessagesAsRead(fromUserId: number, toUserId: number): Promise<number> {
  const result = await query(
    `UPDATE messages
     SET is_read = true, is_new = false
     WHERE sender_id = $1 AND recipient_id = $2 AND is_read = false
     RETURNING *`,
    [fromUserId, toUserId]
  );

  return result.rows.length;
}

/**
 * Get messages for a user's inbox
 * @param userId User ID
 * @param limit Maximum number of messages to return
 * @param offset Offset for pagination
 * @returns Array of messages
 */
export async function getInboxMessages(
  userId: number,
  limit: number = 50,
  offset: number = 0
): Promise<Message[]> {
  const result = await query(
    `SELECT m.*, 
      sender.name AS sender_name, 
      recipient.name AS recipient_name,
      CASE 
        WHEN j.id IS NOT NULL THEN j.title 
        ELSE NULL 
      END AS job_title
     FROM messages m
     JOIN users sender ON m.sender_id = sender.id
     JOIN users recipient ON m.recipient_id = recipient.id
     LEFT JOIN jobs j ON m.job_id = j.id
     WHERE m.recipient_id = $1 AND m.is_deleted = false
     ORDER BY m.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  return result.rows.map(row => ({
    ...mapMessage(row),
    senderName: row.sender_name,
    recipientName: row.recipient_name,
    jobTitle: row.job_title
  }));
}

/**
 * Get messages sent by a user
 * @param userId User ID
 * @param limit Maximum number of messages to return
 * @param offset Offset for pagination
 * @returns Array of messages
 */
export async function getSentMessages(
  userId: number,
  limit: number = 50,
  offset: number = 0
): Promise<Message[]> {
  const result = await query(
    `SELECT m.*, 
      sender.name AS sender_name, 
      recipient.name AS recipient_name,
      CASE 
        WHEN j.id IS NOT NULL THEN j.title 
        ELSE NULL 
      END AS job_title
     FROM messages m
     JOIN users sender ON m.sender_id = sender.id
     JOIN users recipient ON m.recipient_id = recipient.id
     LEFT JOIN jobs j ON m.job_id = j.id
     WHERE m.sender_id = $1 AND m.is_deleted = false
     ORDER BY m.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  return result.rows.map(row => ({
    ...mapMessage(row),
    senderName: row.sender_name,
    recipientName: row.recipient_name,
    jobTitle: row.job_title
  }));
}

/**
 * Get messages in a user's trash
 * @param userId User ID
 * @param limit Maximum number of messages to return
 * @param offset Offset for pagination
 * @returns Array of messages
 */
export async function getTrashMessages(
  userId: number,
  limit: number = 50,
  offset: number = 0
): Promise<Message[]> {
  const result = await query(
    `SELECT m.*, 
      sender.name AS sender_name, 
      recipient.name AS recipient_name,
      CASE 
        WHEN j.id IS NOT NULL THEN j.title 
        ELSE NULL 
      END AS job_title
     FROM messages m
     JOIN users sender ON m.sender_id = sender.id
     JOIN users recipient ON m.recipient_id = recipient.id
     LEFT JOIN jobs j ON m.job_id = j.id
     WHERE (m.sender_id = $1 OR m.recipient_id = $1) AND m.is_deleted = true
     ORDER BY m.created_at DESC
     LIMIT $2 OFFSET $3`,
    [userId, limit, offset]
  );

  return result.rows.map(row => ({
    ...mapMessage(row),
    senderName: row.sender_name,
    recipientName: row.recipient_name,
    jobTitle: row.job_title
  }));
}

/**
 * Start a dispute conversation
 * @param initiatorId User ID of the initiator
 * @param againstId User ID of the other party
 * @param jobId Job ID
 * @param reason Reason for the dispute
 * @returns First message of the dispute
 */
export async function startDisputeConversation(
  initiatorId: number,
  againstId: number,
  jobId: number,
  reason: string
): Promise<Message | null> {
  // First create a dispute record
  const disputeResult = await query(
    `INSERT INTO disputes (
      job_id,
      initiated_by,
      against,
      reason,
      status,
      created_at
    ) VALUES ($1, $2, $3, $4, $5, NOW()) RETURNING *`,
    [jobId, initiatorId, againstId, reason, 'open']
  );

  if (disputeResult.rows.length === 0) {
    return null;
  }

  const disputeId = disputeResult.rows[0].id;

  // Then send a message to start the conversation
  const message = `DISPUTE OPENED: ${reason}`;

  return sendMessage(
    initiatorId,
    againstId,
    message,
    jobId,
    'dispute'
  );
}

/**
 * Get disputes for a user
 * @param userId User ID
 * @param status Optional status filter
 * @returns Array of disputes
 */
export async function getDisputesForUser(
  userId: number,
  status?: 'open' | 'resolved'
): Promise<any[]> {
  let queryText = `
    SELECT d.*, j.title AS job_title, 
    u1.name AS initiator_name, u2.name AS against_name
    FROM disputes d
    JOIN jobs j ON d.job_id = j.id
    JOIN users u1 ON d.initiated_by = u1.id
    JOIN users u2 ON d.against = u2.id
    WHERE (d.initiated_by = $1 OR d.against = $1)
  `;
  const params = [userId];

  if (status) {
    queryText += ` AND d.status = $${params.length + 1}`;
    params.push(status);
  }

  queryText += ` ORDER BY d.created_at DESC`;

  const result = await query(queryText, params);

  return result.rows.map(row => ({
    id: row.id,
    jobId: row.job_id,
    jobTitle: row.job_title,
    initiatedBy: row.initiated_by,
    initiatorName: row.initiator_name,
    against: row.against,
    againstName: row.against_name,
    reason: row.reason,
    status: row.status,
    resolution: row.resolution,
    createdAt: new Date(row.created_at),
    updatedAt: row.updated_at ? new Date(row.updated_at) : undefined
  }));
}

/**
 * Resolve a dispute
 * @param disputeId Dispute ID
 * @param resolution Resolution text
 * @param resolvedBy User ID of the resolver
 * @returns True if successful, false otherwise
 */
export async function resolveDispute(
  disputeId: number,
  resolution: string,
  resolvedBy: number
): Promise<boolean> {
  // Update the dispute status
  const disputeResult = await query(
    `UPDATE disputes
     SET status = 'resolved', resolution = $1, updated_at = NOW()
     WHERE id = $2
     RETURNING *`,
    [resolution, disputeId]
  );

  if (disputeResult.rows.length === 0) {
    return false;
  }

  const dispute = disputeResult.rows[0];

  // Send a message to both parties
  const message = `DISPUTE RESOLVED: ${resolution}`;

  // Send to the initiator
  await sendMessage(
    resolvedBy,
    dispute.initiated_by,
    message,
    dispute.job_id,
    'dispute'
  );

  // Send to the other party
  await sendMessage(
    resolvedBy,
    dispute.against,
    message,
    dispute.job_id,
    'dispute'
  );

  return true;
}

/**
 * Extended Message interface with additional fields for UI display
 */
export interface ExtendedMessage extends Message {
  senderName?: string;
  recipientName?: string;
  jobTitle?: string;
  // Additional fields for different message types
  disputeData?: {
    disputeId: number;
    status: string;
    reason: string;
    resolution?: string;
  };
  inquiryData?: {
    inquiryId: number;
    status: string;
    notes?: string;
  };
}

/**
 * Map database row to Message interface
 * @param row Database row
 * @returns Message object
 */
function mapMessage(row: any): Message {
  return {
    id: row.id,
    senderId: row.sender_id,
    recipientId: row.recipient_id,
    jobId: row.job_id,
    message: row.message,
    isRead: row.is_read,
    isNew: row.is_new !== undefined ? row.is_new : !row.is_read, // Default to !isRead if is_new is not in the database
    isDeleted: row.is_deleted || false, // Default to false if is_deleted is not in the database
    messageType: row.message_type,
    createdAt: new Date(row.created_at)
  };
}

/**
 * Get a single message by ID with type-specific data
 * @param messageId Message ID
 * @param userId User ID (to ensure the user is the sender or recipient)
 * @returns Extended message with type-specific data
 */
export async function getMessageById(messageId: number, userId: number): Promise<ExtendedMessage | null> {
  // Get the basic message data
  const messageResult = await query(
    `SELECT m.*, 
      sender.name AS sender_name, 
      recipient.name AS recipient_name,
      CASE 
        WHEN j.id IS NOT NULL THEN j.title 
        ELSE NULL 
      END AS job_title
     FROM messages m
     JOIN users sender ON m.sender_id = sender.id
     JOIN users recipient ON m.recipient_id = recipient.id
     LEFT JOIN jobs j ON m.job_id = j.id
     WHERE m.id = $1 AND (m.sender_id = $2 OR m.recipient_id = $2)`,
    [messageId, userId]
  );

  if (messageResult.rows.length === 0) {
    return null;
  }

  const message = messageResult.rows[0];
  const extendedMessage: ExtendedMessage = {
    ...mapMessage(message),
    senderName: message.sender_name,
    recipientName: message.recipient_name,
    jobTitle: message.job_title
  };

  // If the user is the recipient, mark the message as read
  if (message.recipient_id === userId && !message.is_read) {
    await markMessageAsRead(messageId, userId);
    extendedMessage.isRead = true;
    extendedMessage.isNew = false;
  }

  // Get additional data based on message type
  if (message.message_type === 'dispute' && message.job_id) {
    const disputeResult = await query(
      `SELECT * FROM disputes
       WHERE job_id = $1 AND (initiated_by = $2 OR against = $2)
       ORDER BY created_at DESC LIMIT 1`,
      [message.job_id, message.sender_id]
    );

    if (disputeResult.rows.length > 0) {
      const dispute = disputeResult.rows[0];
      extendedMessage.disputeData = {
        disputeId: dispute.id,
        status: dispute.status,
        reason: dispute.reason,
        resolution: dispute.resolution
      };
    }
  } else if (message.message_type === 'inquiry' && message.job_id) {
    const inquiryResult = await query(
      `SELECT * FROM bid_inquiries
       WHERE job_id = $1 AND pilot_id = $2
       ORDER BY created_at DESC LIMIT 1`,
      [message.job_id, message.sender_id]
    );

    if (inquiryResult.rows.length > 0) {
      const inquiry = inquiryResult.rows[0];
      extendedMessage.inquiryData = {
        inquiryId: inquiry.id,
        status: inquiry.status,
        notes: inquiry.notes
      };
    }
  }

  return extendedMessage;
}

/**
 * Reply to a message
 * @param messageId Original message ID
 * @param userId User ID of the replier
 * @param replyText Reply message content
 * @returns Newly created message
 */
export async function replyToMessage(
  messageId: number,
  userId: number,
  replyText: string
): Promise<Message | null> {
  // Get the original message
  const originalMessageResult = await query(
    `SELECT * FROM messages WHERE id = $1 AND (sender_id = $2 OR recipient_id = $2)`,
    [messageId, userId]
  );

  if (originalMessageResult.rows.length === 0) {
    return null;
  }

  const originalMessage = originalMessageResult.rows[0];

  // Determine the recipient (the other person in the conversation)
  const recipientId = originalMessage.sender_id === userId 
    ? originalMessage.recipient_id 
    : originalMessage.sender_id;

  // Send the reply with the same message type and job ID as the original
  return sendMessage(
    userId,
    recipientId,
    replyText,
    originalMessage.job_id,
    originalMessage.message_type
  );
}
