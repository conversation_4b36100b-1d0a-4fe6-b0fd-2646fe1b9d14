import { query } from './db.server';
import { sendNewExpenseNotification } from './emailNotifications.server';
import { sendNotification } from './notifications.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { getUserById } from './users.server';

// Define Expense type
export interface Expense {
  id: number;
  jobId: number;
  userId: number;
  amount: number;
  description: string;
  receiptUrl?: string;
  status: 'pending' | 'approved' | 'rejected';
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Create a new expense
 * @param jobId Job ID
 * @param userId User ID of the person submitting the expense
 * @param amount Expense amount
 * @param description Expense description
 * @param receiptUrl Optional URL to the receipt image
 * @returns Newly created expense
 */
export async function createExpense(
  jobId: number,
  userId: number,
  amount: number,
  description: string,
  receiptUrl?: string
): Promise<Expense | null> {
  // Create the expense
  const result = await query(
    `INSERT INTO expenses (
      job_id,
      user_id,
      amount,
      description,
      receipt_url,
      status,
      created_at
    ) VALUES ($1, $2, $3, $4, $5, $6, NOW()) RETURNING *`,
    [jobId, userId, amount, description, receiptUrl || null, 'pending']
  );

  if (result.rows.length === 0) {
    return null;
  }

  const expense = mapExpense(result.rows[0]);

  // Get the job owner to send notification
  const jobResult = await query(
    'SELECT owner_id, title FROM jobs WHERE id = $1',
    [jobId]
  );

  if (jobResult.rows.length > 0) {
    const ownerId = jobResult.rows[0].owner_id;
    const jobTitle = jobResult.rows[0].title;
    const owner = await getUserById(ownerId);
    const submitter = await getUserById(userId);

    if (owner && submitter) {
      // Send in-app notification
      await sendNotification({
        userId: ownerId,
        message: `New expense submitted for job: ${jobTitle}`,
        type: 'new_expense',
        metadata: {
          jobId,
          expenseId: expense.id,
          amount,
          submitterId: userId
        }
      });

      // Send email notification
      await sendNewExpenseNotification(
        owner.email,
        owner.name,
        jobTitle,
        jobId,
        amount,
        description
      );
    }
  }

  // Log the expense creation
  logSecurityEventAsync({
    user_id: userId,
    event_type: 'expense_created',
    description: `Expense created for job ID: ${jobId}, amount: $${amount.toFixed(2)}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return expense;
}

/**
 * Get expenses for a job
 * @param jobId Job ID
 * @returns Array of expenses
 */
export async function getExpensesForJob(jobId: number): Promise<Expense[]> {
  const result = await query(
    'SELECT * FROM expenses WHERE job_id = $1 ORDER BY created_at DESC',
    [jobId]
  );

  return result.rows.map(mapExpense);
}

/**
 * Get expenses submitted by a user
 * @param userId User ID
 * @returns Array of expenses
 */
export async function getExpensesByUser(userId: number): Promise<Expense[]> {
  const result = await query(
    'SELECT * FROM expenses WHERE user_id = $1 ORDER BY created_at DESC',
    [userId]
  );

  return result.rows.map(mapExpense);
}

/**
 * Get an expense by ID
 * @param expenseId Expense ID
 * @returns Expense or null if not found
 */
export async function getExpenseById(expenseId: number): Promise<Expense | null> {
  const result = await query(
    'SELECT * FROM expenses WHERE id = $1',
    [expenseId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  return mapExpense(result.rows[0]);
}

/**
 * Update an expense's status
 * @param expenseId Expense ID
 * @param status New status
 * @param updatedBy User ID of the person updating the status
 * @returns Updated expense
 */
export async function updateExpenseStatus(
  expenseId: number,
  status: 'approved' | 'rejected',
  updatedBy: number
): Promise<Expense | null> {
  // Update the expense status
  const result = await query(
    `UPDATE expenses
     SET status = $1, updated_at = NOW()
     WHERE id = $2
     RETURNING *`,
    [status, expenseId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const expense = mapExpense(result.rows[0]);

  // Get the job and user information for notifications
  const jobResult = await query(
    'SELECT title FROM jobs WHERE id = $1',
    [expense.jobId]
  );

  const submitter = await getUserById(expense.userId);
  const updater = await getUserById(updatedBy);

  if (jobResult.rows.length > 0 && submitter && updater) {
    const jobTitle = jobResult.rows[0].title;

    // Send in-app notification
    await sendNotification({
      userId: expense.userId,
      message: `Your expense for job "${jobTitle}" has been ${status}`,
      type: 'expense_status_updated',
      metadata: {
        jobId: expense.jobId,
        expenseId,
        status,
        updatedBy
      }
    });

    // Log the status update
    logSecurityEventAsync({
      user_id: updatedBy,
      event_type: `expense_${status}`,
      description: `Expense ID: ${expenseId} ${status} by ${updater.name}`,
      ip_address: '127.0.0.1',
      user_agent: 'Server'
    });
  }

  return expense;
}

/**
 * Upload a receipt for an expense
 * @param expenseId Expense ID
 * @param receiptUrl URL to the receipt image
 * @param userId User ID of the person uploading the receipt
 * @returns Updated expense
 */
export async function uploadReceiptForExpense(
  expenseId: number,
  receiptUrl: string,
  userId: number
): Promise<Expense | null> {
  // Check if the user owns the expense
  const checkResult = await query(
    'SELECT * FROM expenses WHERE id = $1 AND user_id = $2',
    [expenseId, userId]
  );

  if (checkResult.rows.length === 0) {
    return null;
  }

  // Update the expense with the receipt URL
  const result = await query(
    `UPDATE expenses
     SET receipt_url = $1, updated_at = NOW()
     WHERE id = $2
     RETURNING *`,
    [receiptUrl, expenseId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  // Log the receipt upload
  logSecurityEventAsync({
    user_id: userId,
    event_type: 'receipt_uploaded',
    description: `Receipt uploaded for expense ID: ${expenseId}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return mapExpense(result.rows[0]);
}

/**
 * Get total approved expenses for a job
 * @param jobId Job ID
 * @returns Total amount of approved expenses
 */
export async function getTotalApprovedExpensesForJob(jobId: number): Promise<number> {
  const result = await query(
    `SELECT SUM(amount) AS total
     FROM expenses
     WHERE job_id = $1 AND status = 'approved'`,
    [jobId]
  );

  return parseFloat(result.rows[0].total || '0');
}

/**
 * Get pending expenses count for a job
 * @param jobId Job ID
 * @returns Number of pending expenses
 */
export async function getPendingExpensesCountForJob(jobId: number): Promise<number> {
  const result = await query(
    `SELECT COUNT(*) AS count
     FROM expenses
     WHERE job_id = $1 AND status = 'pending'`,
    [jobId]
  );

  return parseInt(result.rows[0].count);
}

/**
 * Map database row to Expense interface
 * @param row Database row
 * @returns Expense object
 */
function mapExpense(row: any): Expense {
  return {
    id: row.id,
    jobId: row.job_id,
    userId: row.user_id,
    amount: parseFloat(row.amount),
    description: row.description,
    receiptUrl: row.receipt_url,
    status: row.status,
    createdAt: new Date(row.created_at),
    updatedAt: row.updated_at ? new Date(row.updated_at) : undefined
  };
}