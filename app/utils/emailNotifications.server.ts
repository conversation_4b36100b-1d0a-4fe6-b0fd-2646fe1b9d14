import { logSecurityEventAsync } from './securityAudit.server';

// Define EmailNotification type
export interface EmailNotification {
  to: string;
  subject: string;
  body: string;
  cc?: string[];
  bcc?: string[];
  attachments?: EmailAttachment[];
}

// Define EmailAttachment type
export interface EmailAttachment {
  filename: string;
  content: Buffer | string;
  contentType: string;
}

/**
 * Send an email notification
 * @param notification Email notification to send
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendEmailNotification(notification: EmailNotification): Promise<boolean> {
  try {
    // In a real implementation, this would use a service like SendGrid, AWS SES, etc.
    // For now, we'll just log the email and pretend it was sent
    console.log('Sending email notification:', {
      to: notification.to,
      subject: notification.subject,
      bodyPreview: notification.body.substring(0, 100) + '...',
      cc: notification.cc,
      bcc: notification.bcc,
      attachments: notification.attachments?.map(a => a.filename)
    });

    // Log the email send attempt
    logSecurityEventAsync({
      event_type: 'email_notification_sent',
      description: `Email sent to ${notification.to}: ${notification.subject}`,
      ip_address: '127.0.0.1',
      user_agent: 'Server'
    });

    return true;
  } catch (error) {
    console.error('Error sending email notification:', error);
    return false;
  }
}

/**
 * Send a welcome email to a new user
 * @param email User's email address
 * @param name User's name
 * @param role User's role
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendWelcomeEmail(
  email: string,
  name: string,
  role: string
): Promise<boolean> {
  const subject = `Welcome to FerryPros!`;
  const body = `
    <h1>Welcome to FerryPros, ${name}!</h1>
    <p>Thank you for joining our platform as a ${role}.</p>
    <p>We're excited to have you on board and look forward to helping you connect with ${
      role === 'pilot' ? 'aircraft owners' : 'ferry pilots'
    }.</p>
    <p>If you have any questions, please don't hesitate to contact our support team.</p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a verification email to a new user
 * @param email User's email address
 * @param name User's name
 * @param verificationToken Verification token
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendVerificationEmail(
  email: string,
  name: string,
  verificationToken: string
): Promise<boolean> {
  const verificationUrl = `${process.env.APP_URL}/verify?token=${verificationToken}`;
  const subject = `Verify Your FerryPros Account`;
  const body = `
    <h1>Verify Your Email, ${name}</h1>
    <p>Thank you for registering with FerryPros. Please click the link below to verify your email address:</p>
    <p><a href="${verificationUrl}">${verificationUrl}</a></p>
    <p>If you did not create an account, please ignore this email.</p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a new job
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendNewJobNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `New Job Posted: ${jobTitle}`;
  const body = `
    <h1>New Job Posted</h1>
    <p>Hello ${name},</p>
    <p>A new job has been posted that might interest you: <strong>${jobTitle}</strong></p>
    <p>Click the link below to view the job details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a job application
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param pilotName Pilot's name
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendJobApplicationNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  pilotName: string
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}/applications`;
  const subject = `New Application for Job: ${jobTitle}`;
  const body = `
    <h1>New Job Application</h1>
    <p>Hello ${name},</p>
    <p>A pilot has applied for your job: <strong>${jobTitle}</strong></p>
    <p>Pilot: ${pilotName}</p>
    <p>Click the link below to view the application details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about an application status change
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param status New application status
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendApplicationStatusNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  status: string
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `Application Status Update: ${jobTitle}`;
  const body = `
    <h1>Application Status Update</h1>
    <p>Hello ${name},</p>
    <p>Your application for the job <strong>${jobTitle}</strong> has been <strong>${status}</strong>.</p>
    <p>Click the link below to view the job details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a job approval request
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param requesterName Requester's name
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendJobApprovalRequestNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  requesterName: string
): Promise<boolean> {
  const approvalUrl = `${process.env.APP_URL}/jobs/${jobId}/approve`;
  const subject = `Job Approval Request: ${jobTitle}`;
  const body = `
    <h1>Job Approval Request</h1>
    <p>Hello ${name},</p>
    <p>${requesterName} has requested your approval for the job: <strong>${jobTitle}</strong></p>
    <p>Click the link below to review and approve/reject the job:</p>
    <p><a href="${approvalUrl}">${approvalUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a job approval decision
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param approved Whether the job was approved
 * @param approverName Approver's name
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendJobApprovalDecisionNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  approved: boolean,
  approverName: string
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `Job ${approved ? 'Approved' : 'Rejected'}: ${jobTitle}`;
  const body = `
    <h1>Job ${approved ? 'Approved' : 'Rejected'}</h1>
    <p>Hello ${name},</p>
    <p>Your job <strong>${jobTitle}</strong> has been <strong>${approved ? 'approved' : 'rejected'}</strong> by ${approverName}.</p>
    ${approved ? 
      `<p>The job is now live and visible to pilots.</p>` : 
      `<p>Please review and make necessary changes before resubmitting for approval.</p>`
    }
    <p>Click the link below to view the job details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a bid inquiry
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param pilotName Pilot's name
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendBidInquiryNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  pilotName: string
): Promise<boolean> {
  const inquiryUrl = `${process.env.APP_URL}/jobs/${jobId}/inquiries`;
  const subject = `New Bid Inquiry: ${jobTitle}`;
  const body = `
    <h1>New Bid Inquiry</h1>
    <p>Hello ${name},</p>
    <p>${pilotName} has requested to bid on your job: <strong>${jobTitle}</strong></p>
    <p>Click the link below to review and approve/reject the inquiry:</p>
    <p><a href="${inquiryUrl}">${inquiryUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a bid inquiry decision
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param approved Whether the inquiry was approved
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendBidInquiryDecisionNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  approved: boolean
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `Bid Inquiry ${approved ? 'Approved' : 'Rejected'}: ${jobTitle}`;
  const body = `
    <h1>Bid Inquiry ${approved ? 'Approved' : 'Rejected'}</h1>
    <p>Hello ${name},</p>
    <p>Your request to bid on the job <strong>${jobTitle}</strong> has been <strong>${approved ? 'approved' : 'rejected'}</strong>.</p>
    ${approved ? 
      `<p>You can now submit your bid for this job.</p>` : 
      `<p>Unfortunately, your request to bid on this job has been declined.</p>`
    }
    <p>Click the link below to view the job details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a new message
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param senderName Sender's name
 * @param messagePreview Preview of the message
 * @param messageType Type of message
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendNewMessageNotification(
  email: string,
  name: string,
  senderName: string,
  messagePreview: string,
  messageType: string = 'general'
): Promise<boolean> {
  const messagesUrl = `${process.env.APP_URL}/messages`;
  const subject = `New Message from ${senderName}`;
  const body = `
    <h1>New Message</h1>
    <p>Hello ${name},</p>
    <p>You have received a new ${messageType} message from ${senderName}:</p>
    <p><em>"${messagePreview.substring(0, 100)}${messagePreview.length > 100 ? '...' : ''}"</em></p>
    <p>Click the link below to view and respond to the message:</p>
    <p><a href="${messagesUrl}">${messagesUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a new expense
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param amount Expense amount
 * @param description Expense description
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendNewExpenseNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  amount: number,
  description: string
): Promise<boolean> {
  const expenseUrl = `${process.env.APP_URL}/jobs/${jobId}/expenses`;
  const subject = `New Expense for Job: ${jobTitle}`;
  const body = `
    <h1>New Expense Submitted</h1>
    <p>Hello ${name},</p>
    <p>A new expense has been submitted for the job: <strong>${jobTitle}</strong></p>
    <p>Amount: $${amount.toFixed(2)}</p>
    <p>Description: ${description}</p>
    <p>Click the link below to review the expense:</p>
    <p><a href="${expenseUrl}">${expenseUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about a dispute
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param initiatorName Name of the person who initiated the dispute
 * @param reason Reason for the dispute
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendDisputeNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  initiatorName: string,
  reason: string
): Promise<boolean> {
  const disputeUrl = `${process.env.APP_URL}/jobs/${jobId}/dispute`;
  const subject = `Dispute Filed for Job: ${jobTitle}`;
  const body = `
    <h1>Dispute Filed</h1>
    <p>Hello ${name},</p>
    <p>A dispute has been filed for the job: <strong>${jobTitle}</strong></p>
    <p>Initiated by: ${initiatorName}</p>
    <p>Reason: ${reason}</p>
    <p>Click the link below to view and respond to the dispute:</p>
    <p><a href="${disputeUrl}">${disputeUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about job completion
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendJobCompletionNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `Job Completed: ${jobTitle}`;
  const body = `
    <h1>Job Completed</h1>
    <p>Hello ${name},</p>
    <p>The job <strong>${jobTitle}</strong> has been marked as completed.</p>
    <p>Click the link below to view the job details and confirm completion:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}

/**
 * Send a notification about funds being released
 * @param email Recipient's email address
 * @param name Recipient's name
 * @param jobTitle Job title
 * @param jobId Job ID
 * @param amount Amount released
 * @returns True if the email was sent successfully, false otherwise
 */
export async function sendFundsReleasedNotification(
  email: string,
  name: string,
  jobTitle: string,
  jobId: number,
  amount: number
): Promise<boolean> {
  const jobUrl = `${process.env.APP_URL}/jobs/${jobId}`;
  const subject = `Funds Released for Job: ${jobTitle}`;
  const body = `
    <h1>Funds Released</h1>
    <p>Hello ${name},</p>
    <p>Funds have been released for the job: <strong>${jobTitle}</strong></p>
    <p>Amount: $${amount.toFixed(2)}</p>
    <p>Click the link below to view the job details:</p>
    <p><a href="${jobUrl}">${jobUrl}</a></p>
    <p>Best regards,<br>The FerryPros Team</p>
  `;

  return sendEmailNotification({
    to: email,
    subject,
    body
  });
}