import { Pool, PoolClient, PoolConfig } from 'pg';
import { authenticator } from 'otplib';
import * as crypto from 'crypto';
import { encrypt, decrypt, isEncrypted, generateSecureToken } from './encryption.server.js';
import { Kysely, PostgresDialect } from 'kysely';
import { queryCache } from './cache.server.js';
import fs from "fs";

// Database connection pool metrics
interface PoolMetrics {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
  maxConnections: number;
  queryCount: number;
  queryErrors: number;
  avgQueryTime: number;
  lastResetTime: Date;
}

// Pool configuration with sensible defaults for scalability
const poolConfig: PoolConfig = {
  // Maximum number of clients the pool should contain
  max: parseInt(process.env.DB_POOL_MAX || '20'),

  // Maximum time in ms that a client can stay idle before being closed
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT || '30000'),

  // Maximum time in ms to wait for a connection before timing out
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT || '5000'),

  // How often to check for idle clients and stale connections
  allowExitOnIdle: false
};

// Note: In a production environment, these values should be set using environment variables
let pool: Pool;
let poolMetrics: PoolMetrics = {
  totalConnections: 0,
  idleConnections: 0,
  waitingClients: 0,
  maxConnections: poolConfig.max || 20,
  queryCount: 0,
  queryErrors: 0,
  avgQueryTime: 0,
  lastResetTime: new Date()
};

declare global {
  var __db__: Pool | undefined;
  var __kysely__: Kysely<any> | undefined;
  var __db_metrics__: PoolMetrics | undefined;
}

// This is needed because in development we don't want to restart
// the server with every change, but we want to make sure we don't
// create a new connection to the DB with every change either.
if (process.env.NODE_ENV === "production") {
  pool = new Pool({
    host: process.env.DB_HOST,
    port: parseInt(process.env.DB_PORT || '25060'),
    database: process.env.DB_NAME,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    ssl: {
      cert: process.env.DB_SSL__CA,
      rejectUnauthorized: false
    },
    ...poolConfig
  });
} else {
  if (!global.__db__) {
    global.__db__ = new Pool({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'ferrypros',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || 'postgres',
      ...poolConfig
    });
    global.__db_metrics__ = { ...poolMetrics };
  }
  pool = global.__db__;
  poolMetrics = global.__db_metrics__ || poolMetrics;
}

// Set up event listeners for the pool
pool.on('connect', () => {
  poolMetrics.totalConnections++;
});

pool.on('acquire', () => {
  poolMetrics.idleConnections = Math.max(0, poolMetrics.idleConnections - 1);
});

pool.on('release', () => {
  poolMetrics.idleConnections++;
});

pool.on('error', (err) => {
  console.error('Unexpected error on idle client', err);
  poolMetrics.queryErrors++;
});

// Initialize Kysely with the PostgreSQL pool
let kyselyInstance: Kysely<any>;

if (process.env.NODE_ENV === "production") {
  kyselyInstance = new Kysely<any>({
    dialect: new PostgresDialect({
      pool
    }),
  });
} else {
  if (!global.__kysely__) {
    global.__kysely__ = new Kysely<any>({
      dialect: new PostgresDialect({
        pool
      }),
    });
  }
  kyselyInstance = global.__kysely__;
}

export { pool };
export const db = kyselyInstance;
// Export the expression builder functionality from the Kysely instance
export const eb = kyselyInstance.dynamic;
export const sql = kyselyInstance.sql;

/**
 * Execute a query with automatic connection management and metrics tracking
 * @param text SQL query text
 * @param params Query parameters
 * @param useCache Whether to use query caching (default: false)
 * @returns Query result
 */
export async function query(text: string, params?: any[], useCache: boolean = false) {
  // Check if we should use cache and if the query is cacheable (SELECT queries)
  if (useCache && text.trim().toUpperCase().startsWith('SELECT')) {
    const cacheKey = `query:${text}:${JSON.stringify(params || [])}`;

    // Try to get from cache first
    return await queryCache.getOrCompute(cacheKey, async () => {
      return await executeQuery(text, params);
    });
  }

  // Execute without caching
  return await executeQuery(text, params);
}

/**
 * Internal function to execute a query with connection management
 */
async function executeQuery(text: string, params?: any[]) {
  const startTime = Date.now();
  const client = await pool.connect();

  try {
    poolMetrics.waitingClients = Math.max(0, poolMetrics.waitingClients - 1);
    const result = await client.query(text, params);

    // Update metrics
    poolMetrics.queryCount++;
    const queryTime = Date.now() - startTime;
    poolMetrics.avgQueryTime = 
      (poolMetrics.avgQueryTime * (poolMetrics.queryCount - 1) + queryTime) / 
      poolMetrics.queryCount;

    return result;
  } catch (error) {
    poolMetrics.queryErrors++;
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Get current database pool metrics
 */
export function getPoolMetrics(): PoolMetrics {
  // Update real-time metrics from the pool
  if (pool) {
    poolMetrics.totalConnections = pool.totalCount;
    poolMetrics.idleConnections = pool.idleCount;
    poolMetrics.waitingClients = pool.waitingCount;
  }

  return { ...poolMetrics };
}

/**
 * Reset database pool metrics
 */
export function resetPoolMetrics(): void {
  poolMetrics.queryCount = 0;
  poolMetrics.queryErrors = 0;
  poolMetrics.avgQueryTime = 0;
  poolMetrics.lastResetTime = new Date();
}

/**
 * Execute a query with retries for better resilience
 * @param text SQL query text
 * @param params Query parameters
 * @param options Retry options
 * @returns Query result
 */
export async function queryWithRetries(
  text: string, 
  params?: any[], 
  options: { 
    maxRetries?: number; 
    retryDelay?: number; 
    useCache?: boolean;
  } = {}
) {
  const { 
    maxRetries = 3, 
    retryDelay = 500,
    useCache = false
  } = options;

  let lastError;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await query(text, params, useCache);
    } catch (error: any) {
      lastError = error;

      // Only retry on connection-related errors
      if (!isRetryableError(error) || attempt === maxRetries) {
        throw error;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));
    }
  }

  throw lastError;
}

/**
 * Check if an error is retryable (connection-related)
 */
function isRetryableError(error: any): boolean {
  const retryableCodes = [
    'ECONNRESET', 'ECONNREFUSED', 'ETIMEDOUT', 
    '08006', '08001', '08004', '57P01' // PostgreSQL error codes
  ];

  return retryableCodes.some(code => 
    error.code === code || 
    (error.message && error.message.includes(code))
  );
}

export async function getUser(userId: string) {
  const result = await query('SELECT * FROM users WHERE id = $1', [userId]);

  if (result.rows.length > 0) {
    const user = result.rows[0];

    // Decrypt email if it's encrypted
    if (isEncrypted(user.email)) {
      user.email = decrypt(user.email);
    }

    // Decrypt license information if present and encrypted
    if (user.license_number && isEncrypted(user.license_number)) {
      user.license_number = decrypt(user.license_number);
    }

    if (user.license_type && isEncrypted(user.license_type)) {
      user.license_type = decrypt(user.license_type);
    }

    // Decrypt MFA secret if present and encrypted
    if (user.mfa_secret && isEncrypted(user.mfa_secret)) {
      user.mfa_secret = decrypt(user.mfa_secret);
    }

    return user;
  }

  return null;
}

export async function getUserByEmail(email: string) {
  // First try to find the user with the plaintext email (for backward compatibility)
  let result = await query('SELECT * FROM users WHERE email = $1', [email]);

  // If not found, try with the encrypted email
  if (result.rows.length === 0) {
    const encryptedEmail = encrypt(email);
    result = await query('SELECT * FROM users WHERE email = $1', [encryptedEmail]);
  }

  // If user is found, decrypt sensitive fields
  if (result.rows.length > 0) {
    const user = result.rows[0];

    // Decrypt email if it's encrypted
    if (isEncrypted(user.email)) {
      user.email = decrypt(user.email);
    }

    // Decrypt license information if present and encrypted
    if (user.license_number && isEncrypted(user.license_number)) {
      user.license_number = decrypt(user.license_number);
    }

    if (user.license_type && isEncrypted(user.license_type)) {
      user.license_type = decrypt(user.license_type);
    }

    // Decrypt MFA secret if present and encrypted
    if (user.mfa_secret && isEncrypted(user.mfa_secret)) {
      user.mfa_secret = decrypt(user.mfa_secret);
    }

    return user;
  }

  return null;
}

export async function createUser(userData: { 
  email: string, 
  passwordHash: string, 
  role: string, 
  name: string,
  licenseNumber?: string,
  licenseType?: string,
  licenseExpiry?: Date,
  businessName?: string,
  businessDescription?: string,
  companyName?: string,
  serviceProviderType?: string
}) {
  const { email, passwordHash, role, name, licenseNumber, licenseType, licenseExpiry, businessName, businessDescription, companyName, serviceProviderType } = userData;

  // Encrypt sensitive data
  const encryptedEmail = encrypt(email);
  const encryptedLicenseNumber = licenseNumber ? encrypt(licenseNumber) : null;
  const encryptedLicenseType = licenseType ? encrypt(licenseType) : null;
  const encryptedBusinessName = businessName ? encrypt(businessName) : null;
  const encryptedBusinessDescription = businessDescription ? encrypt(businessDescription) : null;
  const encryptedCompanyName = companyName ? encrypt(companyName) : null;
  const encryptedServiceProviderType = serviceProviderType ? encrypt(serviceProviderType) : null;

  // Generate a secure verification token
  const verificationToken = generateSecureToken();

  // Set expiration to 24 hours from now
  const now = new Date();
  const verificationTokenExpires = new Date(now.getTime() + 24 * 60 * 60 * 1000);

  // Determine account_type based on role
  let accountType = 'individual';
  if (role === 'business') {
    accountType = 'business';
  } else if (role === 'service_provider') {
    accountType = 'service_provider';
  }

  const result = await query(
    `INSERT INTO users (
      email, 
      password_hash, 
      role, 
      name, 
      created_at, 
      verification_token, 
      verification_token_expires,
      license_number,
      license_type,
      license_expiry,
      account_type,
      business_name,
      business_description,
      company_name,
      service_provider_type
    ) VALUES ($1, $2, $3, $4, NOW(), $5, $6, $7, $8, $9, $10, $11, $12, $13, $14) RETURNING *`,
    [
      encryptedEmail, 
      passwordHash, 
      role, 
      name, 
      verificationToken, 
      verificationTokenExpires,
      encryptedLicenseNumber,
      encryptedLicenseType,
      licenseExpiry || null,
      accountType,
      encryptedBusinessName,
      encryptedBusinessDescription,
      encryptedCompanyName,
      encryptedServiceProviderType
    ]
  );

  // Decrypt the data for the returned user object
  const user = result.rows[0];
  user.email = email; // Use the original email

  if (licenseNumber) {
    user.license_number = licenseNumber;
  }

  if (licenseType) {
    user.license_type = licenseType;
  }

  if (businessName) {
    user.business_name = businessName;
  }

  if (businessDescription) {
    user.business_description = businessDescription;
  }

  if (companyName) {
    user.company_name = companyName;
  }

  if (serviceProviderType) {
    user.service_provider_type = serviceProviderType;
  }

  return user;
}

// This function has been replaced by generateSecureToken from encryption.server.ts

// Verify a user's account using their verification token
export async function verifyUser(token: string) {
  // Find the user with the given verification token
  const result = await query(
    'SELECT * FROM users WHERE verification_token = $1 AND verification_token_expires > NOW()',
    [token]
  );

  if (result.rows.length === 0) {
    return { success: false, message: "Invalid or expired verification token" };
  }

  // Update the user to mark them as verified and clear the verification token
  const updateResult = await query(
    'UPDATE users SET verification_status = \'verified\', verification_token = NULL, verification_token_expires = NULL, updated_at = NOW() WHERE id = $1 RETURNING *',
    [result.rows[0].id]
  );

  return { success: true, user: updateResult.rows[0] };
}

// Update user's license information
export async function updateUserLicense(userId: string, licenseData: { licenseNumber: string, licenseType: string, licenseExpiry: Date }) {
  const { licenseNumber, licenseType, licenseExpiry } = licenseData;

  // Encrypt sensitive license information
  const encryptedLicenseNumber = encrypt(licenseNumber);
  const encryptedLicenseType = encrypt(licenseType);

  const result = await query(
    'UPDATE users SET license_number = $1, license_type = $2, license_expiry = $3, updated_at = NOW() WHERE id = $4 RETURNING *',
    [encryptedLicenseNumber, encryptedLicenseType, licenseExpiry, userId]
  );

  // Decrypt the data for the returned user object
  const user = result.rows[0];

  if (user) {
    user.license_number = licenseNumber;
    user.license_type = licenseType;
  }

  return user;
}

// Generate a new MFA secret for a user
export async function generateMfaSecret(userId: string) {
  const secret = authenticator.generateSecret();

  // Encrypt the MFA secret
  const encryptedSecret = encrypt(secret);

  const result = await query(
    'UPDATE users SET mfa_secret = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
    [encryptedSecret, userId]
  );

  // Decrypt sensitive fields in the returned user object
  const user = result.rows[0];

  if (user) {
    // Decrypt email if it's encrypted
    if (isEncrypted(user.email)) {
      user.email = decrypt(user.email);
    }

    // Decrypt license information if present and encrypted
    if (user.license_number && isEncrypted(user.license_number)) {
      user.license_number = decrypt(user.license_number);
    }

    if (user.license_type && isEncrypted(user.license_type)) {
      user.license_type = decrypt(user.license_type);
    }

    // Use the original unencrypted secret for the return value
    user.mfa_secret = secret;
  }

  return { secret, user };
}

// Verify an MFA code
export function verifyMfaCode(secret: string, token: string) {
  // If the secret is encrypted, decrypt it first
  const actualSecret = isEncrypted(secret) ? decrypt(secret) : secret;
  return authenticator.verify({ token, secret: actualSecret });
}

// Enable MFA for a user
export async function enableMfa(userId: string) {
  // Generate recovery codes
  const recoveryCodes = generateRecoveryCodes();

  // Encrypt the recovery codes JSON string
  const encryptedRecoveryCodes = encrypt(JSON.stringify(recoveryCodes));

  const result = await query(
    'UPDATE users SET mfa_enabled = true, mfa_verified = true, mfa_recovery_codes = $1, updated_at = NOW() WHERE id = $2 RETURNING *',
    [encryptedRecoveryCodes, userId]
  );

  // Decrypt sensitive fields in the returned user object
  const user = result.rows[0];

  if (user) {
    // Decrypt email if it's encrypted
    if (isEncrypted(user.email)) {
      user.email = decrypt(user.email);
    }

    // Decrypt license information if present and encrypted
    if (user.license_number && isEncrypted(user.license_number)) {
      user.license_number = decrypt(user.license_number);
    }

    if (user.license_type && isEncrypted(user.license_type)) {
      user.license_type = decrypt(user.license_type);
    }

    // Decrypt MFA secret if present and encrypted
    if (user.mfa_secret && isEncrypted(user.mfa_secret)) {
      user.mfa_secret = decrypt(user.mfa_secret);
    }
  }

  return { user, recoveryCodes };
}

// Disable MFA for a user
export async function disableMfa(userId: string) {
  const result = await query(
    'UPDATE users SET mfa_enabled = false, mfa_verified = false, mfa_secret = NULL, mfa_recovery_codes = NULL, updated_at = NOW() WHERE id = $1 RETURNING *',
    [userId]
  );

  return result.rows[0];
}

// Generate recovery codes
export function generateRecoveryCodes(count = 10) {
  const codes = [];
  for (let i = 0; i < count; i++) {
    // Generate a random 10-character code
    const code = crypto.randomBytes(5).toString('hex').toUpperCase();
    // Format as XXXX-XXXX-XXXX
    codes.push(`${code.slice(0, 4)}-${code.slice(4, 8)}-${code.slice(8)}`);
  }
  return codes;
}

// Verify a recovery code
export async function verifyRecoveryCode(userId: string, code: string) {
  const result = await query('SELECT mfa_recovery_codes FROM users WHERE id = $1', [userId]);

  if (result.rows.length === 0 || !result.rows[0].mfa_recovery_codes) {
    return { success: false, message: "No recovery codes found" };
  }

  // Get the recovery codes - they might be encrypted
  const rawRecoveryCodes = result.rows[0].mfa_recovery_codes;

  // Decrypt if necessary
  let recoveryCodesJson;
  if (isEncrypted(rawRecoveryCodes)) {
    recoveryCodesJson = decrypt(rawRecoveryCodes);
  } else {
    recoveryCodesJson = rawRecoveryCodes;
  }

  const recoveryCodes = JSON.parse(recoveryCodesJson);
  const index = recoveryCodes.indexOf(code);

  if (index === -1) {
    return { success: false, message: "Invalid recovery code" };
  }

  // Remove the used recovery code
  recoveryCodes.splice(index, 1);

  // Encrypt the updated recovery codes
  const encryptedRecoveryCodes = encrypt(JSON.stringify(recoveryCodes));

  // Update the user's recovery codes
  await query(
    'UPDATE users SET mfa_recovery_codes = $1, updated_at = NOW() WHERE id = $2',
    [encryptedRecoveryCodes, userId]
  );

  return { success: true, remainingCodes: recoveryCodes.length };
}

// This function will be used to initialize the database schema
export async function initializeDatabase() {
  // Create users table
  await query(`
    CREATE TABLE IF NOT EXISTS users (
      id SERIAL PRIMARY KEY,
      email TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      role TEXT NOT NULL,
      name TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP,
      is_verified BOOLEAN DEFAULT FALSE,
      verification_token TEXT,
      verification_token_expires TIMESTAMP,
      license_number TEXT,
      license_type TEXT,
      license_expiry DATE
    )
  `);

  // Create jobs table
  await query(`
    CREATE TABLE IF NOT EXISTS jobs (
      id SERIAL PRIMARY KEY,
      owner_id INTEGER REFERENCES users(id) NOT NULL,
      title TEXT NOT NULL,
      description TEXT NOT NULL,
      aircraft_type TEXT NOT NULL,
      origin TEXT NOT NULL,
      destination TEXT NOT NULL,
      estimated_date DATE NOT NULL,
      budget DECIMAL(10, 2),
      status TEXT NOT NULL,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP
    )
  `);

  // Create job applications table
  await query(`
    CREATE TABLE IF NOT EXISTS job_applications (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      pilot_id INTEGER REFERENCES users(id) NOT NULL,
      status TEXT NOT NULL,
      proposed_fee DECIMAL(10, 2),
      notes TEXT,
      created_at TIMESTAMP NOT NULL,
      updated_at TIMESTAMP
    )
  `);

  console.log('Database schema initialized');
}
