import { db, query } from './db.server';
import { encrypt } from './encryption.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { User } from './users.server';
import { createRole, assignRoleToUser } from './business.server';

// Define ServiceProvider type
export interface ServiceProvider extends User {
  serviceProviderType: string;
  addons?: ServiceProviderAddon[];
}

// Define ServiceProviderAddon type
export interface ServiceProviderAddon {
  id: number;
  userId: number;
  addonType: 'boosted' | 'preferred' | 'featured';
  isActive: boolean;
  expiresAt?: Date;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Register a new service provider
 * @param userId User ID to convert to service provider
 * @param serviceProviderType Type of service provider
 * @returns Updated user as service provider
 */
export async function registerServiceProvider(
  userId: number,
  serviceProviderType: string
): Promise<ServiceProvider | null> {
  // Update the user to be a service provider
  const result = await query(
    `UPDATE users SET 
      account_type = 'service_provider',
      service_provider_type = $1,
      updated_at = NOW()
    WHERE id = $2 RETURNING *`,
    [serviceProviderType, userId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const user = result.rows[0];

  // Log service provider registration
  logSecurityEventAsync({
    user_id: userId,
    event_type: 'service_provider_registered',
    description: `User registered as service provider: ${serviceProviderType}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return {
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    createdAt: new Date(user.created_at),
    updatedAt: new Date(user.updated_at),
    accountType: user.account_type,
    serviceProviderType: user.service_provider_type
  };
}

/**
 * Add an employee/pilot to a service provider
 * @param serviceProviderId Service provider user ID
 * @param email Employee email
 * @param name Employee name
 * @param password Employee password
 * @param isAdmin Whether the employee is an admin
 * @returns Newly created employee
 */
export async function addEmployeeToServiceProvider(
  serviceProviderId: number,
  email: string,
  name: string,
  password: string,
  isAdmin: boolean = false
): Promise<User | null> {
  // Check if the service provider exists
  const providerResult = await query(
    'SELECT * FROM users WHERE id = $1 AND account_type = $2',
    [serviceProviderId, 'service_provider']
  );

  if (providerResult.rows.length === 0) {
    return null;
  }

  // Check if the user already exists
  const existingUserResult = await query('SELECT * FROM users WHERE email = $1', [email]);
  if (existingUserResult.rows.length > 0) {
    return null;
  }

  // Encrypt sensitive data
  const encryptedEmail = encrypt(email);
  const bcrypt = require('bcryptjs');
  const passwordHash = await bcrypt.hash(password, 10);

  // Create the user with service provider association
  const result = await query(
    `INSERT INTO users (
      email,
      password_hash,
      name,
      role,
      account_type,
      business_id,
      is_business_admin,
      service_provider_type,
      created_at,
      verification_status
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, NOW(), 'verified') RETURNING *`,
    [
      encryptedEmail,
      passwordHash,
      name,
      'pilot',
      'business_employee',
      null,
      isAdmin,
      providerResult.rows[0].service_provider_type
    ]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const user = result.rows[0];

  // Log employee addition
  logSecurityEventAsync({
    user_id: serviceProviderId,
    event_type: 'service_provider_employee_added',
    description: `Employee added to service provider: ${email}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return {
    id: user.id,
    email: email, // Use the original email
    name: user.name,
    role: user.role,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined,
    accountType: user.account_type,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    serviceProviderType: user.service_provider_type
  };
}

/**
 * Get all service providers
 * @param limit Maximum number of results to return
 * @returns Array of service providers
 */
export async function getAllServiceProviders(limit: number = 100): Promise<ServiceProvider[]> {
  const result = await query(
    `SELECT * FROM users 
     WHERE account_type = 'service_provider'
     ORDER BY name
     LIMIT $1`,
    [limit]
  );

  return result.rows.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined,
    accountType: user.account_type,
    serviceProviderType: user.service_provider_type
  }));
}

/**
 * Get employees of a service provider
 * @param serviceProviderId Service provider user ID
 * @returns Array of employees
 */
export async function getServiceProviderEmployees(serviceProviderId: number): Promise<User[]> {
  // First get the service provider to check if it exists
  const providerResult = await query(
    'SELECT * FROM users WHERE id = $1 AND account_type = $2',
    [serviceProviderId, 'service_provider']
  );

  if (providerResult.rows.length === 0) {
    return [];
  }

  // Get all employees with the same service_provider_type
  const result = await query(
    `SELECT * FROM users 
     WHERE service_provider_type = $1 
     AND id != $2
     AND account_type = 'business_employee'
     ORDER BY name`,
    [providerResult.rows[0].service_provider_type, serviceProviderId]
  );

  return result.rows.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined,
    accountType: user.account_type,
    businessId: user.business_id,
    isBusinessAdmin: user.is_business_admin,
    serviceProviderType: user.service_provider_type
  }));
}

/**
 * Add an addon to a service provider
 * @param userId Service provider user ID
 * @param addonType Type of addon
 * @param expiresAt Expiration date for the addon
 * @returns Newly created addon
 */
export async function addServiceProviderAddon(
  userId: number,
  addonType: 'boosted' | 'preferred' | 'featured',
  expiresAt?: Date
): Promise<ServiceProviderAddon | null> {
  // Check if the user is a service provider
  const userResult = await query(
    'SELECT * FROM users WHERE id = $1 AND account_type = $2',
    [userId, 'service_provider']
  );

  if (userResult.rows.length === 0) {
    return null;
  }

  // Create the addon
  const result = await query(
    `INSERT INTO service_provider_addons (
      user_id,
      addon_type,
      is_active,
      expires_at,
      created_at
    ) VALUES ($1, $2, $3, $4, NOW()) RETURNING *`,
    [userId, addonType, true, expiresAt || null]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const addon = result.rows[0];

  // Log addon creation
  logSecurityEventAsync({
    user_id: userId,
    event_type: 'service_provider_addon_added',
    description: `Addon added to service provider: ${addonType}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return {
    id: addon.id,
    userId: addon.user_id,
    addonType: addon.addon_type,
    isActive: addon.is_active,
    expiresAt: addon.expires_at ? new Date(addon.expires_at) : undefined,
    createdAt: new Date(addon.created_at),
    updatedAt: addon.updated_at ? new Date(addon.updated_at) : undefined
  };
}

/**
 * Get all addons for a service provider
 * @param userId Service provider user ID
 * @returns Array of addons
 */
export async function getServiceProviderAddons(userId: number): Promise<ServiceProviderAddon[]> {
  const result = await query(
    'SELECT * FROM service_provider_addons WHERE user_id = $1',
    [userId]
  );

  return result.rows.map(addon => ({
    id: addon.id,
    userId: addon.user_id,
    addonType: addon.addon_type,
    isActive: addon.is_active,
    expiresAt: addon.expires_at ? new Date(addon.expires_at) : undefined,
    createdAt: new Date(addon.created_at),
    updatedAt: addon.updated_at ? new Date(addon.updated_at) : undefined
  }));
}

/**
 * Deactivate an addon
 * @param addonId Addon ID
 * @returns Updated addon
 */
export async function deactivateServiceProviderAddon(addonId: number): Promise<ServiceProviderAddon | null> {
  const result = await query(
    `UPDATE service_provider_addons SET
      is_active = false,
      updated_at = NOW()
    WHERE id = $1 RETURNING *`,
    [addonId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const addon = result.rows[0];

  // Log addon deactivation
  logSecurityEventAsync({
    user_id: addon.user_id,
    event_type: 'service_provider_addon_deactivated',
    description: `Addon deactivated: ${addon.addon_type}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return {
    id: addon.id,
    userId: addon.user_id,
    addonType: addon.addon_type,
    isActive: addon.is_active,
    expiresAt: addon.expires_at ? new Date(addon.expires_at) : undefined,
    createdAt: new Date(addon.created_at),
    updatedAt: addon.updated_at ? new Date(addon.updated_at) : undefined
  };
}

/**
 * Get service providers with active addons
 * @param addonType Type of addon to filter by
 * @returns Array of service providers with the specified addon
 */
export async function getServiceProvidersWithAddon(addonType: string): Promise<ServiceProvider[]> {
  const result = await query(
    `SELECT u.* FROM users u
     JOIN service_provider_addons spa ON u.id = spa.user_id
     WHERE u.account_type = 'service_provider'
     AND spa.addon_type = $1
     AND spa.is_active = true
     AND (spa.expires_at IS NULL OR spa.expires_at > NOW())
     ORDER BY u.name`,
    [addonType]
  );

  return result.rows.map(user => ({
    id: user.id,
    email: user.email,
    name: user.name,
    role: user.role,
    createdAt: new Date(user.created_at),
    updatedAt: user.updated_at ? new Date(user.updated_at) : undefined,
    accountType: user.account_type,
    serviceProviderType: user.service_provider_type
  }));
}