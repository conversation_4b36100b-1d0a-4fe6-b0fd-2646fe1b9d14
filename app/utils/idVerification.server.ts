import { db } from './db.server';
import { logSecurityEvent } from './securityAudit.server';
import { VerificationStatus } from './verification.server';
import { IdDocumentType } from '~/types/verification';
import path from 'path';
import fs from 'fs';
import { promisify } from 'util';
import { v4 as uuidv4 } from 'uuid';

// Promisify fs functions
const mkdir = promisify(fs.mkdir);
const writeFile = promisify(fs.writeFile);

// Re-export the enum for backward compatibility
export { IdDocumentType };

// Define ID verification result
export interface IdVerificationResult {
  success: boolean;
  message: string;
  status: VerificationStatus;
  documentId?: number;
  verificationDate?: Date;
}

// Interface for ID document upload
export interface IdDocumentUpload {
  fileName: string;
  fileType: string;
  fileData: Buffer;
  documentType: IdDocumentType;
  userId: number;
  notes?: string;
  metadata?: Record<string, any>;
}

/**
 * Upload an ID document for verification
 */
export async function uploadIdDocument(
  document: IdDocumentUpload,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<IdVerificationResult> {
  try {
    // Create uploads directory if it doesn't exist
    const uploadsDir = path.join(process.cwd(), 'uploads', 'id_documents');
    const userDir = path.join(uploadsDir, document.userId.toString());

    await mkdir(userDir, { recursive: true });

    // Generate unique filename
    const uniqueId = uuidv4();
    const fileExtension = path.extname(document.fileName);
    const newFileName = `${uniqueId}${fileExtension}`;
    const filePath = path.join(userDir, newFileName);

    // Write file to disk
    await writeFile(filePath, document.fileData);

    // Save document record in database
    const result = await db
      .insertInto('documents')
      .values({
        user_id: document.userId,
        document_type: document.documentType,
        file_path: filePath,
        file_name: document.fileName,
        mime_type: document.fileType,
        uploaded_at: new Date(),
        verified: false,
        notes: document.notes || null,
        metadata: document.metadata ? JSON.stringify(document.metadata) : null,
        mobile_capture: document.metadata?.captureMethod === 'mobile'
      })
      .returning(['id', 'uploaded_at'])
      .executeTakeFirstOrThrow();

    // Update user's document_uploads field
    await db
      .updateTable('users')
      .set({
        document_uploads: db.fn.jsonArrayAppend(
          'document_uploads',
          JSON.stringify({
            id: result.id,
            document_type: document.documentType,
            file_name: document.fileName,
            uploaded_at: result.uploaded_at
          })
        ),
        identity_verification_status: VerificationStatus.IN_PROGRESS
      })
      .where('id', '=', document.userId)
      .execute();

    // Log the document upload
    await logSecurityEvent({
      user_id: document.userId,
      event_type: 'id_document_uploaded',
      description: `ID document uploaded: ${document.documentType} - ${document.fileName}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });

    // Create a verification request for this document
    await db
      .insertInto('verification_requests')
      .values({
        user_id: document.userId,
        request_type: 'identity',
        status: VerificationStatus.PENDING,
        submitted_at: new Date(),
        notes: `ID document uploaded: ${document.documentType}`
      })
      .execute();

    return {
      success: true,
      message: 'ID document uploaded successfully and pending verification',
      status: VerificationStatus.PENDING,
      documentId: result.id,
      verificationDate: new Date(result.uploaded_at)
    };
  } catch (error) {
    console.error('Error uploading ID document:', error);
    return {
      success: false,
      message: 'Failed to upload ID document',
      status: VerificationStatus.REJECTED
    };
  }
}

/**
 * Verify an ID document (admin function)
 */
export async function verifyIdDocument(
  documentId: number,
  adminId: number,
  verified: boolean,
  notes?: string,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<IdVerificationResult> {
  try {
    // Update document verification status
    const result = await db
      .updateTable('documents')
      .set({
        verified,
        verified_at: new Date(),
        verified_by: adminId,
        notes: notes || db.ref('notes')
      })
      .where('id', '=', documentId)
      .returning(['id', 'user_id', 'document_type', 'verified_at'])
      .executeTakeFirstOrThrow();

    const userId = result.user_id;

    // Update user's identity verification status
    if (verified) {
      await db
        .updateTable('users')
        .set({
          identity_verified: true,
          identity_verification_status: VerificationStatus.VERIFIED,
          identity_verification_date: new Date(),
          identity_verified_by: adminId
        })
        .where('id', '=', userId)
        .execute();
    } else {
      await db
        .updateTable('users')
        .set({
          identity_verification_status: VerificationStatus.REJECTED
        })
        .where('id', '=', userId)
        .execute();
    }

    // Log the document verification
    await logSecurityEvent({
      user_id: adminId,
      event_type: 'id_document_verified',
      description: `ID document ${documentId} verified: ${verified ? 'approved' : 'rejected'} by admin`,
      ip_address: ipAddress,
      user_agent: userAgent
    });

    // Update any related verification requests
    await db
      .updateTable('verification_requests')
      .set({
        status: verified ? VerificationStatus.VERIFIED : VerificationStatus.REJECTED,
        reviewed_at: new Date(),
        reviewed_by: adminId,
        admin_notes: notes || 'ID document verification completed'
      })
      .where('user_id', '=', userId)
      .where('request_type', '=', 'identity')
      .where('status', '=', VerificationStatus.PENDING)
      .execute();

    return {
      success: true,
      message: `ID document ${verified ? 'verified' : 'rejected'} successfully`,
      status: verified ? VerificationStatus.VERIFIED : VerificationStatus.REJECTED,
      documentId: result.id,
      verificationDate: new Date(result.verified_at)
    };
  } catch (error) {
    console.error('Error verifying ID document:', error);
    return {
      success: false,
      message: 'Failed to verify ID document',
      status: VerificationStatus.REJECTED
    };
  }
}

/**
 * Get ID verification status for a user
 */
export async function getIdVerificationStatus(userId: number): Promise<{
  status: VerificationStatus;
  verified: boolean;
  date?: Date;
  documents: any[];
}> {
  try {
    // Get user verification status
    const user = await db
      .selectFrom('users')
      .select(['identity_verified', 'identity_verification_status', 'identity_verification_date'])
      .where('id', '=', userId)
      .executeTakeFirst();

    if (!user) {
      return {
        status: VerificationStatus.PENDING,
        verified: false,
        documents: []
      };
    }

    // Get ID documents
    const documents = await db
      .selectFrom('documents')
      .select([
        'id', 
        'document_type', 
        'file_name', 
        'uploaded_at', 
        'verified', 
        'verified_at', 
        'mobile_capture', 
        'ocr_data', 
        'ocr_confidence'
      ])
      .where('user_id', '=', userId)
      .where(eb => eb.or([
        eb('document_type', '=', IdDocumentType.PASSPORT),
        eb('document_type', '=', IdDocumentType.DRIVERS_LICENSE),
        eb('document_type', '=', IdDocumentType.GOVERNMENT_ID)
      ]))
      .orderBy('uploaded_at', 'desc')
      .execute();

    return {
      status: user.identity_verification_status as VerificationStatus || VerificationStatus.PENDING,
      verified: user.identity_verified || false,
      date: user.identity_verification_date ? new Date(user.identity_verification_date) : undefined,
      documents
    };
  } catch (error) {
    console.error('Error getting ID verification status:', error);
    return {
      status: VerificationStatus.PENDING,
      verified: false,
      documents: []
    };
  }
}

/**
 * Helper function to convert base64 to buffer
 */
export function base64ToBuffer(base64String: string): Buffer {
  // Remove data URL prefix if present
  const base64Data = base64String.replace(/^data:image\/\w+;base64,/, '');
  return Buffer.from(base64Data, 'base64');
}

/**
 * Helper function to determine if a user has valid ID verification
 */
export async function hasValidIdVerification(userId: number): Promise<boolean> {
  const status = await getIdVerificationStatus(userId);
  return status.verified && status.status === VerificationStatus.VERIFIED;
}
