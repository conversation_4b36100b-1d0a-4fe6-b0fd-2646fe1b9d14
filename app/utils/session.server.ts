import { createCookieSessionStorage, redirect } from "@remix-run/node";
import bcrypt from "bcryptjs";
import { 
  getUserByEmail, 
  createUser, 
  getUser as getDbUser, 
  verifyMfaCode,
  verifyRecoveryCode
} from "./db.server";
import { logSecurityEvent, logSecurityEventAsync } from "./securityAudit.server";
import { isUserAdmin } from "./users.server";
import { userCache } from "./cache.server";

type LoginForm = {
  email: string;
  password: string;
};

type RegisterForm = {
  email: string;
  password: string;
  name: string;
  role: "owner" | "pilot" | "business" | "service_provider";
  licenseNumber?: string;
  licenseType?: string;
  licenseExpiry?: string;
  businessName?: string;
  businessDescription?: string;
  companyName?: string;
  serviceProviderType?: string;
};

// Ensure we have a session secret
if (!process.env.SESSION_SECRET && process.env.NODE_ENV === "production") {
  throw new Error("SESSION_SECRET must be set in production environment");
}

// In development, we can use a default secret, but in production, we require a strong secret
const sessionSecret = process.env.SESSION_SECRET || 
  (process.env.NODE_ENV === "production" 
    ? undefined // This will cause an error in production if SESSION_SECRET is not set
    : "default-secret-for-development-only-not-for-production");

const storage = createCookieSessionStorage({
  cookie: {
    name: "ferrypros_session",
    secure: process.env.NODE_ENV === "production",
    secrets: [sessionSecret],
    sameSite: "lax",
    path: "/",
    maxAge: 60 * 60 * 24 * 30, // 30 days
    httpOnly: true,
  },
});

export async function createUserSession(userId: string, redirectTo: string) {
  const session = await storage.getSession();
  session.set("userId", userId);
  const cookie = await storage.commitSession(session);

  // Use Remix's redirect function instead of creating a custom Response
  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": cookie
    }
  });
}

export async function getUserSession(request: Request) {
  return storage.getSession(request.headers.get("Cookie"));
}

export async function getSession(cookie: string | null) {
  return storage.getSession(cookie);
}

export async function getUserId(request: Request) {
  const session = await getUserSession(request);
  const userId = session.get("userId");
  if (!userId || typeof userId !== "string") return null;
  return userId;
}

export async function requireUserId(
  request: Request,
  redirectTo: string = new URL(request.url).pathname
) {
  const session = await getUserSession(request);
  const userId = session.get("userId");
  if (!userId || typeof userId !== "string") {
    const searchParams = new URLSearchParams([["redirectTo", redirectTo]]);
    throw redirect(`/login?${searchParams}`);
  }
  return userId;
}

export async function getUser(request: Request) {
  const userId = await getUserId(request);
  if (typeof userId !== "string") {
    return null;
  }

  try {
    // Use the cache to get or compute the user
    const user = await userCache.getOrCompute(`user:${userId}`, async () => {
      const user = await getDbUser(userId);
      return user;
    });

    // Check if user is impersonating a role
    const session = await getUserSession(request);
    const impersonatedRole = session.get("impersonatedRole");

    if (impersonatedRole && typeof impersonatedRole === "string") {
      // Return a modified user object with the impersonated role
      return {
        ...user,
        actualRole: user.role, // Store the actual role
        role: impersonatedRole, // Override with impersonated role
        isImpersonating: true
      };
    }

    return user;
  } catch {
    throw logout(request);
  }
}

export async function requireUser(
  request: Request,
  redirectTo: string = new URL(request.url).pathname
) {
  const userId = await requireUserId(request, redirectTo);

  try {
    // Use getUser which already uses the cache
    const user = await getUser(request);
    if (!user) {
      throw logout(request);
    }
    return user;
  } catch {
    throw logout(request);
  }
}

export async function requireAdmin(
  request: Request,
  redirectTo: string = "/dashboard"
) {
  const user = await requireUser(request);

  // Check if the user is an admin
  const isAdmin = await isUserAdmin(parseInt(user.id));

  if (!isAdmin) {
    // Log unauthorized access attempt (non-blocking)
    const ip_address = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       '127.0.0.1';
    const user_agent = request.headers.get('user-agent') || 'Unknown';

    logSecurityEventAsync({
      user_id: parseInt(user.id),
      event_type: 'unauthorized_access',
      description: `Unauthorized access attempt to admin area by user: ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    // Redirect to dashboard or another appropriate page
    throw redirect(redirectTo);
  }

  return user;
}

/**
 * Check if a user has a specific role or is an admin
 * @param request Request object
 * @param role Role to check for
 * @param redirectTo URL to redirect to if user doesn't have the required role
 * @returns User object if the user has the specified role or is an admin
 */
export async function requireRoleOrAdmin(
  request: Request,
  role: string,
  redirectTo: string = "/dashboard"
) {
  const user = await requireUser(request);

  // Check if the user has the specified role or is an admin
  const isAdmin = await isUserAdmin(parseInt(user.id));

  if (user.role !== role && !isAdmin) {
    // Log unauthorized access attempt (non-blocking)
    const ip_address = request.headers.get('x-forwarded-for') || 
                       request.headers.get('x-real-ip') || 
                       '127.0.0.1';
    const user_agent = request.headers.get('user-agent') || 'Unknown';

    logSecurityEventAsync({
      user_id: parseInt(user.id),
      event_type: 'unauthorized_access',
      description: `Unauthorized access attempt to ${role} area by user: ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    // Redirect to dashboard or another appropriate page
    throw redirect(redirectTo);
  }

  return user;
}

export async function logout(request: Request) {
  const session = await getUserSession(request);
  const userId = session.get("userId");

  // Get IP and user agent for security logging
  const ip_address = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request.headers.get('user-agent') || 'Unknown';

  // If we have a userId, log the logout event (non-blocking)
  if (userId && typeof userId === "string") {
    try {
      const user = await getDbUser(userId);
      if (user) {
        logSecurityEventAsync({
          user_id: parseInt(userId),
          event_type: 'logout',
          description: `User logged out: ${user.email}`,
          ip_address: ip_address.toString(),
          user_agent: user_agent.toString()
        });
      }
    } catch (error) {
      // If we can't get the user, still log the logout but without user details
      logSecurityEventAsync({
        user_id: parseInt(userId),
        event_type: 'logout',
        description: `User logged out (ID: ${userId})`,
        ip_address: ip_address.toString(),
        user_agent: user_agent.toString()
      });
    }
  }

  return redirect("/login", {
    headers: {
      "Set-Cookie": await storage.destroySession(session),
    },
  });
}

export async function register({ email, password, name, role, licenseNumber, licenseType, licenseExpiry, businessName, businessDescription, companyName, serviceProviderType }: RegisterForm, request?: Request) {
  // Get IP and user agent for security logging
  const ip_address = request?.headers.get('x-forwarded-for') || 
                     request?.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request?.headers.get('user-agent') || 'Unknown';

  const existingUser = await getUserByEmail(email);
  if (existingUser) {
    // Log failed registration attempt (non-blocking)
    logSecurityEventAsync({
      event_type: 'registration_failed',
      description: `Failed registration attempt: Email already exists (${email})`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { error: "A user already exists with this email" };
  }

  const passwordHash = await bcrypt.hash(password, 10);

  // Convert licenseExpiry string to Date if provided
  let licenseExpiryDate: Date | undefined;
  if (licenseExpiry) {
    licenseExpiryDate = new Date(licenseExpiry);
  }

  // Create user with verification token and license info if provided
  const user = await createUser({ 
    email, 
    passwordHash, 
    name, 
    role,
    licenseNumber,
    licenseType,
    licenseExpiry: licenseExpiryDate
  });

  // Log successful registration (non-blocking)
  logSecurityEventAsync({
    user_id: parseInt(user.id),
    event_type: 'registration_success',
    description: `New user registered: ${email} (${role})`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  // In a real application, we would send a verification email here
  // with the verification token

  return { user, verificationToken: user.verification_token };
}

// Define the possible return types for the login function
type LoginError = {
  error: string;
  verificationNeeded?: undefined;
  verificationToken?: undefined;
  user?: undefined;
  licenseInfoNeeded?: undefined;
  mfaRequired?: undefined;
};

type VerificationNeeded = {
  error: string;
  verificationNeeded: boolean;
  verificationToken: any;
  user?: undefined;
  licenseInfoNeeded?: undefined;
  mfaRequired?: undefined;
};

type LicenseInfoNeeded = {
  user: any;
  licenseInfoNeeded: boolean;
  verificationNeeded?: undefined;
  verificationToken?: undefined;
  error?: undefined;
  mfaRequired?: undefined;
};

type MfaRequired = {
  user: any;
  mfaRequired: boolean;
  verificationNeeded?: undefined;
  verificationToken?: undefined;
  error?: undefined;
  licenseInfoNeeded?: undefined;
};

type LoginSuccess = {
  user: any;
  verificationNeeded?: undefined;
  verificationToken?: undefined;
  error?: undefined;
  licenseInfoNeeded?: undefined;
  mfaRequired?: undefined;
};

type LoginResult = LoginError | VerificationNeeded | LicenseInfoNeeded | MfaRequired | LoginSuccess;

export async function login({ email, password }: LoginForm, request?: Request): Promise<LoginResult> {
  const user = await getUserByEmail(email);

  // Get IP and user agent for security logging
  const ip_address = request?.headers.get('x-forwarded-for') || 
                     request?.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request?.headers.get('user-agent') || 'Unknown';

  if (!user) {
    // Log failed login attempt (non-blocking)
    logSecurityEventAsync({
      event_type: 'login_failed',
      description: `Failed login attempt for email: ${email} (user not found)`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { error: "Invalid email or password" };
  }

  const isCorrectPassword = await bcrypt.compare(password, user.password_hash);
  if (!isCorrectPassword) {
    // Log failed login attempt (non-blocking)
    logSecurityEventAsync({
      user_id: parseInt(user.id),
      event_type: 'login_failed',
      description: `Failed login attempt for user: ${user.email} (incorrect password)`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { error: "Invalid email or password" };
  }

  // Check if the user is verified
  if (!user.verification_status || user.verification_status !== 'verified') {
    return { 
      error: "Your account is not verified. Please check your email for verification instructions.",
      verificationNeeded: true,
      verificationToken: user.verification_token
    };
  }

  // For pilots, check if license information is provided
  if (user.role === "pilot" && (!user.license_number || !user.license_type || !user.license_expiry)) {
    return {
      user,
      licenseInfoNeeded: true
    };
  }

  // Check if MFA is enabled for the user
  if (user.mfa_enabled) {
    // Log MFA required event (non-blocking)
    logSecurityEventAsync({
      user_id: parseInt(user.id),
      event_type: 'login_mfa_required',
      description: `MFA verification required for user: ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return {
      user,
      mfaRequired: true
    };
  }

  // Log successful login (non-blocking)
  logSecurityEventAsync({
    user_id: parseInt(user.id),
    event_type: 'login_success',
    description: `Successful login for user: ${user.email}`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  return { user };
}

// Verify MFA code during login
export async function verifyMfaLogin(userId: string, code: string, request?: Request): Promise<{ success: boolean; message?: string; user?: any }> {
  const user = await getDbUser(userId);

  // Get IP and user agent for security logging
  const ip_address = request?.headers.get('x-forwarded-for') || 
                     request?.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request?.headers.get('user-agent') || 'Unknown';

  if (!user) {
    // Log failed MFA verification (non-blocking)
    logSecurityEventAsync({
      event_type: 'mfa_verification_failed',
      description: `Failed MFA verification: User not found (ID: ${userId})`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { success: false, message: "User not found" };
  }

  if (!user.mfa_enabled || !user.mfa_secret) {
    // Log failed MFA verification (non-blocking)
    logSecurityEventAsync({
      user_id: parseInt(userId),
      event_type: 'mfa_verification_failed',
      description: `Failed MFA verification: MFA not enabled for user ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { success: false, message: "MFA not enabled for this user" };
  }

  // Try to verify the MFA code
  const isValidCode = verifyMfaCode(user.mfa_secret, code);

  if (isValidCode) {
    // Log successful MFA verification and login (non-blocking)
    logSecurityEventAsync({
      user_id: parseInt(userId),
      event_type: 'mfa_verification_success',
      description: `Successful MFA verification for user: ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    // Log successful login (non-blocking)
    logSecurityEventAsync({
      user_id: parseInt(userId),
      event_type: 'login_success',
      description: `Successful login with MFA for user: ${user.email}`,
      ip_address: ip_address.toString(),
      user_agent: user_agent.toString()
    });

    return { success: true, user };
  }

  // Log failed MFA verification (non-blocking)
  logSecurityEventAsync({
    user_id: parseInt(userId),
    event_type: 'mfa_verification_failed',
    description: `Failed MFA verification: Invalid code for user ${user.email}`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  return { success: false, message: "Invalid MFA code" };
}

// Verify recovery code during login
export async function verifyRecoveryLogin(userId: string, code: string, request?: Request): Promise<{ success: boolean; message?: string; user?: any }> {
  // Get IP and user agent for security logging
  const ip_address = request?.headers.get('x-forwarded-for') || 
                     request?.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request?.headers.get('user-agent') || 'Unknown';

  const result = await verifyRecoveryCode(userId, code);

  if (result.success) {
    const user = await getDbUser(userId);

    if (user) {
      // Log successful recovery code verification and login (non-blocking)
      logSecurityEventAsync({
        user_id: parseInt(userId),
        event_type: 'recovery_code_success',
        description: `Successful recovery code verification for user: ${user.email}. ${result.remainingCodes} codes remaining.`,
        ip_address: ip_address.toString(),
        user_agent: user_agent.toString()
      });

      // Log successful login (non-blocking)
      logSecurityEventAsync({
        user_id: parseInt(userId),
        event_type: 'login_success',
        description: `Successful login with recovery code for user: ${user.email}`,
        ip_address: ip_address.toString(),
        user_agent: user_agent.toString()
      });

      return { success: true, user, message: `Recovery code accepted. You have ${result.remainingCodes} recovery codes remaining.` };
    }
  }

  // Log failed recovery code verification (non-blocking)
  logSecurityEventAsync({
    user_id: parseInt(userId),
    event_type: 'recovery_code_failed',
    description: `Failed recovery code verification: ${result.message || 'Invalid code'}`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  return { success: false, message: result.message };
}

/**
 * Start role impersonation for admin users
 * @param request Request object
 * @param role Role to impersonate
 * @returns Redirect to the dashboard with impersonation active
 */
export async function startImpersonation(request: Request, role: string) {
  const user = await requireUser(request);

  // Verify the user is an admin
  const isAdmin = await isUserAdmin(parseInt(user.id));
  if (!isAdmin) {
    throw new Error("Only admin users can impersonate roles");
  }

  // Verify the role is valid
  if (!['pilot', 'owner', 'business', 'service_provider'].includes(role)) {
    throw new Error("Invalid role for impersonation");
  }

  // Get IP and user agent for security logging
  const ip_address = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request.headers.get('user-agent') || 'Unknown';

  // Log impersonation start
  logSecurityEvent({
    user_id: parseInt(user.id),
    event_type: 'role_impersonation_start',
    description: `Admin ${user.email} started impersonating role: ${role}`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  // Set impersonation in session
  const session = await getUserSession(request);
  session.set("impersonatedRole", role);

  // Commit the session and redirect to dashboard
  return redirect("/dashboard", {
    headers: {
      "Set-Cookie": await storage.commitSession(session)
    }
  });
}

/**
 * End role impersonation and restore admin role
 * @param request Request object
 * @returns Redirect to the dashboard with admin role restored
 */
export async function endImpersonation(request: Request) {
  const user = await requireUser(request);

  // Get IP and user agent for security logging
  const ip_address = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     '127.0.0.1';
  const user_agent = request.headers.get('user-agent') || 'Unknown';

  // Log impersonation end
  logSecurityEvent({
    user_id: parseInt(user.id),
    event_type: 'role_impersonation_end',
    description: `User ${user.email} ended role impersonation`,
    ip_address: ip_address.toString(),
    user_agent: user_agent.toString()
  });

  // Remove impersonation from session
  const session = await getUserSession(request);
  session.unset("impersonatedRole");

  // Commit the session and redirect to dashboard
  return redirect("/dashboard", {
    headers: {
      "Set-Cookie": await storage.commitSession(session)
    }
  });
}

/**
 * Check if a user is currently impersonating a role
 * @param request Request object
 * @returns Boolean indicating if user is impersonating a role
 */
export async function isImpersonating(request: Request): Promise<boolean> {
  const session = await getUserSession(request);
  const impersonatedRole = session.get("impersonatedRole");
  return impersonatedRole !== undefined && typeof impersonatedRole === "string";
}
