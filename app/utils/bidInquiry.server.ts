import { query } from './db.server';
import { userHasPermission } from './business.server';
import { logSecurityEventAsync } from './securityAudit.server';
import { sendNotification } from './notifications.server';

// Define BidInquiry type
export interface BidInquiry {
  id: number;
  jobId: number;
  pilotId: number;
  status: 'pending' | 'approved' | 'rejected';
  notes?: string;
  createdAt: Date;
  updatedAt?: Date;
}

/**
 * Create a bid inquiry
 * @param jobId Job ID
 * @param pilotId Pilot user ID
 * @param notes Optional notes for the inquiry
 * @returns Newly created bid inquiry
 */
export async function createBidInquiry(
  jobId: number,
  pilotId: number,
  notes?: string
): Promise<BidInquiry | null> {
  // Check if there's already a bid inquiry for this job and pilot
  const existingResult = await query(
    'SELECT * FROM bid_inquiries WHERE job_id = $1 AND pilot_id = $2',
    [jobId, pilotId]
  );

  if (existingResult.rows.length > 0) {
    return mapBidInquiry(existingResult.rows[0]);
  }

  // Create a new bid inquiry
  const result = await query(
    `INSERT INTO bid_inquiries (
      job_id,
      pilot_id,
      status,
      notes,
      created_at
    ) VALUES ($1, $2, $3, $4, NOW()) RETURNING *`,
    [jobId, pilotId, 'pending', notes || null]
  );

  if (result.rows.length === 0) {
    return null;
  }

  // Get the job owner to send notification
  const jobResult = await query(
    'SELECT owner_id FROM jobs WHERE id = $1',
    [jobId]
  );

  if (jobResult.rows.length > 0) {
    const ownerId = jobResult.rows[0].owner_id;

    // Check if the owner is part of a business
    const ownerResult = await query(
      'SELECT business_id FROM users WHERE id = $1',
      [ownerId]
    );

    if (ownerResult.rows.length > 0 && ownerResult.rows[0].business_id) {
      const businessId = ownerResult.rows[0].business_id;

      // Find users with bid approval permission in the business
      const approversResult = await query(
        `SELECT u.id FROM users u
         JOIN user_roles ur ON u.id = ur.user_id
         JOIN role_permissions rp ON ur.role_id = rp.role_id
         JOIN permissions p ON rp.permission_id = p.id
         WHERE u.business_id = $1 AND p.name = $2`,
        [businessId, 'approve_bid_inquiry']
      );

      // Send notifications to all potential approvers
      for (const approver of approversResult.rows) {
        await sendNotification({
          userId: approver.id,
          message: `A pilot has requested to bid on a job`,
          type: 'bid_inquiry_request',
          metadata: {
            jobId,
            pilotId
          }
        });
      }
    } else {
      // If owner is not part of a business, send notification directly to owner
      await sendNotification({
        userId: ownerId,
        message: `A pilot has requested to bid on your job`,
        type: 'bid_inquiry_request',
        metadata: {
          jobId,
          pilotId
        }
      });
    }
  }

  // Log the bid inquiry
  logSecurityEventAsync({
    user_id: pilotId,
    event_type: 'bid_inquiry_created',
    description: `Bid inquiry created for job ID: ${jobId}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return mapBidInquiry(result.rows[0]);
}

/**
 * Approve or reject a bid inquiry
 * @param inquiryId Inquiry ID
 * @param userId User ID of the approver
 * @param approved Whether the inquiry is approved or rejected
 * @param notes Optional notes for the decision
 * @returns Updated bid inquiry
 */
export async function processBidInquiry(
  inquiryId: number,
  userId: number,
  approved: boolean,
  notes?: string
): Promise<BidInquiry | null> {
  // Get the inquiry to check job ownership
  const inquiryResult = await query(
    'SELECT * FROM bid_inquiries WHERE id = $1',
    [inquiryId]
  );

  if (inquiryResult.rows.length === 0) {
    return null;
  }

  const inquiry = inquiryResult.rows[0];

  // Get the job to check ownership
  const jobResult = await query(
    'SELECT owner_id FROM jobs WHERE id = $1',
    [inquiry.job_id]
  );

  if (jobResult.rows.length === 0) {
    return null;
  }

  const ownerId = jobResult.rows[0].owner_id;

  // Check if the user is the job owner or has permission to approve bid inquiries
  const isOwner = userId === ownerId;
  const hasPermission = await userHasPermission(userId, 'approve_bid_inquiry');

  if (!isOwner && !hasPermission) {
    return null;
  }

  // Update the bid inquiry
  const result = await query(
    `UPDATE bid_inquiries SET
      status = $1,
      notes = CASE WHEN $2 IS NOT NULL THEN $2 ELSE notes END,
      updated_at = NOW()
    WHERE id = $3 RETURNING *`,
    [approved ? 'approved' : 'rejected', notes, inquiryId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  const updatedInquiry = result.rows[0];

  // Send notification to the pilot
  await sendNotification({
    userId: updatedInquiry.pilot_id,
    message: `Your bid inquiry has been ${approved ? 'approved' : 'rejected'}`,
    type: 'bid_inquiry_processed',
    metadata: {
      jobId: updatedInquiry.job_id,
      approved
    }
  });

  // Log the decision
  logSecurityEventAsync({
    user_id: userId,
    event_type: approved ? 'bid_inquiry_approved' : 'bid_inquiry_rejected',
    description: `Bid inquiry ${approved ? 'approved' : 'rejected'} for job ID: ${updatedInquiry.job_id}`,
    ip_address: '127.0.0.1',
    user_agent: 'Server'
  });

  return mapBidInquiry(updatedInquiry);
}

/**
 * Get a bid inquiry by ID
 * @param inquiryId Inquiry ID
 * @returns Bid inquiry or null if not found
 */
export async function getBidInquiryById(inquiryId: number): Promise<BidInquiry | null> {
  const result = await query(
    'SELECT * FROM bid_inquiries WHERE id = $1',
    [inquiryId]
  );

  if (result.rows.length === 0) {
    return null;
  }

  return mapBidInquiry(result.rows[0]);
}

/**
 * Get bid inquiries for a job
 * @param jobId Job ID
 * @param status Optional status filter
 * @returns Array of bid inquiries
 */
export async function getBidInquiriesForJob(
  jobId: number,
  status?: 'pending' | 'approved' | 'rejected'
): Promise<BidInquiry[]> {
  let query = 'SELECT * FROM bid_inquiries WHERE job_id = $1';
  const params = [jobId];

  if (status) {
    query += ' AND status = $2';
    params.push(status);
  }

  query += ' ORDER BY created_at DESC';

  const result = await query(query, params);

  return result.rows.map(mapBidInquiry);
}

/**
 * Get bid inquiries created by a pilot
 * @param pilotId Pilot user ID
 * @param status Optional status filter
 * @returns Array of bid inquiries
 */
export async function getBidInquiriesByPilot(
  pilotId: number,
  status?: 'pending' | 'approved' | 'rejected'
): Promise<BidInquiry[]> {
  let queryText = 'SELECT * FROM bid_inquiries WHERE pilot_id = $1';
  const params = [pilotId];

  if (status) {
    queryText += ' AND status = $2';
    params.push(status);
  }

  queryText += ' ORDER BY created_at DESC';

  const result = await query(queryText, params);

  return result.rows.map(mapBidInquiry);
}

/**
 * Get pending bid inquiries for a business
 * @param businessId Business ID
 * @returns Array of pending bid inquiries
 */
export async function getPendingBidInquiriesForBusiness(businessId: number): Promise<BidInquiry[]> {
  const result = await query(
    `SELECT bi.* FROM bid_inquiries bi
     JOIN jobs j ON bi.job_id = j.id
     JOIN users u ON j.owner_id = u.id
     WHERE u.business_id = $1 AND bi.status = $2
     ORDER BY bi.created_at DESC`,
    [businessId, 'pending']
  );

  return result.rows.map(mapBidInquiry);
}

/**
 * Check if a pilot can bid on a job
 * @param jobId Job ID
 * @param pilotId Pilot user ID
 * @returns True if the pilot can bid, false otherwise
 */
export async function canPilotBidOnJob(jobId: number, pilotId: number): Promise<boolean> {
  // Check if there's an approved bid inquiry
  const inquiryResult = await query(
    'SELECT * FROM bid_inquiries WHERE job_id = $1 AND pilot_id = $2 AND status = $3',
    [jobId, pilotId, 'approved']
  );

  return inquiryResult.rows.length > 0;
}

/**
 * Map database row to BidInquiry interface
 * @param row Database row
 * @returns BidInquiry object
 */
function mapBidInquiry(row: any): BidInquiry {
  return {
    id: row.id,
    jobId: row.job_id,
    pilotId: row.pilot_id,
    status: row.status,
    notes: row.notes,
    createdAt: new Date(row.created_at),
    updatedAt: row.updated_at ? new Date(row.updated_at) : undefined
  };
}