import { pool, query } from './db.server';
import { Job } from './jobs.server';
import { createNotification, NotificationType } from './notifications.server';
import { CriteriaOperator, CriteriaField } from '~/types/verification';

// Interface for notification watcher
export interface NotificationWatcher {
  id?: number;
  user_id: number;
  name: string;
  enabled: boolean;
  created_at?: Date;
  updated_at?: Date;
  criteria?: NotificationWatcherCriteria[];
}

// Interface for notification watcher criteria
export interface NotificationWatcherCriteria {
  id?: number;
  watcher_id: number;
  field_name: string;
  operator: string;
  value: string;
  created_at?: Date;
  updated_at?: Date;
}

// Re-export the enums for backward compatibility
export { CriteriaOperator, CriteriaField };

/**
 * Create a new notification watcher
 * @param watcher The notification watcher to create
 * @returns The created notification watcher
 */
export async function createNotificationWatcher(watcher: NotificationWatcher) {
  try {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Insert the watcher
      const watcherResult = await client.query(
        `INSERT INTO notification_watchers (user_id, name, enabled, created_at)
         VALUES ($1, $2, $3, NOW())
         RETURNING *`,
        [watcher.user_id, watcher.name, watcher.enabled]
      );
      
      const newWatcher = watcherResult.rows[0];
      
      // Insert criteria if provided
      if (watcher.criteria && watcher.criteria.length > 0) {
        for (const criterion of watcher.criteria) {
          await client.query(
            `INSERT INTO notification_watcher_criteria (watcher_id, field_name, operator, value, created_at)
             VALUES ($1, $2, $3, $4, NOW())`,
            [newWatcher.id, criterion.field_name, criterion.operator, criterion.value]
          );
        }
      }
      
      await client.query('COMMIT');
      
      // Return the created watcher with criteria
      return {
        success: true,
        watcher: await getNotificationWatcherById(newWatcher.id)
      };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error creating notification watcher:', error);
      return {
        success: false,
        message: 'Failed to create notification watcher'
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error connecting to database:', error);
    return {
      success: false,
      message: 'Database connection error'
    };
  }
}

/**
 * Get a notification watcher by ID
 * @param watcherId The notification watcher ID
 * @returns The notification watcher with its criteria
 */
export async function getNotificationWatcherById(watcherId: number) {
  try {
    // Get the watcher
    const watcherResult = await query(
      'SELECT * FROM notification_watchers WHERE id = $1',
      [watcherId]
    );
    
    if (watcherResult.rows.length === 0) {
      return null;
    }
    
    const watcher = watcherResult.rows[0];
    
    // Get the criteria
    const criteriaResult = await query(
      'SELECT * FROM notification_watcher_criteria WHERE watcher_id = $1',
      [watcherId]
    );
    
    watcher.criteria = criteriaResult.rows;
    
    return watcher;
  } catch (error) {
    console.error('Error getting notification watcher:', error);
    return null;
  }
}

/**
 * Get notification watchers for a user
 * @param userId The user ID
 * @param includeDisabled Whether to include disabled watchers (default: false)
 * @returns The notification watchers with their criteria
 */
export async function getNotificationWatchersForUser(userId: number, includeDisabled: boolean = false) {
  try {
    // Build the query
    let queryText = 'SELECT * FROM notification_watchers WHERE user_id = $1';
    const queryParams = [userId];
    
    if (!includeDisabled) {
      queryText += ' AND enabled = true';
    }
    
    queryText += ' ORDER BY created_at DESC';
    
    // Get the watchers
    const watchersResult = await query(queryText, queryParams);
    
    const watchers = watchersResult.rows;
    
    // Get the criteria for each watcher
    for (const watcher of watchers) {
      const criteriaResult = await query(
        'SELECT * FROM notification_watcher_criteria WHERE watcher_id = $1',
        [watcher.id]
      );
      
      watcher.criteria = criteriaResult.rows;
    }
    
    return {
      success: true,
      watchers
    };
  } catch (error) {
    console.error('Error getting notification watchers for user:', error);
    return {
      success: false,
      message: 'Failed to get notification watchers'
    };
  }
}

/**
 * Update a notification watcher
 * @param watcherId The notification watcher ID
 * @param updates The updates to apply
 * @returns The updated notification watcher
 */
export async function updateNotificationWatcher(watcherId: number, updates: Partial<NotificationWatcher>) {
  try {
    const client = await pool.connect();
    
    try {
      await client.query('BEGIN');
      
      // Update the watcher
      if (updates.name !== undefined || updates.enabled !== undefined) {
        const updateFields = [];
        const updateValues = [];
        let paramIndex = 1;
        
        if (updates.name !== undefined) {
          updateFields.push(`name = $${paramIndex}`);
          updateValues.push(updates.name);
          paramIndex++;
        }
        
        if (updates.enabled !== undefined) {
          updateFields.push(`enabled = $${paramIndex}`);
          updateValues.push(updates.enabled);
          paramIndex++;
        }
        
        updateFields.push(`updated_at = NOW()`);
        
        updateValues.push(watcherId);
        
        await client.query(
          `UPDATE notification_watchers 
           SET ${updateFields.join(', ')} 
           WHERE id = $${paramIndex}`,
          updateValues
        );
      }
      
      // Update criteria if provided
      if (updates.criteria !== undefined) {
        // Delete existing criteria
        await client.query(
          'DELETE FROM notification_watcher_criteria WHERE watcher_id = $1',
          [watcherId]
        );
        
        // Insert new criteria
        for (const criterion of updates.criteria) {
          await client.query(
            `INSERT INTO notification_watcher_criteria (watcher_id, field_name, operator, value, created_at)
             VALUES ($1, $2, $3, $4, NOW())`,
            [watcherId, criterion.field_name, criterion.operator, criterion.value]
          );
        }
      }
      
      await client.query('COMMIT');
      
      // Return the updated watcher
      return {
        success: true,
        watcher: await getNotificationWatcherById(watcherId)
      };
    } catch (error) {
      await client.query('ROLLBACK');
      console.error('Error updating notification watcher:', error);
      return {
        success: false,
        message: 'Failed to update notification watcher'
      };
    } finally {
      client.release();
    }
  } catch (error) {
    console.error('Error connecting to database:', error);
    return {
      success: false,
      message: 'Database connection error'
    };
  }
}

/**
 * Delete a notification watcher
 * @param watcherId The notification watcher ID
 * @returns Whether the deletion was successful
 */
export async function deleteNotificationWatcher(watcherId: number) {
  try {
    // Delete the watcher (this will cascade to criteria due to ON DELETE CASCADE)
    const result = await query(
      'DELETE FROM notification_watchers WHERE id = $1',
      [watcherId]
    );
    
    return {
      success: result.rowCount > 0,
      message: result.rowCount > 0 ? 'Notification watcher deleted' : 'Notification watcher not found'
    };
  } catch (error) {
    console.error('Error deleting notification watcher:', error);
    return {
      success: false,
      message: 'Failed to delete notification watcher'
    };
  }
}

/**
 * Check if a job matches a notification watcher's criteria
 * @param job The job to check
 * @param watcher The notification watcher with criteria
 * @returns Whether the job matches the criteria
 */
export function jobMatchesWatcherCriteria(job: Job, watcher: NotificationWatcher): boolean {
  // If the watcher has no criteria, it matches all jobs
  if (!watcher.criteria || watcher.criteria.length === 0) {
    return true;
  }
  
  // Check each criterion
  for (const criterion of watcher.criteria) {
    const fieldValue = job[criterion.field_name as keyof Job];
    
    // Skip if the field doesn't exist in the job
    if (fieldValue === undefined) {
      continue;
    }
    
    // Convert field value to string for comparison
    const fieldValueStr = fieldValue.toString();
    
    // Check based on operator
    switch (criterion.operator) {
      case CriteriaOperator.EQUALS:
        if (fieldValueStr !== criterion.value) {
          return false;
        }
        break;
        
      case CriteriaOperator.CONTAINS:
        if (!fieldValueStr.toLowerCase().includes(criterion.value.toLowerCase())) {
          return false;
        }
        break;
        
      case CriteriaOperator.GREATER_THAN:
        // Only apply to numeric fields
        if (criterion.field_name === CriteriaField.BUDGET) {
          const numValue = parseFloat(fieldValueStr);
          const criterionValue = parseFloat(criterion.value);
          
          if (isNaN(numValue) || isNaN(criterionValue) || numValue <= criterionValue) {
            return false;
          }
        }
        break;
        
      case CriteriaOperator.LESS_THAN:
        // Only apply to numeric fields
        if (criterion.field_name === CriteriaField.BUDGET) {
          const numValue = parseFloat(fieldValueStr);
          const criterionValue = parseFloat(criterion.value);
          
          if (isNaN(numValue) || isNaN(criterionValue) || numValue >= criterionValue) {
            return false;
          }
        }
        break;
        
      case CriteriaOperator.BETWEEN:
        // Only apply to numeric fields or dates
        if (criterion.field_name === CriteriaField.BUDGET) {
          const numValue = parseFloat(fieldValueStr);
          const [min, max] = criterion.value.split(',').map(v => parseFloat(v.trim()));
          
          if (isNaN(numValue) || isNaN(min) || isNaN(max) || numValue < min || numValue > max) {
            return false;
          }
        } else if (criterion.field_name === CriteriaField.ESTIMATED_DATE) {
          const dateValue = new Date(fieldValueStr);
          const [minStr, maxStr] = criterion.value.split(',').map(v => v.trim());
          const minDate = new Date(minStr);
          const maxDate = new Date(maxStr);
          
          if (isNaN(dateValue.getTime()) || isNaN(minDate.getTime()) || isNaN(maxDate.getTime()) || 
              dateValue < minDate || dateValue > maxDate) {
            return false;
          }
        }
        break;
    }
  }
  
  // If we get here, all criteria matched
  return true;
}

/**
 * Process a new job against all notification watchers
 * @param job The new job
 */
export async function processJobForNotificationWatchers(job: Job) {
  try {
    // Get all enabled notification watchers
    const watchersResult = await query(
      'SELECT * FROM notification_watchers WHERE enabled = true'
    );
    
    const watchers = watchersResult.rows;
    
    // Get criteria for each watcher
    for (const watcher of watchers) {
      const criteriaResult = await query(
        'SELECT * FROM notification_watcher_criteria WHERE watcher_id = $1',
        [watcher.id]
      );
      
      watcher.criteria = criteriaResult.rows;
      
      // Check if the job matches the watcher's criteria
      if (jobMatchesWatcherCriteria(job, watcher)) {
        // Create a notification for the user
        await createNotification({
          userId: watcher.user_id,
          jobId: job.id,
          message: `New job matching your criteria: ${job.title}`,
          type: NotificationType.JOB_CREATED,
          read: false,
          data: {
            job_title: job.title,
            job_type: job.aircraft_type,
            watcher_name: watcher.name
          }
        });
      }
    }
    
    return {
      success: true,
      message: 'Job processed for notification watchers'
    };
  } catch (error) {
    console.error('Error processing job for notification watchers:', error);
    return {
      success: false,
      message: 'Failed to process job for notification watchers'
    };
  }
}