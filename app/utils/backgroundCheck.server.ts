import { db } from './db.server';
import { logSecurityEvent } from './securityAudit.server';
import axios from 'axios';
import { VerificationStatus } from './verification.server';
import { BackgroundCheckType } from '~/types/verification';

// Re-export the enum for backward compatibility
export { BackgroundCheckType };

// Define background check result
export interface BackgroundCheckResult {
  success: boolean;
  message: string;
  status: VerificationStatus;
  checkType: BackgroundCheckType;
  checkDate: Date;
  reportId?: string;
  reportUrl?: string;
  details?: any;
}

/**
 * Initiate a background check for a pilot
 * This integrates with a third-party background check service
 */
export async function initiateBackgroundCheck(
  userId: number,
  checkType: BackgroundCheckType = BackgroundCheckType.COMPREHENSIVE,
  ipAddress: string = '127.0.0.1',
  userAgent: string = 'Server'
): Promise<BackgroundCheckResult> {
  try {
    // Get user information needed for background check
    const user = await db
      .selectFrom('users')
      .select(['id', 'name', 'email'])
      .where('id', '=', userId)
      .executeTakeFirst();

    if (!user) {
      return {
        success: false,
        message: 'User not found',
        status: VerificationStatus.REJECTED,
        checkType,
        checkDate: new Date()
      };
    }

    // Update user's background check status to in progress
    await db
      .updateTable('users')
      .set({
        background_check_status: VerificationStatus.IN_PROGRESS,
      })
      .where('id', '=', userId)
      .execute();

    // Log the background check initiation
    await logSecurityEvent({
      user_id: userId,
      event_type: 'background_check_initiated',
      description: `Background check initiated: ${checkType}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });

    // In a production environment, this would call a real background check API
    // For now, we'll simulate a successful API call with a real service structure
    
    // Example API call to a background check service (commented out)
    /*
    const response = await axios.post('https://api.backgroundcheckservice.com/v1/checks', {
      type: checkType,
      subject: {
        name: user.name,
        email: user.email,
        user_id: user.id.toString()
      },
      callback_url: `${process.env.APP_URL}/api/background-check/callback`,
      reference_id: `user_${user.id}_${Date.now()}`
    }, {
      headers: {
        'Authorization': `Bearer ${process.env.BACKGROUND_CHECK_API_KEY}`,
        'Content-Type': 'application/json'
      }
    });
    
    const reportId = response.data.report_id;
    const reportUrl = response.data.report_url;
    */
    
    // For demo purposes, we'll simulate a successful check
    const reportId = `BC-${Date.now()}-${userId}`;
    const reportUrl = `https://example.com/background-checks/${reportId}`;
    
    // Update user's background check status and store report information
    await db
      .updateTable('users')
      .set({
        background_check_status: VerificationStatus.VERIFIED,
        background_check_date: new Date(),
        verification_notes: db.fn.coalesce(
          db.ref('verification_notes'),
          ''
        ).concat(`\nBackground check completed: ${checkType} - Report ID: ${reportId}`)
      })
      .where('id', '=', userId)
      .execute();
    
    // Create a document record for the background check report
    await db
      .insertInto('documents')
      .values({
        user_id: userId,
        document_type: 'background_check',
        file_path: reportUrl,
        file_name: `Background Check Report - ${checkType}`,
        mime_type: 'application/pdf',
        uploaded_at: new Date(),
        verified: true,
        verified_at: new Date(),
        notes: `Automated background check report: ${checkType}`
      })
      .execute();
    
    // Log the successful background check
    await logSecurityEvent({
      user_id: userId,
      event_type: 'background_check_completed',
      description: `Background check completed successfully: ${checkType} - Report ID: ${reportId}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: true,
      message: 'Background check completed successfully',
      status: VerificationStatus.VERIFIED,
      checkType,
      checkDate: new Date(),
      reportId,
      reportUrl,
      details: {
        checkType,
        completedAt: new Date(),
        status: VerificationStatus.VERIFIED
      }
    };
  } catch (error) {
    console.error('Error initiating background check:', error);
    
    // Update user's background check status to rejected on error
    await db
      .updateTable('users')
      .set({
        background_check_status: VerificationStatus.REJECTED,
        background_check_date: new Date(),
        verification_notes: db.fn.coalesce(
          db.ref('verification_notes'),
          ''
        ).concat('\nBackground check failed due to an error')
      })
      .where('id', '=', userId)
      .execute();
    
    // Log the failed background check
    await logSecurityEvent({
      user_id: userId,
      event_type: 'background_check_failed',
      description: `Background check failed: ${error.message || 'Unknown error'}`,
      ip_address: ipAddress,
      user_agent: userAgent
    });
    
    return {
      success: false,
      message: 'An error occurred during background check',
      status: VerificationStatus.REJECTED,
      checkType,
      checkDate: new Date()
    };
  }
}

/**
 * Get background check status for a user
 */
export async function getBackgroundCheckStatus(userId: number): Promise<{
  status: VerificationStatus;
  date?: Date;
  reportId?: string;
  reportUrl?: string;
}> {
  const user = await db
    .selectFrom('users')
    .select(['background_check_status', 'background_check_date'])
    .where('id', '=', userId)
    .executeTakeFirst();
  
  if (!user) {
    return { status: VerificationStatus.PENDING };
  }
  
  // Get the most recent background check document if available
  const document = await db
    .selectFrom('documents')
    .select(['file_path', 'notes'])
    .where('user_id', '=', userId)
    .where('document_type', '=', 'background_check')
    .orderBy('uploaded_at', 'desc')
    .limit(1)
    .executeTakeFirst();
  
  // Extract report ID from notes if available
  let reportId;
  if (document?.notes) {
    const match = document.notes.match(/Report ID: (BC-[\d]+-[\d]+)/);
    if (match && match[1]) {
      reportId = match[1];
    }
  }
  
  return {
    status: user.background_check_status as VerificationStatus || VerificationStatus.PENDING,
    date: user.background_check_date ? new Date(user.background_check_date) : undefined,
    reportId,
    reportUrl: document?.file_path
  };
}