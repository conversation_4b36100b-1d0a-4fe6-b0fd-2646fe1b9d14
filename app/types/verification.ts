// Shared types for verification that can be used by both client and server code

// Define ID document types
export enum IdDocumentType {
  PASSPORT = 'passport',
  DRIVERS_LICENSE = 'drivers_license',
  GOVERNMENT_ID = 'government_id',
  PILOT_LICENSE = 'pilot_license'
}

// Valid operators for notification watcher criteria
export enum CriteriaOperator {
  EQUALS = 'equals',
  CONTAINS = 'contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  BETWEEN = 'between'
}

// Valid fields for notification watcher criteria
export enum CriteriaField {
  AIRCRAFT_TYPE = 'aircraft_type',
  ORIGIN = 'origin',
  DESTINATION = 'destination',
  BUDGET = 'budget',
  ESTIMATED_DATE = 'estimated_date',
  REQUIREMENTS = 'requirements'
}

// Define credential types that need verification
export enum CredentialType {
  IDENTITY = 'identity',
  PILOT_LICENSE = 'pilot_license',
  MEDICAL_CERTIFICATE = 'medical_certificate',
  BACKGROUND_CHECK = 'background_check',
  FLIGHT_EXPERIENCE = 'flight_experience'
}

// Define verification requirement levels
export enum VerificationRequirement {
  REQUIRED = 'required',
  RECOMMENDED = 'recommended',
  OPTIONAL = 'optional'
}

// Define background check types
export enum BackgroundCheckType {
  CRIMINAL = 'criminal',
  DRIVING_RECORD = 'driving_record',
  EMPLOYMENT = 'employment',
  EDUCATION = 'education',
  COMPREHENSIVE = 'comprehensive'
}

// Define tracking status types
export enum TrackingStatus {
  NOT_STARTED = 'not_started',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  DELAYED = 'delayed',
  CANCELLED = 'cancelled'
}

// Define flight status types
export enum FlightStatus {
  SCHEDULED = 'scheduled',
  TAXIING = 'taxiing',
  DEPARTED = 'departed',
  EN_ROUTE = 'en_route',
  APPROACHING = 'approaching',
  LANDED = 'landed',
  DIVERTED = 'diverted',
  CANCELLED = 'cancelled'
}
