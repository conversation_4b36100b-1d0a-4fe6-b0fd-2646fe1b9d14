/* Import custom fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Poppins:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }
  body {
    @apply font-sans text-gray-200 bg-gray-900;
  }
  h1, h2, h3, h4, h5, h6 {
    @apply font-display font-semibold;
  }
  /* Darker h1 color for white backgrounds */
  .bg-white h1 {
    @apply text-gray-900;
  }
  /* Ensure input text is black on white backgrounds */
  .bg-white input, .bg-white select, .bg-white textarea {
    @apply text-gray-900;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 rounded-md font-medium transition-colors duration-200;
  }
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }
  .btn-secondary {
    @apply btn bg-gray-800 text-gray-200 border border-gray-700 hover:bg-gray-700 hover:text-primary-300 focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }
  .card {
    @apply bg-gray-800 rounded-lg shadow-card p-6 text-gray-100;
  }
  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}
