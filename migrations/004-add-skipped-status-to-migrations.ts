import { PoolClient } from 'pg';

export async function up(client: PoolClient): Promise<void> {
  // Add skipped column to migrations table
  await client.query(`
    ALTER TABLE migrations
    ADD COLUMN skipped BOOLEAN NOT NULL DEFAULT FALSE
  `);
}

export async function down(client: PoolClient): Promise<void> {
  // Remove skipped column from migrations table
  await client.query(`
    ALTER TABLE migrations
    DROP COLUMN skipped
  `);
}