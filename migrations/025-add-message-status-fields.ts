import { PoolClient } from 'pg';

// Migration to add message status fields
export async function up(client: PoolClient): Promise<void> {
  // Add is_new field to messages table
  await client.query(`
    ALTER TABLE messages
    ADD COLUMN is_new BOOLEAN DEFAULT TRUE
  `);

  // Add is_deleted field to messages table
  await client.query(`
    ALTER TABLE messages
    ADD COLUMN is_deleted BOOLEAN DEFAULT FALSE
  `);

  // Update existing messages to set is_new based on is_read
  await client.query(`
    UPDATE messages
    SET is_new = NOT is_read
  `);

  console.log('Message status fields migration completed');
}

export async function down(client: PoolClient): Promise<void> {
  // Remove the added columns
  await client.query(`
    ALTER TABLE messages
    DROP COLUMN IF EXISTS is_new,
    DROP COLUMN IF EXISTS is_deleted
  `);
  
  console.log('Message status fields migration reverted');
}