-- Add OCR fields to documents table
ALTER TABLE documents
ADD COLUMN ocr_data JSONB,
ADD COLUMN ocr_confidence FLOAT,
ADD COLUMN ocr_processed_at TIMESTAMP;

-- Create document_capture_urls table for short URLs
CREATE TABLE IF NOT EXISTS document_capture_urls (
  id SERIAL PRIMARY KEY,
  short_code VARCHAR(10) NOT NULL UNIQUE,
  user_id INTEGER REFERENCES users(id) NOT NULL,
  document_type VARCHAR(50) NOT NULL,
  created_at TIMESTAMP NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  used_at TIMESTAMP,
  ip_address VARCHAR(45),
  user_agent TEXT
);

-- Create index for faster lookups
CREATE INDEX idx_document_capture_urls_short_code ON document_capture_urls(short_code);
CREATE INDEX idx_document_capture_urls_user_id ON document_capture_urls(user_id);
CREATE INDEX idx_document_capture_urls_expires_at ON document_capture_urls(expires_at);

-- Add metadata field to documents table for additional data
ALTER TABLE documents
ADD COLUMN metadata JSONB;

-- Add mobile_capture field to documents table to track how the document was captured
ALTER TABLE documents
ADD COLUMN mobile_capture BOOLEAN DEFAULT FALSE;