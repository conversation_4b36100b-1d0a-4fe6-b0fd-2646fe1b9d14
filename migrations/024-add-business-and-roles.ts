import { PoolClient } from 'pg';

// Add business and role-based permissions
export async function up(client: PoolClient): Promise<void> {
  // Add account_type to users table
  await client.query(`
    ALTER TABLE users
    ADD COLUMN account_type TEXT NOT NULL DEFAULT 'individual',
    ADD COLUMN business_id INTEGER NULL,
    ADD COLUMN is_business_admin BOOLEAN DEFAULT FALSE
  `);

  // Create businesses table
  await client.query(`
    CREATE TABLE IF NOT EXISTS businesses (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      owner_id INTEGER REFERENCES users(id) NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create roles table
  await client.query(`
    CREATE TABLE IF NOT EXISTS roles (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL,
      business_id INTEGER REFERENCES businesses(id) NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create permissions table
  await client.query(`
    CREATE TABLE IF NOT EXISTS permissions (
      id SERIAL PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW()
    )
  `);

  // Create role_permissions table (many-to-many)
  await client.query(`
    CREATE TABLE IF NOT EXISTS role_permissions (
      role_id INTEGER REFERENCES roles(id) NOT NULL,
      permission_id INTEGER REFERENCES permissions(id) NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      PRIMARY KEY (role_id, permission_id)
    )
  `);

  // Create user_roles table (many-to-many)
  await client.query(`
    CREATE TABLE IF NOT EXISTS user_roles (
      user_id INTEGER REFERENCES users(id) NOT NULL,
      role_id INTEGER REFERENCES roles(id) NOT NULL,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      PRIMARY KEY (user_id, role_id)
    )
  `);

  // Add foreign key constraint to users.business_id
  await client.query(`
    ALTER TABLE users
    ADD CONSTRAINT fk_users_business
    FOREIGN KEY (business_id)
    REFERENCES businesses(id)
  `);

  // Insert default permissions
  await client.query(`
    INSERT INTO permissions (name, description) VALUES
    ('view_jobs', 'Can view jobs'),
    ('create_jobs', 'Can create new jobs'),
    ('edit_jobs', 'Can edit existing jobs'),
    ('delete_jobs', 'Can delete jobs'),
    ('approve_jobs', 'Can approve jobs for posting'),
    ('view_applications', 'Can view job applications'),
    ('create_applications', 'Can create job applications'),
    ('approve_applications', 'Can approve job applications'),
    ('manage_employees', 'Can manage employees'),
    ('manage_roles', 'Can manage roles and permissions'),
    ('view_finances', 'Can view financial information'),
    ('manage_finances', 'Can manage financial information'),
    ('inquire_bid', 'Can inquire to bid on a job'),
    ('approve_bid_inquiry', 'Can approve bid inquiries')
  `);

  // Add service_provider_type to users table
  await client.query(`
    ALTER TABLE users
    ADD COLUMN service_provider_type TEXT NULL
  `);

  // Create service_provider_addons table
  await client.query(`
    CREATE TABLE IF NOT EXISTS service_provider_addons (
      id SERIAL PRIMARY KEY,
      user_id INTEGER REFERENCES users(id) NOT NULL,
      addon_type TEXT NOT NULL,
      is_active BOOLEAN DEFAULT TRUE,
      expires_at TIMESTAMP,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create job_approval table
  await client.query(`
    CREATE TABLE IF NOT EXISTS job_approvals (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      requested_by INTEGER REFERENCES users(id) NOT NULL,
      approved_by INTEGER REFERENCES users(id),
      status TEXT NOT NULL DEFAULT 'pending',
      notes TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create bid_inquiries table
  await client.query(`
    CREATE TABLE IF NOT EXISTS bid_inquiries (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      pilot_id INTEGER REFERENCES users(id) NOT NULL,
      status TEXT NOT NULL DEFAULT 'pending',
      notes TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create messages table
  await client.query(`
    CREATE TABLE IF NOT EXISTS messages (
      id SERIAL PRIMARY KEY,
      sender_id INTEGER REFERENCES users(id) NOT NULL,
      recipient_id INTEGER REFERENCES users(id) NOT NULL,
      job_id INTEGER REFERENCES jobs(id),
      message TEXT NOT NULL,
      is_read BOOLEAN DEFAULT FALSE,
      message_type TEXT DEFAULT 'general',
      created_at TIMESTAMP NOT NULL DEFAULT NOW()
    )
  `);

  // Create expenses table if it doesn't exist
  await client.query(`
    CREATE TABLE IF NOT EXISTS expenses (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      user_id INTEGER REFERENCES users(id) NOT NULL,
      amount DECIMAL(10, 2) NOT NULL,
      description TEXT NOT NULL,
      receipt_url TEXT,
      status TEXT NOT NULL DEFAULT 'pending',
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  // Create disputes table if it doesn't exist
  await client.query(`
    CREATE TABLE IF NOT EXISTS disputes (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      initiated_by INTEGER REFERENCES users(id) NOT NULL,
      against INTEGER REFERENCES users(id) NOT NULL,
      reason TEXT NOT NULL,
      status TEXT NOT NULL DEFAULT 'open',
      resolution TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP
    )
  `);

  console.log('Business and role-based permissions migration completed');
}

export async function down(client: PoolClient): Promise<void> {
  // Drop tables in reverse order to avoid foreign key constraints
  await client.query('DROP TABLE IF EXISTS disputes');
  await client.query('DROP TABLE IF EXISTS expenses');
  await client.query('DROP TABLE IF EXISTS messages');
  await client.query('DROP TABLE IF EXISTS bid_inquiries');
  await client.query('DROP TABLE IF EXISTS job_approvals');
  await client.query('DROP TABLE IF EXISTS service_provider_addons');
  await client.query('DROP TABLE IF EXISTS user_roles');
  await client.query('DROP TABLE IF EXISTS role_permissions');
  await client.query('DROP TABLE IF EXISTS permissions');
  await client.query('DROP TABLE IF EXISTS roles');
  
  // Remove foreign key constraint before dropping businesses table
  await client.query('ALTER TABLE users DROP CONSTRAINT IF EXISTS fk_users_business');
  
  await client.query('DROP TABLE IF EXISTS businesses');
  
  // Remove columns from users table
  await client.query(`
    ALTER TABLE users
    DROP COLUMN IF EXISTS account_type,
    DROP COLUMN IF EXISTS business_id,
    DROP COLUMN IF EXISTS is_business_admin,
    DROP COLUMN IF EXISTS service_provider_type
  `);
  
  console.log('Business and role-based permissions migration reverted');
}