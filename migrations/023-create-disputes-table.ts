import { Pool } from 'pg';

export async function up(pool: Pool) {
  // Create disputes table
  await pool.query(`
    CREATE TABLE IF NOT EXISTS disputes (
      id SERIAL PRIMARY KEY,
      job_id INTEGER REFERENCES jobs(id) NOT NULL,
      escrow_account_id INTEGER REFERENCES escrow_accounts(id) NOT NULL,
      initiated_by_id INTEGER REFERENCES users(id) NOT NULL,
      reason TEXT NOT NULL,
      description TEXT,
      status TEXT NOT NULL DEFAULT 'open',
      admin_notes TEXT,
      resolution_details TEXT,
      created_at TIMESTAMP NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMP,
      resolved_at TIMESTAMP
    )
  `);

  // Add dispute_id column to transactions table
  await pool.query(`
    ALTER TABLE transactions
    ADD COLUMN IF NOT EXISTS dispute_id INTEGER REFERENCES disputes(id)
  `);

  // Add dispute_resolution_type column to transactions table
  await pool.query(`
    ALTER TABLE transactions
    ADD COLUMN IF NOT EXISTS dispute_resolution_type TEXT
  `);

  console.log('Created disputes table and updated transactions table');
}

export async function down(pool: Pool) {
  // Remove dispute_resolution_type column from transactions
  await pool.query(`
    ALTER TABLE transactions
    DROP COLUMN IF EXISTS dispute_resolution_type
  `);

  // Remove dispute_id column from transactions
  await pool.query(`
    ALTER TABLE transactions
    DROP COLUMN IF EXISTS dispute_id
  `);

  // Drop disputes table
  await pool.query(`
    DROP TABLE IF EXISTS disputes
  `);

  console.log('Dropped disputes table and removed columns from transactions table');
}