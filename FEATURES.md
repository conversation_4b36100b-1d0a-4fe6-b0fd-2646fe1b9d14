# FerryPros New Features Documentation

This document provides an overview of the new features implemented in the FerryPros application.

## Table of Contents

1. [Business Registration for Brokers](#business-registration-for-brokers)
2. [Employee Management](#employee-management)
3. [Role-Based Permissions](#role-based-permissions)
4. [Job Approval Workflow](#job-approval-workflow)
5. [Ferry Pilot Agencies](#ferry-pilot-agencies)
6. [Bidding Inquiry System](#bidding-inquiry-system)
7. [Messaging System](#messaging-system)
8. [Expense Tracking](#expense-tracking)
9. [Dispute Handling](#dispute-handling)
10. [Email Notifications](#email-notifications)

## Business Registration for Brokers

Brokers can now register as businesses, which allows them to add employees to their account.

### Key Features

- Brokers can create a business profile
- Business owners automatically become business administrators
- Default roles are created for each business

### Usage

```typescript
import { createBusiness } from '~/utils/business.server';

// Create a new business
const business = await createBusiness(
  ownerId,
  'Business Name',
  'Business Description'
);
```

## Employee Management

Business owners and administrators can add employees to their business.

### Key Features

- Add employees to a business
- Manage employee roles and permissions
- Remove employees from a business

### Usage

```typescript
import { addEmployeeToBusiness, removeEmployeeFromBusiness } from '~/utils/business.server';

// Add an employee to a business
const employee = await addEmployeeToBusiness(
  businessId,
  '<EMAIL>',
  'Employee Name',
  'password',
  false // isAdmin
);

// Remove an employee from a business
const removed = await removeEmployeeFromBusiness(employeeId);
```

## Role-Based Permissions

Businesses can define roles with specific permissions for their employees.

### Key Features

- Create custom roles with specific permissions
- Assign roles to employees
- Check if a user has a specific permission

### Default Roles

- **Admin**: Has all permissions
- **Manager**: Can manage jobs, employees, and finances
- **Employee**: Can view and create jobs

### Usage

```typescript
import { createRole, assignRoleToUser, userHasPermission } from '~/utils/business.server';

// Create a new role
const role = await createRole(
  businessId,
  'Job Manager',
  [1, 2, 3, 5] // Permission IDs
);

// Assign a role to a user
const assigned = await assignRoleToUser(userId, roleId);

// Check if a user has a specific permission
const canApproveJobs = await userHasPermission(userId, 'approve_jobs');
```

## Job Approval Workflow

Jobs created by employees can require approval before being posted.

### Key Features

- Request approval for a job
- Approve or reject job approval requests
- Notifications for approval requests and decisions

### Usage

```typescript
import { requestJobApproval, processJobApproval } from '~/utils/jobApproval.server';

// Request approval for a job
const approval = await requestJobApproval(
  jobId,
  requestedBy,
  'Please approve this job'
);

// Process a job approval request
const processed = await processJobApproval(
  approvalId,
  approvedBy,
  true, // approved
  'Looks good, approved!'
);
```

## Ferry Pilot Agencies

Companies that represent multiple pilots can register as Ferry Pilot Agencies.

### Key Features

- Register as a service provider
- Add employees/pilots to the service provider account
- Purchase premium addons for boosted visibility

### Usage

```typescript
import { registerServiceProvider, addEmployeeToServiceProvider } from '~/utils/serviceProvider.server';
import { addServiceProviderAddon } from '~/utils/serviceProvider.server';

// Register as a service provider
const serviceProvider = await registerServiceProvider(
  userId,
  'Aircraft Ferry Service'
);

// Add an employee to a service provider
const employee = await addEmployeeToServiceProvider(
  serviceProviderId,
  '<EMAIL>',
  'Pilot Name',
  'password',
  false // isAdmin
);

// Add a premium addon
const addon = await addServiceProviderAddon(
  serviceProviderId,
  'boosted',
  new Date('2023-12-31') // expiresAt
);
```

## Bidding Inquiry System

Pilots must request permission to bid on jobs, which can be approved by the job owner or authorized employees.

### Key Features

- Create bid inquiries
- Approve or reject bid inquiries
- Notifications for inquiries and decisions

### Usage

```typescript
import { createBidInquiry, processBidInquiry, canPilotBidOnJob } from '~/utils/bidInquiry.server';

// Create a bid inquiry
const inquiry = await createBidInquiry(
  jobId,
  pilotId,
  'I would like to bid on this job'
);

// Process a bid inquiry
const processed = await processBidInquiry(
  inquiryId,
  userId,
  true, // approved
  'You are approved to bid'
);

// Check if a pilot can bid on a job
const canBid = await canPilotBidOnJob(jobId, pilotId);
```

## Messaging System

Users can send messages to each other regarding jobs, including dispute messages.

### Key Features

- Send messages between users
- View message threads
- Special message types for disputes and job updates
- Mark messages as read

### Usage

```typescript
import { sendMessage, getMessagesBetweenUsers, getMessageThreadsForUser } from '~/utils/messaging.server';

// Send a message
const message = await sendMessage(
  senderId,
  recipientId,
  'Hello, I have a question about the job',
  jobId,
  'general' // messageType
);

// Get messages between users
const messages = await getMessagesBetweenUsers(
  userId,
  otherUserId,
  jobId // optional
);

// Get message threads for a user
const threads = await getMessageThreadsForUser(userId);
```

## Expense Tracking

Pilots can submit expenses with receipt uploads for jobs.

### Key Features

- Create expenses with descriptions
- Upload receipts for expenses
- Approve or reject expenses
- Track total expenses for a job

### Usage

```typescript
import { createExpense, uploadReceiptForExpense, updateExpenseStatus } from '~/utils/expenseTracking.server';

// Create an expense
const expense = await createExpense(
  jobId,
  userId,
  150.75, // amount
  'Fuel expense'
);

// Upload a receipt
const receipt = await uploadReceiptForExpense(
  expenseId,
  'https://example.com/receipt.jpg',
  userId
);

// Update expense status
const updated = await updateExpenseStatus(
  expenseId,
  'approved',
  approvedBy
);
```

## Dispute Handling

Users can initiate disputes for jobs, which creates a special message thread for resolution.

### Key Features

- Start dispute conversations
- Resolve disputes
- View disputes for a user
- Special message type for disputes

### Usage

```typescript
import { startDisputeConversation, resolveDispute, getDisputesForUser } from '~/utils/messaging.server';

// Start a dispute
const dispute = await startDisputeConversation(
  initiatorId,
  againstId,
  jobId,
  'There is an issue with payment'
);

// Resolve a dispute
const resolved = await resolveDispute(
  disputeId,
  'The payment has been processed',
  resolvedBy
);

// Get disputes for a user
const disputes = await getDisputesForUser(
  userId,
  'open' // status (optional)
);
```

## Email Notifications

Email notifications are sent for various events in the system.

### Key Features

- Welcome emails for new users
- Verification emails
- Job-related notifications
- Message notifications
- Expense and dispute notifications

### Usage

```typescript
import { sendWelcomeEmail, sendVerificationEmail } from '~/utils/emailNotifications.server';

// Send a welcome email
const sent = await sendWelcomeEmail(
  '<EMAIL>',
  'User Name',
  'pilot'
);

// Send a verification email
const sent = await sendVerificationEmail(
  '<EMAIL>',
  'User Name',
  'verification-token'
);
```

## Database Schema Changes

The following tables were added to support the new features:

- `businesses`: Stores business information
- `roles`: Defines roles for businesses
- `permissions`: Defines available permissions
- `role_permissions`: Maps roles to permissions
- `user_roles`: Maps users to roles
- `service_provider_addons`: Stores premium addons for service providers
- `job_approvals`: Tracks job approval requests
- `bid_inquiries`: Tracks bid inquiry requests
- `messages`: Stores messages between users
- `expenses`: Tracks expenses for jobs
- `disputes`: Tracks disputes between users

The `users` table was extended with the following columns:

- `account_type`: Type of account ('individual', 'business', 'business_employee', 'service_provider')
- `business_id`: Reference to the business the user belongs to
- `is_business_admin`: Flag indicating if the user is a business admin
- `service_provider_type`: Type of service provider
